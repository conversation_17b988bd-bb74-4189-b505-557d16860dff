name: Build & Deploy atwork-mweb-frontend
run-name: Deploying atwork-mweb-frontend ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment"
        required: true
        default: "sandbox"
        type: choice
        options:
          - sandbox
          - production

jobs:
  deploy:
    name: atwork-mweb-frontend-${{ inputs.environment }}
    uses: YouGotaGift/devops-organization-actions/.github/workflows/me-central-1-frontend-application-build-and-deploy.yaml@main
    with:
      application_name: atwork-mweb-frontend
      environment: ${{ inputs.environment }}
      app_image_repository: atwork-mweb
    secrets: inherit
