namespace: "atwork-mweb-frontend"

app_name: "atwork-mweb-frontend"
environment: "production"

service:
  name: "atwork-mweb-frontend-nginx"
  default:
    port: 80
    protocol: "TCP"
    targetPort: 80
  https:
    port: 443
    protocol: "TCP"
    targetPort: 80

hpa:
  name: "atwork-mweb-frontend-nginx-hpa"
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

pdb:
  name: "atwork-mweb-frontend-nginx-pdb"
  minAvailable: 50%

deployment:
  name: "atwork-mweb-frontend-nginx-deployment"
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%

  containers:
    default:
      name: "nginx"
      imagePullPolicy: "Always"
      image: "459037613883.dkr.ecr.me-central-1.amazonaws.com/production/ygg/atwork-mweb/frontend-nginx:[BUILD_TAG]"
      port: 80
      memory:
        requests: 32Mi
        limits: 64Mi
      cpu:
        requests: 10m
        limits: 100m
      health:
        path: "/nginx-health"
        port: 80
        scheme: "HTTP"
      startupProbe:
        initialDelaySeconds: 3
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 5
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 4

  volumes:
    local:
      - name: "ygg-atwork-mweb-frontend-production-nginx-logs"
        mountPath: "/var/log/nginx/"
        hostPath: "/home/<USER>/ygag-logs/ygg-atwork-mweb-frontend-production/nginx"

  nodeSelector:
    key: "karpenter.sh/nodepool"
    value: "default"

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: "topology.kubernetes.io/zone"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend"
          tier: nginx
    - maxSkew: 1
      topologyKey: "kubernetes.io/hostname"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend"
          tier: nginx

  priorityClassName: "production-medium"
  terminationGracePeriodSeconds: 100
