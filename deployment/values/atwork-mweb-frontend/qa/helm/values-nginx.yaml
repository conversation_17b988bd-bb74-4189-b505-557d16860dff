namespace: "atwork-mweb-frontend-[JIRA_ID]"

app_name: "atwork-mweb-frontend-[JIRA_ID]"
environment: "qa"

service:
  name: "atwork-mweb-frontend-[JIRA_ID]-nginx"
  default:
    port: 80
    protocol: "TCP"
    targetPort: 80
  https:
    port: 443
    protocol: "TCP"
    targetPort: 80

enableHPA: false
hpa:
  name: "atwork-mweb-frontend-[JIRA_ID]-nginx-hpa"
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80

enableKedaCron: true
keda:
  name: "atwork-mweb-frontend-[JIRA_ID]-nginx-keda"
  minReplicas: 0
  desiredReplicas: 2
  maxReplicas: 2
  start: "0 7 * * 1-5"
  end: "0 22 * * 1-5"
  cpu: 80

pdb:
  name: "atwork-mweb-frontend-[JIRA_ID]-nginx-pdb"
  minAvailable: 50%

deployment:
  name: "atwork-mweb-frontend-[JIRA_ID]-nginx-deployment"
  replicas: 2
  maxSurge: 100%
  maxUnavailable: 50%

  containers:
    default:
      name: "nginx"
      imagePullPolicy: "IfNotPresent"
      image: "420360167813.dkr.ecr.me-central-1.amazonaws.com/qa/ygg/atwork-mweb/frontend-nginx:[BUILD_TAG]"
      command: '["sh", "-c", "sed -i \"s/\\[JIRA_ID\\]/[JIRA_ID]/g\" /ygag/nginx/conf/nginx.conf ; sh /ygag/nginx/entrypoint.sh "]'
      port: 80
      memory:
        requests: 32Mi
        limits: 64Mi
      cpu:
        requests: 10m
        limits: 100m
      health:
        path: "/nginx-health"
        port: 80
        scheme: "HTTP"
      startupProbe:
        initialDelaySeconds: 3
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 5
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 4

  volumes:
    local:
      - name: "ygag-atwork-mweb-frontend-[JIRA_ID]-qa-nginx-logs"
        mountPath: "/var/log/nginx/"
        hostPath: "/home/<USER>/ygag-logs/ygag-atwork-mweb-frontend-[JIRA_ID]-qa/nginx"

  initContainers:
    app:
      - image: "420360167813.dkr.ecr.me-central-1.amazonaws.com/qa/k8s/busybox:latest"
        host: "atwork-mweb-frontend-[JIRA_ID]-app"
        name: "init-app"
        port: 3000
        healthPath: "/atwork/health/"

  nodeSelector:
    key: "karpenter.sh/nodepool"
    value: "default"

  topologySpreadConstraints:
    - maxSkew: 2
      topologyKey: "topology.kubernetes.io/zone"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend-[JIRA_ID]"
          tier: nginx
    - maxSkew: 2
      topologyKey: "kubernetes.io/hostname"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend-[JIRA_ID]"
          tier: nginx

  priorityClassName: "qa-medium"
  terminationGracePeriodSeconds: 100
