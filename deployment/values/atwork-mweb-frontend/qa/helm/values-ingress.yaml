ingress:
  apiVersion: "networking.k8s.io/v1"

  namespace: "atwork-mweb-frontend-[JIRA_ID]"
  name: "atwork-mweb-frontend-[JIRA_ID]-ingress"

  annotations:
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/backend-protocol: "HTTP"
    alb.ingress.kubernetes.io/certificate-arn: "[CERTIFICATE_ARN]"
    alb.ingress.kubernetes.io/group.name: "default"
    alb.ingress.kubernetes.io/healthcheck-path: "/atwork/health/"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/tags: "Name=ygg-qa-default-alb-eks-tf, Platform=EKS, Environment=qa"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "[WAF_ARN]"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/subnets: "ygg-qa-public-me-central-1a, ygg-qa-public-me-central-1b, ygg-qa-public-me-central-1c"
    alb.ingress.kubernetes.io/load-balancer-attributes: "idle_timeout.timeout_seconds=120"
    kubernetes.io/ingress.class: "alb"

  rules:
    - host: "atwork-mweb-frontend-[JIRA_ID].sit.yougotagift.co"
      http:
        paths:
          - backend:
              service:
                name: "atwork-mweb-frontend-[JIRA_ID]-nginx"
                port:
                  number: 80
            path: "/*"
            pathType: "ImplementationSpecific"
