namespace: "atwork-mweb-frontend"

app_name: "atwork-mweb-frontend"
environment: "sandbox"

service:
  name: "atwork-mweb-frontend-app"
  default:
    port: 3000
    protocol: "TCP"
    targetPort: 3000

hpa:
  name: "atwork-mweb-frontend-app-hpa"
  minReplicas: 3
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

pdb:
  name: "atwork-mweb-frontend-app-pdb"
  minAvailable: 50%

deployment:
  name: "atwork-mweb-frontend-app-deployment"
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%
  serviceAccountName: "ygg-atwork-mweb-frontend-vault"

  containers:
    default:
      name: "app"
      imagePullPolicy: "Always"
      image: "************.dkr.ecr.me-central-1.amazonaws.com/production/ygg/atwork-mweb/frontend-app:[BUILD_TAG]"
      command: '["sh", "-c" , "source /vault/secrets/application.env; node server.js"]'
      port: 3000
      memory:
        requests: 400Mi
        limits: 500Mi
      cpu:
        requests: 100m
      health:
        path: "/atwork/health/"
        port: 3000
        scheme: "HTTP"
      startupProbe:
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 7
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 4
      volumeMounts:
        - mountPath: "/ygag/logs/"
          name: "ygg-atwork-mweb-frontend-sandbox-app-logs"
        - name: "atwork-mweb-frontend-app-env-volume"
          mountPath: "/vault/secrets"

  volumes:
    - name: "ygg-atwork-mweb-frontend-sandbox-app-logs"
      hostPath:
        path: "/home/<USER>/ygag-logs/ygg-atwork-mweb-frontend-sandbox/app"
    - name: "atwork-mweb-frontend-app-env-volume"
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "atwork-mweb-frontend-envs"

  nodeSelector:
    key: "karpenter.sh/nodepool"
    value: "default"

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: "topology.kubernetes.io/zone"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend"
          tier: app
    - maxSkew: 1
      topologyKey: "kubernetes.io/hostname"
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: "atwork-mweb-frontend"
          tier: app

  priorityClassName: "sandbox-medium"
  terminationGracePeriodSeconds: 60
