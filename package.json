{"name": "next-boilerplate", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:local": "PORT=3000 node ./server-local.js", "lint": "next lint", "stylelint": "npx stylelint 'src/**/*.{css,scss}'", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci", "test:coverage": "jest --coverage", "check-types": "tsc --pretty --noEmit", "check-lint": "eslint . --ext ts --ext tsx --ext js", "prepare": "husky install"}, "dependencies": {"@apollo/client": "^3.13.8", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.1", "@giphy/js-fetch-api": "^5.7.0", "@giphy/react-components": "^10.1.0", "@intercom/messenger-js-sdk": "^0.0.14", "@mui/icons-material": "^5.14.21", "@mui/lab": "^5.0.0-alpha.125", "@mui/material": "^5.14.21", "@mui/x-date-pickers": "^6.17.0", "@opentelemetry/core": "^2.0.1", "@reduxjs/toolkit": "^2.8.2", "@sentry/nextjs": "^9.41.0", "@tweenjs/tween.js": "^25.0.0", "@yougotagift/personalization_preview_package_frontend": "^1.1.5", "apollo-link-logger": "^2.0.1", "aws-amplify": "^6.15.4", "clevertap-web-sdk": "^2.0.0", "cookies-next": "^2.1.0", "cors": "^2.8.5", "d3-shape": "^3.2.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "framer-motion": "^12.23.9", "graphql": "^16.11.0", "html-to-image": "^1.11.13", "i18next-resources-to-backend": "^1.2.1", "install": "^0.13.0", "isomorphic-dompurify": "^2.26.0", "isomorphic-fetch": "^3.0.0", "js-sha256": "^0.11.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "luxon": "^3.7.1", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "next": "^14.2.4", "next-i18n-router": "^5.5.3", "next-i18next": "^15.4.2", "next-redux-cookie-wrapper": "^2.2.1", "next-redux-wrapper": "^8.1.0", "npm": "^11.5.1", "photoeditorsdk": "5.20.0", "react": "^18.2.0", "react-awesome-reveal": "^4.3.1", "react-circle-flags": "^0.0.23", "react-color": "^2.19.3", "react-countdown": "^2.3.6", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-flags-select": "^2.5.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.61.1", "react-image-crop": "^11.0.10", "react-infinite-scroll-component": "^6.1.0", "react-lazyload": "^3.2.1", "react-marquee-slider": "^1.1.5", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.18", "react-redux": "^9.2.0", "react-zendesk": "^0.1.13", "redux-thunk": "^3.1.0", "sass": "^1.89.2", "sharp": "^0.34.3", "simplebar-react": "^3.3.2", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "swiper": "^11.2.10", "uuid": "^11.1.0"}, "devDependencies": {"@apollo/react-testing": "^4.0.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/clevertap-web-sdk": "^1.1.13", "@types/cors": "^2.8.19", "@types/lodash": "^4.17.20", "@types/luxon": "^3.6.2", "@types/node": "24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-google-recaptcha": "^2.1.9", "@types/react-lazyload": "^3.2.3", "@types/stylis": "^4.2.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "9.31.0", "eslint-config-next": "15.4.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "postcss": "^8.5.6", "prettier": "3.6.2", "react-test-renderer": "^18.2.0", "stylelint": "^14.16.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-prettier-scss": "0.0.1", "stylelint-config-standard": "^24.0.0", "tailwindcss": "^4.1.11", "typescript": "5.8.3"}}