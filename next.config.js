/** @type {import('next').NextConfig} */
const { withSentryConfig } = require("@sentry/nextjs");
const isProd = process.env.NODE_ENV === "production";
const basePath = "/atwork";

const GROUP_GIFT_APP_URL = "https://app.groupgift.yougotagift.com/";
const GROUP_GIFT_APP_LEGACY_URL = "https://groupgift-frontend-egg-do-ae.sit.yougotagift.co/";

// You can choose which headers to add to the list
const securityHeaders = [
  {
    key: "X-DNS-Prefetch-Control",
    value: "on",
  },
  {
    key: "X-XSS-Protection",
    value: "1; mode=block",
  },
  {
    key: "X-Frame-Options",
    value: "SAMEORIGIN",
  },
  {
    key: "Content-Security-Policy",
    value: `frame-ancestors 'self' ${
      process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GROUP_GIFT_APP_URL
        : process.env.NEXT_PUBLIC_GROUP_GIFT_APP_URL
    };`,
  },
  {
    key: "Content-Security-Policy",
    value: `frame-ancestors 'self' ${
      process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GROUP_GIFT_APP_LEGACY_URL
        : process.env.NEXT_PUBLIC_GROUP_GIFT_APP_LEGACY_URL
    };`,
  },
];

const moduleExports = {
  basePath: basePath,
  assetPrefix: isProd ? process.env.NEXT_PUBLIC_ECOM_ASSET_PREFIX : basePath,
  async headers() {
    return [
      {
        // Apply these headers to all routes in your application.
        source: "/:path*",
        headers: securityHeaders,
      },
    ];
  },
  output: 'standalone',
  experimental: {
    outputStandalone: true,
    esmExternals: true,
  },
  reactStrictMode: true,
  trailingSlash: true,
  optimizeFonts: true,
  webpack(config) {
    config.module.rules.push({
      test: /\.(woff|woff2|eot|ttf|svg)$/,
      use: [
        {
          loader: "url-loader",
          options: {
            limit: 100000,
          },
        },
      ],
    });
    return config;
  },
  webpackDevMiddleware: (config) => {
    config.watchOptions = {
      poll: 1000,
      aggregateTimeout: 300,
    };
    return config;
  },
  images: {
    minimumCacheTTL: 300,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "**.sandbox.yougotagift.com",
      },
      {
        protocol: "https",
        hostname: "**.yougotagift.com",
      },
      {
        protocol: "https",
        hostname: "**.cameratag.com",
      },
      {
        protocol: "https",
        hostname: "**.giphy.com",
      },
    ],
    path: `${basePath}/_next/image`,
  },
  sentry: {
    widenClientFileUpload: true,
    hideSourceMaps: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

const sentryWebpackPluginOptions = {
  url: process.env.SENTRY_URL,
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,
  silent: false, // Suppresses all logs
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options.
};

// Make sure adding Sentry options is the last code to run before exporting, to
// ensure that your source maps include changes from all other Webpack plugins
module.exports = withSentryConfig(moduleExports, sentryWebpackPluginOptions);
