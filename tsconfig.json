{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": "src",
    "paths": {
      "@styles/*": [
        "styles/*"
      ],
      "@layout/*": [
        "layout/*"
      ],
      "@common/*": [
        "common/*"
      ],
      "@features/*": [
        "features/*"
      ],
      "@redux/*": [
        "redux/*"
      ],
      "@utils/*": [
        "utils/*"
      ],
      "@constants/*": [
        "constants/*"
      ],
      "@graphql/*": [
        "graphql/*"
      ],
      "@mock-data/*": [
        "__mockData__/*"
      ],
      "@interfaces/*": [
        "interfaces/*"
      ],
    },
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.js", "src/features/common/i18n/TranslationsProvider.js"],
  "exclude": ["node_modules"]
}
