<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="24" height="24" fill="#1E1E1E"/>
<path d="M-13283 -3809C-13283 -3810.1 -13282.1 -3811 -13281 -3811H17393C17394.1 -3811 17395 -3810.1 17395 -3809V37833C17395 37834.1 17394.1 37835 17393 37835H-13281C-13282.1 37835 -13283 37834.1 -13283 37833V-3809Z" fill="#444444"/>
<path d="M-13281 -3810H17393V-3812H-13281V-3810ZM17394 -3809V37833H17396V-3809H17394ZM17393 37834H-13281V37836H17393V37834ZM-13282 37833V-3809H-13284V37833H-13282ZM-13281 37834C-13281.6 37834 -13282 37833.6 -13282 37833H-13284C-13284 37834.7 -13282.7 37836 -13281 37836V37834ZM17394 37833C17394 37833.6 17393.6 37834 17393 37834V37836C17394.7 37836 17396 37834.7 17396 37833H17394ZM17393 -3810C17393.6 -3810 17394 -3809.55 17394 -3809H17396C17396 -3810.66 17394.7 -3812 17393 -3812V-3810ZM-13281 -3812C-13282.7 -3812 -13284 -3810.66 -13284 -3809H-13282C-13282 -3809.55 -13281.6 -3810 -13281 -3810V-3812Z" fill="white" fill-opacity="0.1"/>
<path d="M-13183 -3709C-13183 -3710.1 -13182.1 -3711 -13181 -3711H2585C2586.1 -3711 2587 -3710.1 2587 -3709V10140C2587 10141.1 2586.1 10142 2585 10142H-13181C-13182.1 10142 -13183 10141.1 -13183 10140V-3709Z" fill="#0071FF"/>
<path d="M-13181 -3710H2585V-3712H-13181V-3710ZM2586 -3709V10140H2588V-3709H2586ZM2585 10141H-13181V10143H2585V10141ZM-13182 10140V-3709H-13184V10140H-13182ZM-13181 10141C-13181.6 10141 -13182 10140.6 -13182 10140H-13184C-13184 10141.7 -13182.7 10143 -13181 10143V10141ZM2586 10140C2586 10140.6 2585.55 10141 2585 10141V10143C2586.66 10143 2588 10141.7 2588 10140H2586ZM2585 -3710C2585.55 -3710 2586 -3709.55 2586 -3709H2588C2588 -3710.66 2586.66 -3712 2585 -3712V-3710ZM-13181 -3712C-13182.7 -3712 -13184 -3710.66 -13184 -3709H-13182C-13182 -3709.55 -13181.6 -3710 -13181 -3710V-3712Z" fill="white" fill-opacity="0.1"/>
<rect width="1512" height="1095" transform="translate(-601 -141)" fill="#99C6FF"/>
<rect x="-569" y="-8" width="1448" height="932" rx="24" fill="white"/>
<mask id="mask0_7167_86109" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-569" y="-8" width="1448" height="932">
<rect x="-569" y="-8" width="1448" height="932" rx="24" fill="white"/>
</mask>
<g mask="url(#mask0_7167_86109)">
</g>
<g filter="url(#filter0_d_7167_86109)">
<rect x="-20" y="-18" width="350" height="60" rx="12" fill="#0E0F0C"/>
<rect x="-4" y="-4" width="32" height="32" rx="16" fill="#E74848" fill-opacity="0.1"/>
<path d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM15.36 14.3C15.65 14.59 15.65 15.07 15.36 15.36C15.21 15.51 15.02 15.58 14.83 15.58C14.64 15.58 14.45 15.51 14.3 15.36L12 13.06L9.7 15.36C9.55 15.51 9.36 15.58 9.17 15.58C8.98 15.58 8.79 15.51 8.64 15.36C8.35 15.07 8.35 14.59 8.64 14.3L10.94 12L8.64 9.7C8.35 9.41 8.35 8.93 8.64 8.64C8.93 8.35 9.41 8.35 9.7 8.64L12 10.94L14.3 8.64C14.59 8.35 15.07 8.35 15.36 8.64C15.65 8.93 15.65 9.41 15.36 9.7L13.06 12L15.36 14.3Z" fill="#E74848"/>
</g>
<defs>
<filter id="filter0_d_7167_86109" x="-32" y="-18" width="374" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="8" operator="erode" in="SourceAlpha" result="effect1_dropShadow_7167_86109"/>
<feOffset dy="16"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.0196078 0 0 0 0 0.0705882 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7167_86109"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7167_86109" result="shape"/>
</filter>
</defs>
</svg>
