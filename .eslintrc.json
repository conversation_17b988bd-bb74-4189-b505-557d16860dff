{
  "extends": [
    "next/core-web-vitals",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "no-unused-vars": "off",
    "no-constant-condition": "off",  // Disable constant condition warnings
    "no-dupe-keys": "off",  // Disable duplicate key warnings if necessary
    "no-useless-escape": "off",  // Disable useless escape warnings
    "no-unsafe-optional-chaining": "off",
    "no-fallthrough": "off",
    "no-undef": "off",
    "no-undef-init": "off",
    "react-hooks/rules-of-hooks": "off",
    "react/jsx-key": "off",
    "react/no-unescaped-entities": "off",
    "react/jsx-no-target-blank": "off",
    "react/no-unknown-property": "off",
    "react/no-children-prop": "off",
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "no-console": "off",
    "no-unneeded-ternary": "off",
    "@typescript-eslint/no-unsafe-any": "off",
    "@typescript-eslint/no-unsafe-argument": "off",
    "no-unused-expressions": "off"
  }
}
