"use client";
import { ReactNode, use, useEffect } from "react";
import { ApolloProvider } from "@apollo/client";
import { Provider as ReduxProvider } from "react-redux";
import ThemeRegistry from "@features/common/themeRegistry/ThemeRegistry";
import ChatbotTrigger from "@features/common/chatbotTrigger/Chatbottrigger";
import { AppContextProvider } from "@features/common/common.context";
import AppDataDispatcher from "@redux/AppDataDispatcher";
import StoreProvider from "@redux/StoreProvider";
import { useApollo } from "@graphql/useApolloClient";
// import CheckCookie from "@features/common/checkCookie/CheckCookie";

export default function AppProviders({
  children,
  globalAppData,
  locale,
}: {
  children: ReactNode;
  globalAppData: any;
  locale: string;
}) {
  // Memoize userTokens and ip payload to avoid unnecessary re-renders
  const userTokens = {
    AccessToken: globalAppData?.accessToken,
    IdToken: globalAppData?.idToken,
    RefreshToken: globalAppData?.refreshToken,
    isUserSignedIn: globalAppData?.isUserSignedIn,
    userAttributes: globalAppData?.userAttributes,
    isGuestEnabled: globalAppData?.isGuestEnabled,
  };
  const ipPayload = globalAppData?.ip ? { ip: globalAppData?.ip } : null;

  const apolloClient = useApollo(globalAppData, locale);

  return (
    <AppContextProvider
      cameraTagAppId={globalAppData?.secureConfigs?.cameraTagAppId}
      imglyLicense={globalAppData?.secureConfigs?.imglyLicense}
      globalAppData={globalAppData}
    >
      <StoreProvider>
        <AppDataDispatcher userTokens={userTokens} ipPayload={ipPayload} />
        <ThemeRegistry>
          <ApolloProvider client={apolloClient}>
            <ChatbotTrigger />
            {/* <CheckCookie /> */}
            {children}
          </ApolloProvider>
        </ThemeRegistry>
      </StoreProvider>
    </AppContextProvider>
  );
}
