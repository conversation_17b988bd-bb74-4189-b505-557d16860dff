import { ReactNode } from "react";
import { cookies, headers } from "next/headers";
import { getGlobalAppData } from "@utils/getGlobalAppData";
import AppProviders from "app/AppProviders";
import TranslationsProvider from "@features/common/i18n/TranslationsProvider";
import initTranslations from "app/i18n";
import i18nConfig from "../../../i18nConfig";
import { dir } from "i18next";
import { imageBaseUrl } from "@constants/envVariables";
import {
  blakaHollow,
  bricolageGrotesque,
  cairo,
  elMessiri,
  monaSans,
  notoKufiArabic,
  poppins,
  reemKufi,
  tajawal,
} from "app/font";
import "../../styles/globals.scss";
import "../../styles/_personalisation.scss";
import "swiper/css";
import PreloadImages from "@features/common/preloadImages/PreloadImages";
import CameraTag from "@features/common/cameraTag/CameraTag";

const i18nNamespaces = ["common"];

export function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({ locale }));
}

export default async function RootLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: { locale: string };
}) {
  // #. Get google tag manager id
  const GTM_ID = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID;
  // #. Get hotjar NEXT_PUBLIC_HOTJAR_IDid
  const HOTJAR_ID = process.env.NEXT_PUBLIC_HOTJAR_ID;
  const reqHeadersObj = Object.fromEntries(headers().entries());
  reqHeadersObj["x-forwarded-for"] =
    reqHeadersObj["x-forwarded-for"] || "127.0.0.1";
  const reqCookies = cookies();

  // Try to extract the pathname from headers (works in most deployments)
  const pathname =
    reqHeadersObj["x-next-url"] ||
    reqHeadersObj["referer"]?.replace(/^https?:\/\/[^/]+/, "") ||
    "/";

  // Match locale in the first segment: /en-ae/ or /ar-ae/
  const localeMatch = pathname.match(
    /^\/([a-z]{2}(?:-[a-z]{2})?)(?:\/|$)/i,
  );
  const locale = params.locale || "en-ae";
  const supportedLocales = ["en", "en-ae", "ar-ae"];
  const defaultLocale = "en";

  const context = {
    res: {},
    req: { headers: reqHeadersObj, cookies: reqCookies },
    locale,
    locales: supportedLocales,
    defaultLocale,
    resolvedUrl: pathname,
  };
  const globalAppData = (await getGlobalAppData(context)) as any;
  const lang = locale.split("-")[0] || "en";
  const { resources } = await initTranslations(lang, i18nNamespaces);
  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={lang}
      resources={resources}
    >
      <html
        lang={lang || "en"}
        dir={dir(lang)}
        className={`${cairo.variable} ${poppins.variable} ${tajawal.variable} ${elMessiri.variable} ${reemKufi.variable} ${blakaHollow.variable} ${bricolageGrotesque.variable} ${notoKufiArabic.variable} ${monaSans.variable}`}
      >
        <head>
          {GTM_ID && (
            <script
              dangerouslySetInnerHTML={{
                __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${GTM_ID}');`,
              }}
            />
          )}

          {HOTJAR_ID && (
            <script
              dangerouslySetInnerHTML={{
                __html: `(function(h,o,t,j,a,r){
                            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                            h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
                            a=o.getElementsByTagName('head')[0];
                            r=o.createElement('script');r.async=1;
                            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                            a.appendChild(r);
                        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,
              }}
            />
          )}
          <PreloadImages />
          <CameraTag />
          <link rel="icon" href={`${imageBaseUrl}/favIcon.png`} />
        </head>
        <body>
          <AppProviders globalAppData={globalAppData} locale={lang}>
            {children}
          </AppProviders>
        </body>
      </html>
    </TranslationsProvider>
  );
}
