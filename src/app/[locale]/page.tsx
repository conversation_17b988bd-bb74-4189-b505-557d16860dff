"use client";
import useApp from "@features/common/common.context";
// import { getTokenInfo } from "@features/common/commonSlice";
import CommonLayout from "@layout/common";
import LoginPage from "@features/animateRoute/login/LoginPage";
// import { useAppSelector } from "@redux/hooks";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/navigation";
import React, { use, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { motion } from "framer-motion";
import { useFirstTimeUserComponentList } from "@features/componentsToAnimate";
import { COMPONENT_ID } from "@constants/common";
import { WorkComponentProps } from "@interfaces/common.inteface";
import InfoSection from "@features/common/intersection/Intersection";
import Landing from "@features/animateRoute/landing/Landing";
import BackButton from "@features/common/backButton/BackButton";
import GradientText from "@features/common/gradientText/GradientText";
import BrandPersonalize from "@features/personalisation/brandPersonalize/BrandPersonalize";

export default function Home({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const { state } = useApp();
  const chatbotImage =
    state?.atWorkSiteConfig?.chatbotDetails?.chatbotImage || "";

  // const tokenInfo = useAppSelector(getTokenInfo);
  const { t } = useTranslation();
  const router = useRouter();
  const [currentComponentId, setCurrentComponentId] =
    useState<COMPONENT_ID>(COMPONENT_ID.LANDING);
  const { firstTimeUserComponentList } =
    useFirstTimeUserComponentList(currentComponentId);
  const CurrentComponent = useMemo(
    () =>
      firstTimeUserComponentList.find(
        (comp) => comp.id === currentComponentId,
      )?.component ?? Landing,
    [currentComponentId],
  ) as React.FC<Partial<WorkComponentProps>>;

  const getNextComponentId = (currentId: string) => {
    const currentComponent = firstTimeUserComponentList.find(
      (c) => c.id === currentId,
    );
    const { next } = currentComponent || ({} as any);
    return next;
  };

  const getPrevComponentId = (currentId: string) => {
    const currentComponent = firstTimeUserComponentList.find(
      (c) => c.id === currentId,
    );
    return currentComponent?.prev || COMPONENT_ID.LANDING;
  };

  const handleContinue = (componentId: string = "") => {
    console.log("handleContinue called with componentId:", componentId);
    if (componentId && typeof componentId === "string") {
      setCurrentComponentId(componentId as any);
      return;
    }
    const nextComponentId = getNextComponentId(currentComponentId);
    console.log(nextComponentId, "nextComponentId");
    setCurrentComponentId(nextComponentId as any);
  };

  const handleCancel = () => {
    let targetId = getPrevComponentId(currentComponentId);
    setCurrentComponentId(targetId);
  };

  const [typingCompleted, setTypingCompleted] = useState(false);

  useEffect(() => {
    setTypingCompleted(false);
  }, [currentComponentId]);

  const currentComponentObj = firstTimeUserComponentList.find(
    (comp) => comp.id === currentComponentId,
  );
  const showInfoSection = currentComponentObj?.isAnimated !== false;
  const conversationText = currentComponentObj?.conversation || "";

  return (
    <CommonLayout>
      <motion.div key={currentComponentId} transition={{ duration: 0.3 }}>
        {currentComponentId !== COMPONENT_ID.LANDING && (
          <BackButton
            handleCancel={handleCancel}
            label={`${currentComponentObj?.label || ""}`}
          />
        )}
        {currentComponentId === COMPONENT_ID.CREATE_ACCOUNT && (
          <GradientText />
        )}

        {showInfoSection ? (
          <>
            <InfoSection
              text={conversationText}
              onTypingComplete={() => setTypingCompleted(true)}
              componentId={currentComponentId}
              chatbotImage={chatbotImage}
            />
            {typingCompleted && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                <CurrentComponent
                  key={currentComponentId}
                  onContinue={handleContinue}
                  onCancel={handleCancel}
                  componentId={currentComponentId}
                  localeWithRegion={locale}
                />
              </motion.div>
            )}
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <CurrentComponent
              key={currentComponentId}
              onContinue={handleContinue}
              onCancel={handleCancel}
              componentId={currentComponentId}
              localeWithRegion={locale}
            />
          </motion.div>
        )}
      </motion.div>
    </CommonLayout>
  );
}
