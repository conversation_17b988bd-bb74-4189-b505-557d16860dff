import { HttpLink, ApolloLink } from "@apollo/client";
import {
  defaultAPIBaseUrl,
  webStoreCDNAPIBaseUrl,
  greetingsAPIBaseUrl,
  feedbackGiftAPIBaseUrl,
  personalizationAPIBaseUrl,
  ordersAPIBaseUrl,
  searchAPIBaseUrl,
  searchAppKey,
  giftOpenAPIBaseUrl,
  groupGiftOpenAPIBaseUrl,
  guestLoginAPIUrl,
  ecomUsersAPIUrl,
  atWorkAPIBaseUrl
} from "@constants/apiEndPoints";
import { createPersistedQueryLink } from "@apollo/client/link/persisted-queries";
import { sha256 } from "../utils/sha256";

/**
 * @method HttpLinks
 * @description Links based on the http client name
 */
const HttpLinks = (locale: string) => {
  const defaultLocale = locale?.split("-")[0];
  const defaultGrahpqlEndpoint = new HttpLink({
    uri: !!locale
      ? `${defaultAPIBaseUrl}/${defaultLocale}/api/`
      : `${defaultAPIBaseUrl}/en/api/`,
  });

  const webSoreWithCDNGrahpqlEndpoint = createPersistedQueryLink({
    sha256,
    useGETForHashedQueries: true,
  }).concat(
    new HttpLink({
      uri: !!locale
        ? `${webStoreCDNAPIBaseUrl}/${defaultLocale}/api/`
        : `${webStoreCDNAPIBaseUrl}/en/api/`,
      headers: {
        "Content-Type": "application/json",
        "query-hash-timeout": "180",
        // "Yg-Ip-Address": ip,
      },
    })
  );

  const greetingsGrahpqlEndpoint = new HttpLink({
    uri: `${greetingsAPIBaseUrl}/api/`,
  });

  const feedbackGrahpqlEndpoint = new HttpLink({
    uri: `${feedbackGiftAPIBaseUrl}`,
  });

  const personalizationGrahpqlEndpoint = new HttpLink({
    uri: `${personalizationAPIBaseUrl}/api/v3`,
    headers: {
      "accept-language": defaultLocale,
    },
  });

  const ordersGrahpqlEndpoint = new HttpLink({
    uri: !!locale
      ? `${ordersAPIBaseUrl}/${defaultLocale}/api/`
      : `${ordersAPIBaseUrl}/en/api/`,
    credentials: "include",
  });

  const searchGrahpqlEndpoint = new HttpLink({
    uri: `${searchAPIBaseUrl}`,
    headers: {
      "x-api-key": searchAppKey || "",
    },
  });

  const giftOpenGraphqlEndpoint = new HttpLink({
    uri: `${giftOpenAPIBaseUrl}/graphql/`,
  });

  const groupGiftOpenGraphqlEndpoint = new HttpLink({
    uri: `${groupGiftOpenAPIBaseUrl}`,
  });

  const ecomUsersGraphQlEndpoint = new HttpLink({
    uri: `${ecomUsersAPIUrl}/graphql/`
  });

  const guestSignInGraphQlEndpoint = new HttpLink({
    uri: !!locale
      ? `${guestLoginAPIUrl}/${defaultLocale}/api/`
      : `${guestLoginAPIUrl}/en/api/`, credentials: 'include'
  });

  const atWorkGraphQlEndpoint = new HttpLink({
    uri: !!locale
      ? `${atWorkAPIBaseUrl}/${defaultLocale}/api/`
      : `${atWorkAPIBaseUrl}/en/api/`
  });

  const usersGraphqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "ecom-users",
    ecomUsersGraphQlEndpoint,
    defaultGrahpqlEndpoint
  );

  const guestLoginGrahpqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "guestLogin",
    guestSignInGraphQlEndpoint,
    usersGraphqlEndpoint
  );

  const defaultAndotherGrahpqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "webstore-with-cdn",
    webSoreWithCDNGrahpqlEndpoint,
    guestLoginGrahpqlEndpoints
  );

  const feebackGraphqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "feedback",
    feedbackGrahpqlEndpoint,
    defaultAndotherGrahpqlEndpoint
  );

  const searchBaseGrahpqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "search",
    searchGrahpqlEndpoint,
    feebackGraphqlEndpoints
  );

  const ordersAndOtherGrahpqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "ecomcart",
    ordersGrahpqlEndpoint,
    searchBaseGrahpqlEndpoint
  );

  const openGiftGraphqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "gift-open",
    giftOpenGraphqlEndpoint,
    ordersAndOtherGrahpqlEndpoint
  );

  const groupGiftopenGraphqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "group-gift-open",
    groupGiftOpenGraphqlEndpoint,
    openGiftGraphqlEndpoints
  );

  const otherGrahpqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "personalization",
    personalizationGrahpqlEndpoint,
    groupGiftopenGraphqlEndpoints
  );

  const atWorkGrahpqlEndpoints = ApolloLink.split(
    (operation) => operation.getContext().clientName === "at-work",
    atWorkGraphQlEndpoint,
    otherGrahpqlEndpoints
  );

  /**
   * @method currentHttpLink
   * @description To return active link based on the clientName
   * @returns
   */
  const currentHttpLink = () => {
    const link = ApolloLink.split(
      (operation) => operation.getContext().clientName === "greetings",
      greetingsGrahpqlEndpoint, // <= apollo will send to this if clientName is "greetings".
      atWorkGrahpqlEndpoints // <= else atWorkGrahpqlEndpoints will run
    );

    return link;
  };

  return {
    currentHttpLink,
  };
};

export default HttpLinks;
