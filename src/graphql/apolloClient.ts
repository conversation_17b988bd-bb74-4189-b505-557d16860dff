import {
  ApolloClient,
  InMemoryCache,
  NormalizedCacheObject,
  ApolloLink
} from "@apollo/client";
import merge from "deepmerge";
import isEqual from "lodash/isEqual";
import HttpLinks from "./httpLinks";
import ErrorLink from "./errorLink";
// import apolloLogger from "apollo-link-logger";

export const APOLLO_STATE_PROP_NAME = "__APOLLO_STATE__";

let apolloClient: ApolloClient<NormalizedCacheObject>;

export function createApolloClient(
  locale: string,
  tokenInfoParams: any,
  ip?: string,
  onCookieSet?: (cookies: string[]) => void
) {
  const { currentHttpLink } = HttpLinks(locale);
  const { onErrorLink } = ErrorLink();

  const cookieLink = new ApolloLink((operation, forward) => {
    return forward(operation).map((response) => {
      if (typeof window === "undefined" && operation.getContext()?.response) {
        const raw = operation.getContext().response.headers?.get("set-cookie");
        if (raw && onCookieSet) {
          const { splitCookiesString } = require("set-cookie-parser");
          const cookies = splitCookiesString(raw);
          onCookieSet(cookies);
        }
      }
      return response;
    });
  });

  // Add accept-language header globally unless already set
  const languageLink = new ApolloLink((operation, forward) => {
    operation.setContext(({ headers = {} as Record<string, any> }) => ({
      headers: {
        ...headers,
        'accept-language': headers['accept-language'] || locale || 'en',
      },
    }));
    return forward(operation);
  });

  return new ApolloClient({
    ssrMode: typeof window === "undefined",
    cache: new InMemoryCache({
      addTypename: false,
    }),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: "network-only",
      },
    },
    link: ApolloLink.from([onErrorLink, cookieLink, languageLink, currentHttpLink()]),
  });
}


export function initializeApollo(
  locale: string,
  initialState = null,
  tokenInfoParams = {},
  ip: any = "",
  onCookieSet?: (cookies: string[]) => void
) {
  const _apolloClient = createApolloClient(locale, tokenInfoParams, ip, onCookieSet);

  // If your page has Next.js data fetching methods that use Apollo Client, the initial state
  // gets hydrated here
  if (initialState) {
    // Get existing cache, loaded during client side data fetching
    const existingCache = _apolloClient.extract();

    // Merge the existing cache into data passed from getStaticProps/getServerSideProps
    const data = merge(initialState, existingCache, {
      // combine arrays using object equality (like in sets)
      arrayMerge: (destinationArray, sourceArray) => [
        ...sourceArray,
        ...destinationArray.filter((d) =>
          sourceArray.every((s) => !isEqual(d, s))
        ),
      ],
    });

    // Restore the cache with the merged data
    _apolloClient.cache.restore(data);
  }
  // For SSG and SSR always create a new Apollo Client
  if (typeof window === "undefined") return _apolloClient;
  // Create the Apollo Client once in the client
  if (!apolloClient) apolloClient = _apolloClient;

  return _apolloClient;
}

export function addApolloState(
  client: { cache: { extract: () => any } },
  pageProps: { props: { [x: string]: any } }
) {
  if (pageProps?.props) {
    pageProps.props[APOLLO_STATE_PROP_NAME] = client.cache.extract();
  }

  return pageProps;
}
