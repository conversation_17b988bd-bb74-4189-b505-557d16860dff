import { Observable } from "@apollo/client";
import { YGAG_REGION_CODE_COOKIE } from "@constants/common";
import { HTTP_STATUS_CODE } from "@constants/statusCodes";
import userAuth from "@features/common/userAuth.hook";
import { HAS_WINDOWS_REF } from "@utils/hasWindowRef";
import { getCookie } from "cookies-next";

/**
 * @method TokenHandler
 * @description A class to handle the access, id and refresh token on API calls
 */
const TokenHandler = () => {
  /**
   * @method isTokenExpired
   * @description Check whether the token has expired or not
   * @param status { number }
   * @returns boolean
   */
  const isTokenExpired = (status: number) => {
    return status === HTTP_STATUS_CODE.CORS;
  };

  /**
   * @method forwardOperationWithNewToken
   * @description Refresh token by call a token helper method
   */
  const forwardOperationWithNewToken = (
    forward: any,
    operation: any,
    tokenInfoParams: any
  ) => {
    const observable = new Observable<any>((observer) => {
      // used an annonymous function for using an async function
      (async () => {
        try {
          if (!HAS_WINDOWS_REF) {
            throw new Error("Support only to client side calls");
          }

          const { tokenInfo, dispatch, setTokenInfo } = tokenInfoParams;
          const activeRegion = getCookie(YGAG_REGION_CODE_COOKIE) || "STAE";
          const refreshToken = tokenInfo?.RefreshToken;

          if (!refreshToken) {
            throw new Error("RefreshToken not available from the storage");
          }

          // #. Get token from the service
          const { getTokenInfo } = userAuth();
          const newTokenInfo = await getTokenInfo(refreshToken, activeRegion as string);
          const accessToken = newTokenInfo?.accessToken;

          if (!accessToken) {
            window.location.reload();
            throw new Error("AccessToken unavailable");
          }

          // #. Update the header with new token
          operation.setContext(({ headers = {} }) => ({
            headers: {
              // Re-add old headers
              ...headers,
              // Switch out old access token for new one
              AccessToken: accessToken,
              Authorization: `JWT ${accessToken}`
            },
          }));

          const subscriber = {
            next: observer.next.bind(observer),
            error: observer.error.bind(observer),
            complete: observer.complete.bind(observer),
          };

          // #. Retry the failed call
          forward(operation).subscribe(subscriber);

          // #. Update the new access-token and id-token
          const userTokens = {
            ...tokenInfo,
            AccessToken: newTokenInfo?.accessToken,
            IdToken: newTokenInfo?.idToken,
            RefreshToken: refreshToken,
            isUserSignedIn: tokenInfo?.isUserSignedIn,
          };

          dispatch(setTokenInfo(userTokens));
        } catch (err) {
          observer.error(err);
        }
      })();
    });

    return observable;
  };

  return {
    isTokenExpired,
    forwardOperationWithNewToken,
  };
};

export default TokenHandler;
