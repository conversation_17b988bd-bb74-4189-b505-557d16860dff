import { onError } from "@apollo/client/link/error";
import * as Sen<PERSON> from "@sentry/nextjs";
import To<PERSON><PERSON>and<PERSON> from "./tokenHandler";
import { GENERIC_BRAND_LISTS_PATH, PAGEURLS } from "../constants/common";
import { HAS_WINDOWS_REF } from "../utils/hasWindowRef";
import getLocaleRegion from "../utils/getLocaleRegion";
import { HTTP_STATUS_CODE } from "../constants/statusCodes";
import { basePath } from "@constants/envVariables";
import { store } from "@redux/store";

/**
 * @method ErrorLink
 * @description Error link to handle the grphql onError event
 */
const ErrorLink = () => {
    /**
     * @method onErrorLink
     * @description Handle Error event
     */
    const onErrorLink = onError(
        ({ graphQLErrors, networkError, operation, forward }) => {
            //Access locale and region
            const localeRegion = HAS_WINDOWS_REF && getLocaleRegion(window.location.pathname);
            // #. Handle network error
            if (networkError) {
                const errorMessage = `[Network error]: ${networkError} [query]: ${JSON.stringify(operation?.operationName, null, 2)}`;
                const httpMethod = operation?.getContext()?.fetchOptions?.method || undefined;
                const requestContext = {
                    clientName: operation?.getContext()?.clientName,
                    method: httpMethod,
                };

                console.log(JSON.stringify(networkError, null, 2));
                console.log(`[Network error]: ${networkError} [query]: ${JSON.stringify(operation.operationName, null, 2)}`);
                console.log(graphQLErrors);
                Sentry.captureException(new Error(errorMessage), { extra: { requestContext } });
            }

            // #. Handle graphql errors
            if (graphQLErrors) {
                for (const gError of graphQLErrors) {
                    const erroObj: any = gError;
                    const { message, path, status } = erroObj;

                    // Suspicious activity detected check
                    if (HAS_WINDOWS_REF && status === HTTP_STATUS_CODE.UNUSUAL_ACTIVITY) {
                        window.location.href = `${window.location.origin}${basePath}/${localeRegion}${PAGEURLS.SUSPICIOUS_ACTIVITY}`;
                        break;
                    }

                    const { isTokenExpired, forwardOperationWithNewToken } =
                        TokenHandler();
                    const tokenInfoParams = store.getState().common.tokenInfo;
                    // #. status 401: accessToken expired
                    // #. If token expired, then get a new one from auth service
                    if (isTokenExpired(status)) {
                        return forwardOperationWithNewToken(
                            forward,
                            operation,
                            tokenInfoParams
                        );
                    }

                    if (path && String(path) !== GENERIC_BRAND_LISTS_PATH) {
                        console.log(`[GraphQL error]: Message: ${message}, Path: ${path} `);
                        Sentry.captureException(
                            `[GraphQL error]: Message: ${message}, Path: ${path} `
                        );
                    }
                }
            }
        }
    );

    return {
        onErrorLink,
    };
};

export default ErrorLink;
