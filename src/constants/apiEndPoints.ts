// #. Base API url for QA and other environments
const ECOM_GRAPHQL_BASE_URL =
  "https://ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co";
const WEBSTORE_CDN_BASE_URL =
  "https://ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co";
const GREETINGS_GRAPHQL_BASE_URL =
  "https://greetings-hub-gh-ecomv2.sit.yougotagift.co";
const FEEDBACK_GIFT_GRAPHQL_BASE_URL =
  "https://ecom-gifts-do-ae.sit.yougotagift.co/graphql/";
const SEARCH_GRAPHQL_BASE_URL =
  "https://search.ecom.sit.yougotagift.co/graphql/"; // need to change this later
const PERSONALIZATION_GRAPHQL_BASE_URL =
  "https://ecom-personalization-ep-ecomv2.sit.yougotagift.co";
const ORDERS_GRAPHQL_BASE_URL =
  "https://ecom-orders-eo-ecomv2.sit.yougotagift.co";

const GIFT_OPEN_GRAPHQL_BASE_URL =
  "https://ecom-gifts-ecom-ecomv2.sit.yougotagift.co";
const GROUP_GIFT_GRAPHQL_BASE_URL =
  "https://groupgift-do-ae.sit.yougotagift.co/graphql/";

const PAYMENT_CANCEL_API = "https://youpay-do-ae.sit.yougotagift.co";

const ECOM_GUEST_LOGIN_URL = "https://ecom-orders-eo-ecomv2.sit.yougotagift.co";

const ECOM_USERS_URL = "https://ecom-users-eu-ecomv2.sit.yougotagift.co";

const AT_WORK_GRAPHQL_BASE_URL = "https://atwork-aw-ecomv2.sit.yougotagift.co";

// #. API base url values are taking from env varaiable for sandbox and production. Not for QA.
export const defaultAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? ECOM_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_ECOM_GRAPHQL_BASE_URL;
export const webStoreCDNAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? WEBSTORE_CDN_BASE_URL
    : process.env.NEXT_PUBLIC_WEBSTORE_CDN_BASE_URL;
export const greetingsAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? GREETINGS_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_GREETINGS_GRAPHQL_BASE_URL;
export const feedbackGiftAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? FEEDBACK_GIFT_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_FEEDBACK_GIFT_BASE_URL;
export const searchAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? SEARCH_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_SEARCH_GRAPHQL_BASE_URL;
export const personalizationAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? PERSONALIZATION_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_PERSONALIZATION_GRAPHQL_BASE_URL;
export const ordersAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? ORDERS_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_ORDERS_GRAPHQL_BASE_URL;
export const giftOpenAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? GIFT_OPEN_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_GIFT_OPEN_GRAPHQL_BASE_URL;
export const groupGiftOpenAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? GROUP_GIFT_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_GROUP_GIFT_OPEN_GRAPHQL_BASE_URL;
export const guestLoginAPIUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? ECOM_GUEST_LOGIN_URL
    : process.env.NEXT_PUBLIC_ORDERS_GRAPHQL_BASE_URL;

// #. End point of the search api
export const brandSearchStaticEndpoint = "/brand-search/";
export const searchAppKey = process.env.NEXT_PUBLIC_SEARCH_API_KEY;

// #. Payment cancel API
export const paymentCancelAPIBaseUrl = `${process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
  ? PAYMENT_CANCEL_API
  : process.env.NEXT_PUBLIC_ECOM_PAYMENT_CANCEL_API
  }/services/api/v1/payment-cancel/`;

export const ecomUsersAPIUrl = process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
  ? ECOM_USERS_URL
  : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT;

export const atWorkAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? AT_WORK_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_AT_WORK_GRAPHQL_BASE_URL;