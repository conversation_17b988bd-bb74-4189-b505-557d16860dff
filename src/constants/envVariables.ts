
const GIFT_ACTIVATE_BASE_URL = "https://ecom-gifts-do-ae.sit.yougotagift.co";
const GIFT_ACTIVATE_API = {
    GET_OTP: "/gifts/api/v1/gift-accept/send-otp/",
    VERIFY_OTP: "/gifts/api/v1/gift-accept/verify-otp/",
    RESEND_OTP: "/gifts/api/v1/gift-accept/resend-otp/",
};

const ECOM_APP_URL = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/";
const LOCALE_REGION_COOKIE_DOMAIN = ".yougotagift.co";
const GROUP_GIFT_APP_LEGACY_URL =
    "https://groupgift-frontend-egg-do-ae.sit.yougotagift.co/";
const COGNITO_CUSTOM_DOMAIN = "https://login-v2.sit.yougotagift.co";
const GROUP_GIFT_APP_URL =
    "https://ecom-groupgift-frontend-egg-ecomv2.sit.yougotagift.co/";

const COGNITO_LOGOUT_URL =
    "https://ecom-mweb-frontend-ev-ecomv2.sit.yougotagift.co/";

const ECOM_GIFT_ENDPOINT = "https://ecom-gifts-ecom-do-ae.sit.yougotagift.co";

const ECOM_GIFT_API = {
    DOWNLOAD_PDF: "/gifts/download-zip-pdf/",
    DownloadPDFV2: "/gifts/api/v2/download-pdf/",
};

const ECOM_GIPHY_API_KEY = "p69f7Yp2E7PtXTiORtPX7mhMwc6Nu0zp";
const RECAPTCHA_SITE_KEY =
    "6LcR5QcoAAAAAEDuN1ymGFjEazBQO5zwwuPqwF0U-vz1uz4GfeIWCr";
const ECOM_COGNITO_REDIRECT_URL =
    "https://ecom-frontend-do-ae.sit.yougotagift.co";
const ECOM_COOKIE_DOMAIN = ".yougotagift.co";
const ECOM_COGNITO_ORIGINS = [
    "https://ecom-mweb-frontend-do-ae.sit.yougotagift.co",
];
// const ECOM_TOKEN_URL = "https://ecom-users-eu-100.sit.yougotagift.co/users/api/v1/auth/user/set_tokens/";

const ECOM_COGNITO_AUTH_REDIRECT_URL =
    "https://ecom-auth-frontend-do-ae.sit.yougotagift.co/";

const ECOM_USERS_ENDPOINT = "https://ecom-users-eu-ecomv2.sit.yougotagift.co";

const ECOM_USERS_API = {
    GET_TOKEN: "/users/api/v1/auth/user/get-tokens/",
    PROFILE_UPDATE_API: "/users/api/v1/auth/user/update/",
    REVOKE_TOKEN_API: "/users/api/v1/auth/user/revoke-tokens/",
    GET_PROFILE_API: "/users/api/v1/auth/user/details/",
    CHANGE_PASSWORD_API: "/users/api/v1/auth/user/change-password/",
    DELETE_USER_API: "/users/api/v1/auth/user/delete-user/",
    UPDATE_PHONE_NUMBER_API: "/users/api/v1/auth/user/update-phone-number/",
};

const ECOM_HOST_DOMAIN = "https://ecom-frontend-do-ae.sit.yougotagift.co";
const IFRAME_GG_APP_URL =
    "https://ecom-groupgift-frontend-egg-ecomv3.sit.yougotagift.co/";
const isProd = process.env.NODE_ENV === "production";

export const imageBaseUrl =
isProd ? process.env.NEXT_PUBLIC_ECOM_ASSET_PREFIX : '/atwork';
export const identityPoolId = process.env.NEXT_PUBLIC_ECOM_COGNITO_IDENTITY_POOL_ID;
export const region = process.env.NEXT_PUBLIC_ECOM_COGNITO_REGION;
export const userPoolId = process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_ID;
export const userPoolWebClientId = process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_WEBCLIENT_ID;
export const s3BucketName = process.env.NEXT_PUBLIC_ECOM_COGNITO_S3_BUCKET_NAME;
export const redirectURL =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_COGNITO_REDIRECT_URL
        : process.env.NEXT_PUBLIC_ECOM_COGNITO_REDIRECT_URL;

export const cookieDomain =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_COOKIE_DOMAIN
        : process.env.NEXT_PUBLIC_ECOM_COOKIE_DOMAIN;

export const origins =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_COGNITO_ORIGINS
        : process.env.NEXT_PUBLIC_ECOM_ORIGIN_SITES?.split(" ");

export const userTokenUrl =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.GET_TOKEN;

export const profileUpdateAPI =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.PROFILE_UPDATE_API;

export const revokeTokenUrl =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.REVOKE_TOKEN_API;

export const profileDetailsAPI =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.GET_PROFILE_API;

export const authRedirectUrl =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_COGNITO_AUTH_REDIRECT_URL
        : process.env.NEXT_PUBLIC_ECOM_AUTH_REDIRECT_URL;

export const changePasswordAPI =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.CHANGE_PASSWORD_API;

export const groupGiftUrl =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GROUP_GIFT_APP_URL
        : process.env.NEXT_PUBLIC_GROUP_GIFT_DOMAIN;

export const ecomUrl =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_APP_URL
        : process.env.NEXT_PUBLIC_ECOM_DOMAIN;

export const giftActivateGetTokenUrl =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GIFT_ACTIVATE_BASE_URL
        : process.env.NEXT_PUBLIC_GIFT_ACTIVATE_BASE_URL) + GIFT_ACTIVATE_API.GET_OTP;

export const giftActivateVerifyTokenUrl =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GIFT_ACTIVATE_BASE_URL
        : process.env.NEXT_PUBLIC_GIFT_ACTIVATE_BASE_URL) + GIFT_ACTIVATE_API.VERIFY_OTP;

export const giftActivateReSendTokenUrl =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? GIFT_ACTIVATE_BASE_URL
        : process.env.NEXT_PUBLIC_GIFT_ACTIVATE_BASE_URL) + GIFT_ACTIVATE_API.RESEND_OTP;

export const basePath = '/atwork';

export const downloadGiftUrl =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_GIFT_ENDPOINT + ECOM_GIFT_API.DOWNLOAD_PDF
        : process.env.NEXT_PUBLIC_GIFT_ENDPOINT + ECOM_GIFT_API.DownloadPDFV2;

export const ecomHostDomain =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_HOST_DOMAIN
        : process.env.NEXT_PUBLIC_ECOM_HOST_DOMAIN;

export const deleteProfileAPI =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.DELETE_USER_API;

export const phoneNumberUpdate =
    (process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_USERS_ENDPOINT
        : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) + ECOM_USERS_API.UPDATE_PHONE_NUMBER_API;

export const giphyKey =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? ECOM_GIPHY_API_KEY
        : process.env.NEXT_PUBLIC_GIPHY_API_KEY;

export const localeRegionDomain =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? LOCALE_REGION_COOKIE_DOMAIN
        : process.env.NEXT_PUBLIC_LOCALE_REGION_COOKIE_DOMAIN;

export const domain =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? LOCALE_REGION_COOKIE_DOMAIN
        : process.env.NEXT_PUBLIC_LOCALE_REGION_COOKIE_DOMAIN;

export const captchSiteKey =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? RECAPTCHA_SITE_KEY
        : process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

export const iframeGgAppUrl =
    process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
        ? IFRAME_GG_APP_URL
        : process.env.NEXT_PUBLIC_IFRAME_GG_APP_URL;

export const solutionsHubURL = process.env.NEXT_PUBLIC_SOLUTIONS_HUB_URL;