// Platform Types
export enum PLATFORM_TYPE {
  WEB = "WEB",
  TABLET = "TABLET",
  MOBILE = "MOBILE",
  MWEB = "MWEB",
}

// Header Types
export enum HEADER_TYPE {
  MAJOR = "MAJOR",
  MINOR = "MINOR",
}

// Banner Types
export enum BANNER_TYPE {
  MAIN = "MAIN",
  SIDE = "SIDE",
  REWARDS = "REWARDS",
  PROMOTIONAL = "PROMOTIONAL",
}
// #. Brand form states
export enum ILLUSTRATION_TYPE {
  STANDARD = "illustrations",
  GIF = "gifIllustrations",
}

// Slider Types
export enum SLIDER_TYPE {
  CATEGORY = "CATEGORY_SLIDER",
  BRAND = "BRAND_SLIDER",
}

// Widget Types
export enum WIDGET_TYPE {
  BLOG = "BLOG",
  REWARDS_BANNER = "REWARDS_BANNER",
  PROMOTIONAL_BANNER = "PROMOTIONAL_BANNER",
  DOWNLOAD_APP = "DOWNLOAD_APP",
}

// #. Upload Types
export enum UPLOAD_TYPES {
  PHOTO = "photo",
  VIDEO = "video",
  GIF = "gif",
}

// #. Brand form states
export enum BRAND_FORM_STATE {
  CREATE = "create",
  EDIT = "edit",
  SESSION = "session",
}

// pages types
export enum PAGES {
  HOME = "Home",
  ALL_BRANDS = "All Brands",
  CART = "Cart",
  BRAND = "Brand",
  HAPPY_YOU = "HappyYou"
}

// Page URL'S
export enum PAGEURLS {
  HOME = "/home",
  LOGIN = "/login",
  ALL_BRANDS = "/brands",
  ALL_BRANDS_SEARCH = "/all",
  APP_DOWNLOAD = "/app-download",
  BACK_SOON = "/back-soon",
  CART = "/cart",
  ABOUT_US = "/about-us",
  PRIVACY_POLICY = "/privacy-policy",
  TERMS = "/terms-of-use",
  BRAND = "/brands",
  UNSUPPORTED_COUNTRY = "/unsupported-country",
  PAYMENT_SUCCESS = "/payment/success",
  PAYMENT_FAIL = "/payment/error",
  GIFT_OPEN_PUBLIC = "/gifts/greetings",
  SUSPICIOUS_ACTIVITY = "/suspicious-activity",
  DATA_DELETION_INSTRUCTIONS = "/data-deletion-instructions",
  MEDIA_CENTER = "/media-center",
  PROFILE = "/profile/edit",
  CATERGORIES = "/categories",
  NOT_FOUND = "/not-found",
  // GROUP_GIFTING = "/group-gifting/",
  offers = "/offers/",
  rewardsPath = "https://rewards.yougotagift.com/en/account/login/identifier/",
  rewardsPathAR = "https://rewards.yougotagift.com/ar/account/login/identifier/",
  WORK = "/",
  SHOP = "/shop",
  START_GG = "start-group-gift",
  GAMING_GIFT_CARD = "/gaming-gift-card",
  GIFTING = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/shop/en-ae/",
  GROUP_GIFTING = "https://ecom-groupgift-frontend-egg-ecomv2.sit.yougotagift.co/en-ae/",
  OFFERS = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/shop/en-ae/offers/",
  GAMING = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/shop/en-ae/categories/gaming-gift-card/brands/",
}

// Signin Signup screens
export enum SIGNIN_SIGNUP_SCREENS {
  SIGNIN_MOBILE = "signin-mobile",
  OTP_VERIFICATION = "otp-verification",
  ADD_INFO = "add-info",
  ADD_INFO_WITH_EMAIL = "add-info-with-email",
  SIGNIN_EMAIL = "signin-email",
  CHANGE_PASSWORD = "change-password",
  ENTER_DETAILS = "enter-details",
  FORGOT_PASSWORD = "forgot-password",
}

// Cognito oauth scopes
export const COGNITO_OAUTH_SCOPES = [
  "profile",
  "email",
  "openid",
  "https://www.googleapis.com/auth/user.gender.read",
  "https://www.googleapis.com/auth/user.phonenumbers.read",
  "aws.cognito.signin.user.admin",
];

// Cognito static configurations
export enum COGNITO_CONFIG {
  USER_PASSWORD_AUTH_TYPE = "USER_PASSWORD_AUTH",
  CUSTOM_AUTH_TYPE = "CUSTOM_AUTH",
  RESPONSE_TYPE = "code",
  RESPONSE_SEPARATOR = "1*#*1",
}

export enum COGNITO_USER_POOL_FIELDS {
  NAME = "name",
  EMAIL = "email",
  PHONE = "phone_number",
  PHONE_VERIFIED = "phone_number_verified",
  SECURE_PAGE_ACCESS = "custom:secure_page_access",
  FIRST_SIGN_IN = "custom:first_sign_in",
  GENDER = "gender",
  BIRTH_DATE = "birthdate",
  PICTURE = "picture",
  IDENTITIES = "identities",
}

export enum COGNITO_USER_TYPE {
  COGNITO = "cognito",
  GOOGLE = "google",
  FACEBOOK = "facebook",
  APPLE = "signinwithapple",
  LEGACY = "legacy",
}

export enum ASIDE_PAGES {
  EDIT_PROFILE = "profile",
  CHANGE_PASSWORD = "change-password",
  ORDERED = "ordered",
  RECIEVED = "recieved",
  REDEEMED = "redeemed",
  EXPIRED = "expired",
}

export enum PROFILE_MENU {
  EDIT_PROFILE = "profile",
  DELETE_PROFILE = "delete",
}

export enum SIGN_IN_SLUGS {
  SIGN_IN = "login",
  EMAIL_LOGIN = "email-login",
  CHANGE_PASSWORD = "change-password",
  ADD_INFO = "add-info",
  VERIFY_OTP = "verify-otp",
  ADD_MOBILE = "add-mobile",
  MOBILE_LOGIN = "mobile",
  SECURE_PAGE = "secure-Page",
  VERIFY_EMAIL = "verify-email",
}

export enum PASSWORD_STRENGTH {
  GOOD = "good",
  BAD = "bad",
}
export enum FEEDBACK_STATUS {
  HAPPY = "happy",
  NOT_HAPPY = "not_happy",
  VERY_HAPPY = "very_happy",
}
export enum STATUS_CODE {
  STATUS_CODE_200 = 200,
  STATUS_CODE_201 = 201,
  STATUS_CODE_202 = 202,
  STATUS_CODE_406 = 406,
  STATUS_CODE_403 = 403,
  STATUS_CODE_97200 = 97200,
  STATUS_CODE_97201 = 97201,
  STATUS_CODE_97401 = 97401,
  STATUS_CODE_97404 = 97404,
  STATUS_CODE_97405 = 97405,
}

export enum SIGN_UP_STATE {
  LEGACY_RESET_PASSWORD = "legacy-reset-password",
  NONE = "none",
}

export enum BRAND_REDEMPTION_TYPE {
  IN_STORE = "store",
  ONLINE = "online",
  ONLINE_AND_IN_STORE = "online_store",
}

// #. User Types
export enum USER_TYPES {
  LOGGED_IN = "Logged In",
  GUEST = "Guest",
}

export enum CAPTCHA_CONFIG_ACTION {
  DOWNLOAD_APP = "download_app",
  PRODUCT_FEEDBACK = "product_feedback",
  NEWS_LETTER = "newsletter",
  GIFT_ACCEPT_EMAIL = "gift_accept_email",
}

export enum CAPTCHA_CONFIG_ACTION {
  UPDATE_PHONE_NUMBER = "update_phone_number",
}

export enum FLOW_TYPE {
  CHANGE_PHONE_NUMBER = "ChangePhoneNumber",
}

export enum CHANNEL_TYPE {
  WHATSAPP = "WHATSAPP",
}

export enum DATE_FORMAT {
  DATE_DMY = "d MMM,  yyyy",
}

export enum SESSIONKEYS {
  PHONE_NUMBER = "phoneNumber",
}

export enum DIAL_CODES {
  UM = "1",
}

export enum COUNTRY_CODES {
  UM = "UM",
}

export enum FOOTER_ITEM_TYPE {
  ITEM_CODE_OUR_SOCIAL = "ITEM_CODE_OUR_SOCIAL",
  ITEM_CODE_OUR_SERVICES = "ITEM_CODE_OUR_SERVICES",
  ITEM_CODE_SUPPORT = "ITEM_CODE_SUPPORT",
  FB_ITEM_CODE = "FB_ITEM_CODE",
  ITEM_CODE_FOR_DEVELOPERS = "ITEM_CODE_FOR_DEVELOPERS",
}

export enum FOOTER_ALIGNMENT {
  VERTICAL = "VERTICAL",
  HORIZONTAL = "HORIZONTAL",
}

export const GROUP_GIFT_APP_URL = "https://app.groupgift.yougotagift.com/";
export const BUSINESS_APP_URL = "https://yougotagift.com/business/";
export const BUSINESS_APP_URL_AR = "https://yougotagift.com/business/ar";
export const HELP_CENTER_URL = "https://support.yougotagift.com/hc/en-us";
export const HELP_CENTER_URL_AR = "https://support.yougotagift.com/hc/ar";
export const YGAG_BLOG_URL = "https://blog.yougotagift.com/";
export const PAGE_CACHE_CONFIG = "no-store";
export const YGAG_REGION_COOKIE = "YGAG_REGION_INFO";
export const YGAG_REGION_CODE_COOKIE = "YGAG_REGION_CODE";
export const GENERIC_BRAND_LISTS_PATH = "genericBrandLists";
export const GUEST_USER_SESSION_COOKIE = "session_ygag";
export const SNOW_ENABLED = "SNOW_ENABLED";
export const VOLUME_ENABLED = "VOLUME_ENABLED";

export const CUSTOM_GIFT_CARD = {
  DEFAULT_BG_COLOR: "#B800C4",
  LOGO_IMG_WIDTH: 170,
  LOGO_IMG_HEIGHT: 60,
  BACKGROUND_IMG_WIDTH: 1016,
  BACKGROUND_IMG_HEIGHT: 650,
  VALID_TYPES: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
};

export enum COMPONENT_ID {
  LANDING = 'landing',
  GIFT_TYPE = 'giftTypeSelection',
  BRAND_LIST = 'brandList',
  BRAND_DENOMINATION = 'brandDenomination',
  PERSONALIZE_GREETINGS = 'personalizeGreetings',
  PERSONALIZE_MEDIA = 'personalizeMedia',
  PERSONALIZE_MESSAGE = 'personalizeMessage',
  RECIPIENT = 'recipient',
  CART = 'cart',
  ORDER_SUMMARY = 'orderSummary',
  LOGIN = 'login',
  CUSTOM_GIFT_CARD = 'customGiftCard',
  CREATE_PROFILE = 'createProfile',
  PDF_PREVIEW = 'pdfPreview',
  RECIPIENT_PDF = 'pdfRecipients',
  CUSTOM_CARD_SELECTION = 'customCardSelection',
  YOU_REWARDS_REDIRECT = 'youRewardsRedirect',
  FAQ_ANIMATION = 'faqAnimation',
  LOGIN_INTRO = 'loginIntro',
  CREATE_ACCOUNT = 'createAccount',
  CARD_TYPE_SELECTION = 'cardTypeSelection',
  VISIT_WEB_OR_APP = 'visitWebOrApp',
  ABOUT = 'about',
  HAPPY_YOU_OFFERS = 'happyYouOffers',
  HAPPY_YOU_TERMS = 'happyYouTerms',
  HAPPY_YOU_ABOUT = 'happyYouAbout',
  HAPPY_YOU_BRANDS_INCLUDED = 'happyYouBrandsIncluded',
}

export enum WORKPAGE_URLS {
  CART = "/cart",
  WORK = "/",
  PROFILE = "/account/profile",
  ORDERS = "/account/orders",
  WALLET = "/account/wallet",
}

export enum BRAND_CARD_TYPE {
  NON_GENERIC = "NON_GENERIC_CARDS",
  HAPPY_YOU_NORMAL = "HAPPY_YOU_CARDS_NORMAL",
  HAPPY_YOU_CUSTOM = "HAPPY_YOU_CARDS_CUSTOM",
}

export const STATUS_USER_EXIST = "96408";
export const AUTH_SESSION_EXPIRED = "92402";
export const REWARDS_USER_EXIST = "48200";

export const STORE_TIMING = "Everyday 9 AM to 9 PM";

export enum PAYMENT_CARD_TYPE {
  VISA = "Visa",
}

export enum UPLOAD_TYPE {
  CUSTOM_HYC_LOGO_IMAGE = "CUSTOM_HYC_LOGO_IMAGE",
  CUSTOM_HYC_BG_IMAGE = "CUSTOM_HYC_BG_IMAGE",
  CUSTOM_HYC_CARD_IMAGE = "CUSTOM_HYC_CARD_IMAGE",
  SENDER_LOGO_IMAGE = "SENDER_LOGO_IMAGE",
  PDF_GREETINGS_COVER_IMAGE = "PDF_GREETINGS_COVER_IMAGE",
  GREETINGS_COVER_IMAGE = "GREETINGS_COVER_IMAGE",
  PERSONALIZATION_IMAGE = "PERSONALIZATION_IMAGE",
  TRANSFER_RECEIPT = "TRANSFER_RECEIPT",
  REQUESTED_FILE = "REQUESTED_FILE",
}
export enum GIFT_TYPE_SELECTION {
  CUSTOM_CARD = "HappyYOU Custom",
  HAPPY_YOU_CARD = "HappyYOU Card",
  SINGLE_CARD = "Single Brands",
}

export const BASE_PATH = "atwork";

export const ORDER_DETAILS_DATA_LIMIT: number = 10;

export const MOBILE_WIDTH = 768;

export enum MENU_TYPE {
  CAREER = "CR_ITEM_CODE",
}

export const CHATBOT_TRIGGER_CONFIG = {
  SHOW_AFTER_MINUTES: 5,
  COOKIE_NAME: "chatbot_opened",
  COOKIE_EXPIRY_DAYS: 1,
};

export enum IMAGE_CROP {
  MIN_CROP_WIDTH = 170,
  MIN_CROP_HEIGHT = 40,
  MIN_IMG_WIDTH = 250,
  SLIDER_MIN_VALUE = 0.2,
  SLIDER_MAX_VALUE = 1,
  SLIDER_STEP_VALUE = 0.01,
  FILE_TYPE = "image/png",
}

export const FEATURED_OCCASION_CODE = "featured";
export const THANK_YOU_OCCASION_CODE = "THAY";

export const CUSTOM_HAPPY_PATH = "/account/custom-happy/";

export enum PDF_LOGO_UPLOAD {
  ASPECT_RATIO = 170 / 60,
  DIMENSION_PREVIEW = "170w x 60h",
}

export enum GIFT_OPEN_SECTION {
  OPEN = "open",
  STORIES = "stories",
  PERSONALIZE = "personalize",
  UNWRAP = "unwrap",
  GIFT = "gift",
}

export enum WALLET_MODAL_TYPE {
  CREATE = "create-topup",
  CONFIRM = "confirm-transfer",
  EDIT_TOPUP = "edit-topup",
  VIEW_REQUEST = "view-request",
}

export enum CREATE_MODAL_FLOW {
  SELECET_CURRENCY = "Select Currency",
  TOPUP_AMOUNT = "Top Up Amount",
  TAX_INFO = "Tax Information",
}

export interface GiftSummaryDataInterface {
  sender?: {
    name: string;
  };
  receiverName?: string;
  senderName?: string;
}

export interface GiftDataInterface extends GiftSummaryDataInterface {
  giftData?:
    | {
        brand: {
          name: string;
          brandImage: string;
        };
        currency: {
          code: string | any;
        };
        amount: string;
        senderName: string;
        sender: {
          name: string | any;
        };
      }
    | any;
  receiverEmail?: string | any;
  receiverPhone?: string;
  receiverPhoneCountryCode?: string;
  isSmsGift?: boolean;
  isPreview?: boolean;
  deonominationPreview?: boolean;
}

export enum PAYMENT_METHOD {
  CREDITS = "credits",
  CARD = "card",
}

export enum ALLOWED_FILE_TYPES {
  PNG = "image/png",
  JPG = "image/jpg",
  JPEG = "image/jpeg",
  PDF = "application/pdf",
  DOC = "application/msword",
  DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  TXT = "text/plain",
}

export enum URL_TYPES {
  VAT_CERTIFICATE = "VAT_CERTIFICATE",
}

// #. Card purchase types
export enum CARD_PURCHASE_TYPE {
  SELF = "self",
  GIFT = "gift",
  BUY_FOR_SELF = "buy-for-self",
  GIFT_SOMEONE = "send-as-gift",
  ONLINE = "Online",
  INSTORE = "Instore",
  ONLINE_INSTORE = "Online & Instore",
  EMAIL_SMS = "email-sms",
  DOWNLOAD_PDF = "download-pdf",
}

export enum GREETING_COVER_TYPE {
  STANDARD = "standard",
  ANIMATED = "gif",
}

export enum PERSONALISATION_TYPE {
  VIDEO = "video",
  PHOTO = "photo",
}

export enum PERSONAL_TITLE {
  MISTER = "Mr",
  MISS = "Ms",
  OTHER = "Mx",
}

export enum CARD_DELIVERY_TYPE {
  INSTANT = "immediate",
  CUSTOM = "custom",
}

export enum GREETING_LANGUAGE {
  ENGLISH = "en",
  ARABIC = "ar",
}

// #. Default occasion
export enum DEFAULT_OCCASION {
  CODE = "BIR",
  NAME = "Birthday",
}

// #. Default phonecode
export const DEFAULT_PHONE_CODE = "+971";

// #. Different display mode of the selected value display component
export enum BRAND_VALUE_DISPLAY_MODE {
  NORMAL = "normal",
  PERSONALIZE = "personalize",
}

// #. Brand user message section

// #. Message background-colors
export const USER_MESSAGE_BACKGROUNDS = [
  "#EB6B6B",
  "#1DCAFF",
  "#6B8CFA",
  "#F8C668",
  "#6FDD1F",
  "#C260F5",
  "#F22F98",
  "#808080",
];

// #. Message font-sizes
export const USER_MESSAGE_FONT_SIZES = [
  16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36,
];

// #. Message English font family
export const USER_MESSAGE_FONT_FAMILY_EN = [
  "Poppins",
  "Brush script MT",
  "Georgia",
  "Arial",
  "Bradley Hand",
];

// #. Message Arabic font family
export const USER_MESSAGE_FONT_FAMILY_AR = [
  "Cairo",
  "Tajawal",
  "El Messiri",
  "Reem Kufi",
  "Blaka Hollow",
];

export const FONT_FAMILIES = {
  POPPINS: "Poppins",
  BRUSH_SCRIPT: "Brush script MT",
  GEORGIA: "Georgia",
  ARIAL: "Arial",
  BRADLEY: "Bradley Hand",
  CAIRO: "Cairo",
  TAJAWAL: "Tajawal",
  EL_MESSIRI: "El Messiri",
  REEM_KUFI: "Reem Kufi",
  BLAKA_HOLLOW: "Blaka Hollow",
  MONA_SANS: "Mona Sans",
  NOTA_KUFI: "Noto Kufi",
};

// Locales
export enum LOCALES {
  ENGLISH = "en",
  ARABIC = "ar",
}

// #. Date values
export enum DATE_TYPE {
  TODAY = "today",
  TOMORROW = "tomorrow",
  DAY_AFTER_TOMORROW = "day_after_tomorrow",
  CUSTOM = "custom",
}

export enum TIMZONE_VALUES {
  CENTRAL = "Asia/Dubai",
  GMT = "gmt",
}

// #. Name for the accordions
export enum BRAND_ACCORDION {
  CARD_VALUE = "card-value",
  GREETING = "greeting",
  PERSONALIZE = "personalize",
  USER_MESSAGE = "user-message",
}

export enum BRAND_STEPPER {
  UNKNOWN__ = -1,
  CARD_VALUE = 0,
  GREETING = 1,
  PERSONALIZE = 2,
  USER_MESSAGE = 3,
  DELIVERY = 4,
}

export enum WORK_STEPPER {
  UNKNOWN__ = -1,
  GREETING = 0,
  PERSONALIZE = 1,
  USER_MESSAGE = 2,
}

export enum BRAND_STEPPER_STATE {
  NOT_OPENED = 0,
  ACTIVE = 1,
  SKIPPED = 2,
  COMPLETED = 3,
}

export enum WORK_STEPPER_STATE {
  NOT_OPENED = 0,
  ACTIVE = 1,
  SKIPPED = 2,
  COMPLETED = 3,
}

// #. Preview types
export enum PREVIEW_TYPES {
  greeting,
  photo,
  video,
  message,
  dummy,
}

//#. Delivery types
export enum DELIVERY_TYPE {
  INSTANT = "Instant",
  ANOTHER_DATE = "Another Date",
}

// #. Session storage key
export const BRAND_SESSION_STORE_KEY = "YGAG_BRAND_PERS";
export const BRAND_SESSION_DIRTY_KEY = "YGAG_BRAND_DIRTY";

// #. Happy mode
export enum REVIEW_HAPPY_MODE {
  VERY_HAPPY = "VERY_HAPPY",
  HAPPY = "HAPPY",
  NOT_HAPPY = "NOT_HAPPY",
}

export const BRAND_INCLUDED_FILTER = {
  ALL: "all",
  ONLINE: BRAND_REDEMPTION_TYPE.ONLINE,
  IN_STORE: BRAND_REDEMPTION_TYPE.IN_STORE,
};

export const DEFAULT_ARABIC_FONT = "Cairo";

export enum BRAND_DELIVERY_MODES {
  DIGITAL = "digital",
  PRINT_AT_HOME = "print-at-home",
}

export enum BRAND_ITEM_CATEGORY {
  NO_CATEGORY = "No category",
}

export enum BannerType {
  GROUP_GIFT = "GROUP_GIFT",
  HAPPY_CARD = "HAPPY_CARD",
  BRAND_OFFER = "BRAND_OFFER",
}

export enum BrandPurchaseCode {
  GIFT = "gift_a_friend",
  SELF = "buy_for_self",
}

export enum BrandPersonalizeType {
  VIDEO = "video",
  PHOTO = "photo",
  GIF = "gif",
}

export enum BRAND_OOS_CLASSIFICATION {
  BLOCK_CODE = "block_code",
}

export const DEFAULT_COUNTRY_CODE = "AE";

export const SELECTED_CARD_TYPE = {
  CUSTOM: "custom",
  STANDARD: "standard",
}
