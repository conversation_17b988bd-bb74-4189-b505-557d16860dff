
export enum CT_EVENTS {
  AT_WORK_START = "@WORK START",
  BRAND_TYPE_SELECT = "BRAND TYPE SELECT",
  SKIN_SELECT = "SKIN SELECT",
  DENOMINATION = "DENOMINATION",
  SINGLE_BRAND_SELECT = "SINGLE BRAND SELECT",
  EMAIL_DELIVERY_PERSONALIZATION = "EMAIL DELIVERY PERSONALIZATION",
  PDF_DELIVERY_PERSONALIZATION = "PDF DELIVERY PERSONALIZATION",
  ORDER_SUMMARY = "ORDER SUMMARY",
  AT_WORK_ADD_TO_CART = "@WORK ADD TO CART",
  AT_WORK_CHECKOUT = "@WORK CHECKOUT",
  AT_WORK_CHARGED = "@WORK CHARGED",
  AT_WORK_LOGIN = "@WORK LOGIN",
  AT_WORK_SIGN_UP = "@WORK SIGN UP",
  AT_WORK_EMAIL_GREETING_SELECTED ="@WORK EMAIL GREETING SELECTED",
  AT_WORK_EMAIL_MEDIA = "@WORK EMAIL MEDIA",
  AT_WORK_EMAIL_MESSAGE = "@WORK EMAIL MESSAGE",
  AT_WORK_PDF_LOGO = "@WORK PDF LOGO",
  AT_WORK_PDF_GREETING_COVER = "@WORK PDF GREETING COVER",
  AT_WORK_PDF_MESSAGE = "@WORK PDF MESSAGE",
  AT_WORK_CART_DENOMINATION_REMOVE = "@WORK CART DENOMINATION REMOVE",
  AT_WORK_CART_ITEM_REMOVE = "@WORK CART ITEM REMOVE",
  AT_WORK_CART_RECIPIENT_REMOVE = "@WORK CART RECIPIENT REMOVE"
  }

export enum CT_PROPERTIES {
  PLATFORM = "Platform",
  STORE = "Store",
  BRAND_TYPE = "Brand Type",
  SKIN_ID = "Skin ID",
  OCCASION = "Occasion",
  DENOMINATION = "Denomination",
  CURRENCY = "Currency",
  DELIVERY_TYPE = "Delivery Type",
  BRAND = "Brand",
  GREETING_OCCASION = "Greeting Occasion",
  GREETING_ID = "Greeting ID",
  LANGUAGE = "Language",
  GREETING_CUSTOMIZATION = "Greeting Customization",
  MEDIA = "Media",
  MESSAGE = "Message",
  PERSONALIZATION_TYPE = "Personalization Type",
  NUMBERS_OF_RECIPIENTS = "Number of Recipients",
  TOTAL_CARDS = "Total Cards",
  TOTAL_VALUE = "Total Value",
  TOTAL_TO_PAY = "Total To Pay",
  DELIVERY_TIME = "Delivery Time",
  SENDER_EMAIL = "Sender Email",
  SENDER_PHONE = "Sender Phone",
  SENDER_NAME = "Sender Name",
  RECEIVER_NAME = "Receiver Name",
  RECEIVER_EMAIL = "Receiver Email",
  RECEIVER_PHONE = "Receiver Phone",
  PERSONALIZATION = "Personalization",
  PAYMENT_METHOD = "Payment Method",
  AT_WORK_EMAIL = "@Work Email",
  AT_WORK_COMPANY_NAME = "@Work Company Name",
  TYPE = "Type",
  CUSTOMIZATION = "Customization"
}

export enum GTM_PROPERTIES {
  STORE = "store",
  BRAND_TYPE = "brand_type",
  SKIN_ID = "skin_name",
  OCCASION = "occasion",
  DENOMINATION = "denomination",
  CURRENCY = "currency",
  DELIVERY_TYPE = "delivery_type",
  BRAND = "brand",
  GREETING_OCCASION = "greeting_occasion",
  GREETING_ID = "greeting_id",
  LANGUAGE = "language",
  GREETING_CUSTOMIZATION = "greeting_customization",
  MEDIA = "media",
  MESSAGE = "message",
  PERSONALIZATION_TYPE = "personalization_type",
  NUMBERS_OF_RECIPIENTS = "number_of_recipients",
  TOTAL_CARDS = "total_cards",
  TOTAL_VALUE = "value",
  TOTAL_TO_PAY = "total_to_pay",
  DELIVERY_TIME = "delivery_time",
  PERSONALIZATION = "personalization",
  PAYMENT_METHOD = "payment_method",
  AT_WORK_COMPANY_NAME = "@work_company_name",
  CUSTOMIZATION = "customization",
  TYPE = "type",
}

export enum TRACKING_VALUES {
  PURPLE = "Purple",
  CUSTOM = "Custom",
  SINGLE = "Single",
  EMAIL_OR_SMS = "Email / SMS",
  PDF = "PDF",
  YES = "Yes",
  SKIP = "Skip",
  PHOTO = "Photo",
  VIDEO = "Video",
  GIF = "Gif",
  STANDARD = "Standard",
  ANIMATION = "Animation",
  FULL_PERSONALIZATION = "Full Personalization",
  FULL = "Full",
  INSTANT = "Instant",
  DONE = "Done",
  EN = "English",
  AR = "Arabic"
}