import { COMPONENT_ID } from "@constants/common";
import Button from "@features/common/button/Button";
import { useTranslation } from "next-i18next";
import React from "react";
import styles from "./VisitWebOrApp.module.scss";

export default function VisitWebOrApp({
  onContinue,
}: {
  onContinue: (id: string) => void;
}) {
  const { t } = useTranslation("common");
  return (
    <div className={styles["confirm-account"]}>
      <Button
        action={() => onContinue(COMPONENT_ID.LANDING)}
        wrapperClassName={styles["confirm-account__login-btn-wrapper"]}
        className={styles["confirm-account__login-btn"]}
        theme="at-work-primary"
      >
        {t("done")}
      </Button>
      <Button
        action={() => onContinue(COMPONENT_ID.GIFT_TYPE)}
        wrapperClassName={styles["confirm-account__signup-btn-wrapper"]}
        className={styles["confirm-account__signup-btn"]}
        theme="at-work-secondary"
      >
        {t("continueHere")}
      </Button>
    </div>
  );
}
