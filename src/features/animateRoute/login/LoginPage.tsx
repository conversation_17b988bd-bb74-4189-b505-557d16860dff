'use client'
import React, { useState } from "react";
import styles from "./LoginPage.module.scss";
import AccountConfirmation from "./accountConfirmation/AccountConfirmation";
import CreateAccount from "./createAccount/CreateAccount";
import AccountLogin from "./accountLogin/AccountLogin";

const LoginPage = () => {
    const [isAccountConfirmed, setIsAccountConfirmed]= useState <boolean | null> (null);
  const handleContinue = async (val:boolean) => {
    setIsAccountConfirmed(val)
  };
  return (
    <div className={styles["login-page"]}>
     {isAccountConfirmed === null && <AccountConfirmation handleContinue={handleContinue} />}
     {isAccountConfirmed === true && <AccountLogin/>}
     {isAccountConfirmed === false && <CreateAccount/>}
    </div>
  );
};

export default LoginPage;
