import { useMutation, useQuery } from "@apollo/client";
import {
  CREATE_PROFILE_QUERY,
  GENERATE_LOGIN_OTP_QUERY,
  GENERATE_SIGNUP_OTP_QUERY,
  LOGIN_OTP_VERIFY_QUERY,
  LOGOUT_USER_QUERY,
  SIGNUP_OTP_VERIFY_QUERY,
  VALIDATE_BUSINESS_USER_QUERY,
  VALIDATE_USER_QUERY
} from "./auth.query";

const useAuthAPI = () => {
  const [
    generateSignUpOtp,
    {
      data: generateSignUpOtpData,
      loading: generateSignUpOtpDataLoading,
      error: generateSignUpOtpDataError
    }
  ] = useMutation(GENERATE_SIGNUP_OTP_QUERY);

  const [
    verifySignUpOtp,
    {
      data: verifySignUpOtpData,
      loading: verifySignUpOtpDataLoading,
      error: verifySignUpOtpDataError
    }
  ] = useMutation(SIGNUP_OTP_VERIFY_QUERY);

  const [
    generateLogInOtp,
    {
      data: generateLogInOtpData,
      loading: generateLogInOtpDataLoading,
      error: generateLogInOtpDataError
    }
  ] = useMutation(GENERATE_LOGIN_OTP_QUERY);

  const [
    verifyLogInOtp,
    {
      data: verifyLogInOtpData,
      loading: verifyLogInOtpDataLoading,
      error: verifyLogInOtpDataError
    }
  ] = useMutation(LOGIN_OTP_VERIFY_QUERY);

  const [
    createProfile,
    {
      data: createProfileData,
      loading: createProfileDataLoading,
      error: createProfileDataError
    }
  ] = useMutation(CREATE_PROFILE_QUERY);

  const [validateUserExists, {
    data: validateUserData
  }] = useMutation(VALIDATE_USER_QUERY);

  const [logoutUser, {
    data: logoutUserData,
    loading: logoutUserLoading,
    error: logoutUserError
  }] = useMutation(LOGOUT_USER_QUERY);

  const [validateBusinessUserExists, {
    data: validateBusinessUserData
  }] = useMutation(VALIDATE_BUSINESS_USER_QUERY);

  return {
    generateSignUpOtpData,
    generateLogInOtpData,
    generateSignUpOtp,
    verifySignUpOtp,
    generateLogInOtp,
    verifyLogInOtp,
    createProfile,
    verifyLogInOtpDataLoading,
    verifySignUpOtpDataLoading,
    createProfileData,
    validateUserExists,
    validateUserData,
    createProfileDataLoading,
    logoutUserData,
    logoutUser,
    logoutUserLoading,
    validateBusinessUserExists,
    validateBusinessUserData
  };
};

export default useAuthAPI;
