"use client";
import React from "react";
import styles from "./AccountConfirmation.module.scss";
import Button from "@features/common/button/Button";
import { useTranslation } from "next-i18next";
import { COMPONENT_ID } from "@constants/common";
import Chatbot from "@features/common/chatbot/Chatbot";

const AccountConfirmation = ({ onContinue }: any) => {
  const { t } = useTranslation("common");

  return (
    <>
      <div className={styles["chat-bot-wrapper"]}>
        <Chatbot />
      </div>
      <div className={styles["confirm-account"]}>
        <Button
          action={() => onContinue(COMPONENT_ID.VISIT_WEB_OR_APP)}
          wrapperClassName={styles["confirm-account__login-btn-wrapper"]}
          className={styles["confirm-account__login-btn"]}
          theme="at-work-primary"
        >
          {t("iHaveAccount")}
        </Button>
        <Button
          action={() => onContinue(COMPONENT_ID.GIFT_TYPE)}
          wrapperClassName={styles["confirm-account__signup-btn-wrapper"]}
          className={styles["confirm-account__signup-btn"]}
          theme="at-work-secondary"
        >
          {t("iDontHaveAccount")}
        </Button>
      </div>
    </>
  );
};

export default AccountConfirmation;
