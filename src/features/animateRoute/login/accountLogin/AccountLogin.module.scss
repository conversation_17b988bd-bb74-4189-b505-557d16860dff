@import "@styles/variables";
@import "/src/styles/mixins";

.login-container {
  height: 100%;
  position: relative;

  .work-otp {
    display: flex;
    gap: 8px;
    flex-direction: column;

    &__title {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
      margin: 16px 0 0;
    }

    .otp-input {
      width: 50px !important;
      height: 50px;
      border-radius: 12px;
      border: 1px solid $semi-dark-grey1;
      background-color: white;
      user-select: none;
      color: #0e0f0c;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
      &::selection {
        background-color: transparent;
      }

      &:focus {
        border-color: 1px solid $dark-charcoal;
        outline: 2px solid $dark-charcoal;
      }
    }

    .otp-container {
      display: flex;
      // gap: 8px;
      justify-content: space-between;
    }

    .otp-btn {
      width: 100%;
    }

    .resend-btn {
      padding: 0;
      color: $dark-charcoal;
      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      letter-spacing: -0.14px;
    }

    &__resend {
      text-align: center;
      margin-top: 8px;
      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      letter-spacing: -0.14px;
    }
  }

  .otp-error {
    margin: 0;
    color: $mild-red;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: -0.12px;
  }

  &__btn-wrap {
    position: absolute;
    width: 100%;
    bottom: 0;
  }
}
