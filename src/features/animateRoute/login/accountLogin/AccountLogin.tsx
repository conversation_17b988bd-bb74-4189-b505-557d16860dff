import React, { useCallback, useRef, useState } from "react";
import styles from "./AccountLogin.module.scss";
import {
  CircularProgress,
  FormControl,
  FormHelperText,
  InputAdornment,
  TextField,
} from "@mui/material";
import { useTranslation } from "next-i18next";
import { imageBaseUrl } from "@constants/envVariables";
import { debounce } from "lodash";
import { isValidLoginEmailAddress } from "@utils/emailValidation";
import useApp, { AppContextAction } from "@features/common/common.context";
import Button from "@features/common/button/Button";
import IntervalTimer from "@features/common/intervalTimer/IntervalTimer";
import {
  AUTH_SESSION_EXPIRED,
  BASE_PATH,
  COMPONENT_ID,
  PLATFORM_TYPE,
  REWARDS_USER_EXIST,
  STATUS_USER_EXIST,
  WORKPAGE_URLS,
} from "@constants/common";
import OTPInput from "@features/common/otpTextField/OtpTextField";
import useAuth<PERSON>I from "../authAPI";
import { setShowSnackBarError } from "@features/common/commonSlice";
import { useAppDispatch } from "@redux/hooks";
import { useRouter } from "next/navigation";

const AccountLogin = ({ onContinue, localeWithRegion }: any) => {
  const appDispatch = useAppDispatch();
  const [email, setEmail] = useState("");
  const [showOtp, setShowOtp] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [otpVal, setOtpVal] = useState("");
  const [otpErrorMsg, setOtpErrorMsg] = useState("");
  const [userExists, setUserExists] = useState(false);
  const [otpError, setOtpError] = useState("");
  const [throttledUpto, setThrottledUpto] = useState(60);
  const [isOTPLoaderOn, showOTPLoader] = useState(false);
  const [showError, setShowError] = useState(false);
  const [authToken, setAuthToken] = useState<any>(null);
  const router = useRouter();
  const emailMaxLength = 256;
  const { t } = useTranslation("common");
  const emailIcon = `${imageBaseUrl}/icons/icon-email.svg`;
  const editIcon = `${imageBaseUrl}/icons/edit-black.svg`;

  const { state, dispatch } = useApp();
  const {
    validateBusinessUserExists,
    validateUserExists,
    generateLogInOtp,
    generateSignUpOtp,
    verifyLogInOtp,
    verifySignUpOtp,
    generateSignUpOtpData,
    generateLogInOtpData,
    validateUserData,
    validateBusinessUserData,
    verifyLogInOtpDataLoading,
    verifySignUpOtpDataLoading,
  } = useAuthAPI();
  const storeCode = state?.activeRegion?.code || "";
  const countryCode = state?.activeRegion?.country?.code || "";
  const context = {
    clientName: "at-work",
    headers: {
      "access-locale": storeCode,
      "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
    },
    credentials: "include",
  };

  const otpGenerateAuthSession = !userExists
    ? generateSignUpOtpData?.signupGenerateOtp?.data?.authSession
    : generateLogInOtpData?.loginGenerateOtp?.data?.authSession;

  const validateError =
    validateUserData?.validateUserExists?.errors[0]?.message;

  const validateBusinessError =
    validateBusinessUserData?.validateBusinessUserExists?.errors[0]
      ?.message;

  const handleEditEmail = () => {
    dispatch({
      type: AppContextAction.LOGIN,
      payload: {
        ...state?.login,
        email: "",
      },
    });
    // @ts-ignore
    inputRef?.current && inputRef.current.focus();
    setShowOtp(false);
    setButtonDisabled(false);
    setOtpVal("");
    setOtpErrorMsg("");
    setUserExists(false);
    setOtpError("");
  };

  const handleChange = (event: any) => {
    const inputValue = String(event?.target?.value)
      .trim()
      .replace(/[<>]/g, "");
    setEmail(inputValue);

    validateEmail(inputValue);
  };

  const validateEmail = useCallback(
    debounce((inputValue) => {
      if (!isValidLoginEmailAddress(inputValue)) {
        setEmailError(t("businessEmail"));
      } else {
        setEmailError("");
      }
    }, 500),
    [],
  );

  const handleSendOtp = async () => {
    if (emailError) {
      setShowError(true);
      return;
    }
    try {
      showOTPLoader(true);
      setButtonDisabled(true);
      let validateUser, statusCode;
      validateUser = await validateUserExists({
        context: context,
        variables: { input: { email: email } },
      });
      statusCode = validateUser?.data?.validateUserExists?.data?.status;
      const isExistingUser = statusCode === STATUS_USER_EXIST;
      setUserExists(isExistingUser);

      const response = await generateOtp(isExistingUser);
      const generateOtpErrors =
        response?.data?.signupGenerateOtp?.errors[0]?.message ||
        response?.data?.loginGenerateOtp?.errors[0]?.message;
      showOTPLoader(false);
      if (generateOtpErrors) {
        setOtpError(generateOtpErrors);
        setButtonDisabled(false);
        return;
      }
      setOtpError("");
      setShowOtp(true);
      showOTPLoader(false);
    } catch (err: any) {
      setButtonDisabled(false);
      showOTPLoader(false);

      console.error("Error sending OTP:", err);
      appDispatch(
        setShowSnackBarError({
          show: true,
          message: err?.message || t("somethingWrong"),
        }),
      );
    }
  };

  const generateOtp = async (isExistingUser = false) => {
    const variables = {
      input: {
        email: email,
        countryCode: countryCode.toUpperCase(),
        languageCode: state?.localeCode,
      },
    };

    if (isExistingUser) {
      const response = await generateLogInOtp({
        context: context,
        variables: variables,
      });
      return response;
    } else {
      const response = await generateSignUpOtp({
        context: context,
        variables: variables,
      });
      return response;
    }
  };

  const verifyOtp = async (isExistingUser = false) => {
    const variables = {
      input: { authSession: otpGenerateAuthSession, otp: otpVal },
    };

    if (isExistingUser) {
      const response = await verifyLogInOtp({
        context: context,
        variables: variables,
      });
      const authTokens = (response as any)?.data?.loginVerifyOtp?.data
        ?.authTokens;
      setAuthToken(authTokens);
      return response;
    } else {
      const response = await verifySignUpOtp({
        context: context,
        variables: variables,
      });
      return response;
    }
  };

  const handleVerify = async () => {
    try {
      const response = await verifyOtp(userExists);

      const verifyOtpErrors =
        response?.data?.signupVerifyOtp?.errors[0]?.message ||
        response?.data?.loginVerifyOtp?.errors[0]?.message;

      const authSession = !userExists
        ? response?.data?.signupVerifyOtp?.data?.authSession
        : response?.data?.loginVerifyOtp?.data?.authSession;

      const verifyOtpErrorCode =
        response?.data?.signupVerifyOtp?.errors[0]?.code ||
        response?.data?.loginVerifyOtp?.errors[0]?.code;

      // @ts-ignore
      if (verifyOtpErrorCode === AUTH_SESSION_EXPIRED) {
        appDispatch(
          setShowSnackBarError({
            show: true,
            message: verifyOtpErrors || t("somethingWrong"),
          }),
        );
        handleEditEmail();
        return;
      }
      if (verifyOtpErrors) {
        setOtpErrorMsg(verifyOtpErrors);
        return;
      }

      dispatch({
        type: AppContextAction.LOGIN,
        payload: {
          email: email,
          authSessionToken: authSession,
        },
      });

      if (!userExists) {
        onContinue(COMPONENT_ID.CREATE_ACCOUNT);
      } else {
        window.location.href = `${window.location.origin}/${BASE_PATH}/${localeWithRegion}/cart/`;
      }
    } catch (err) {
      console.error("Error verifying OTP", err);
    }
  };

  const handleOtpChange = (otp: string) => {
    setOtpVal(otp);
    setOtpErrorMsg("");
  };

  const handleResendOtp = async () => {
    setOtpErrorMsg("");
    setThrottledUpto(60);
    await generateOtp(userExists);
  };

  const inputRef = useRef(null);
  const errorCases =
    (!!emailError && showError) ||
    otpError ||
    validateError ||
    validateBusinessError;

  return (
    <div className={styles["login-container"]}>
      <div
        className={`${styles["work-login"]} work-login${
          errorCases ? " work-login-error" : ""
        }`}
      >
        <FormControl>
          <TextField
            value={email}
            type="email"
            autoFocus
            variant="outlined"
            placeholder={t("enterWorkEmail")}
            inputRef={inputRef}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <img src={emailIcon} alt="email" />
                </InputAdornment>
              ),
              endAdornment: showOtp ? (
                <div onClick={handleEditEmail}>
                  <InputAdornment
                    position="end"
                    style={{ cursor: "pointer" }}
                  >
                    <img src={editIcon} alt="edit-icon" />
                  </InputAdornment>
                </div>
              ) : null,
              readOnly: showOtp,
            }}
            inputProps={{
              maxLength: emailMaxLength,
            }}
            onChange={handleChange}
          />
        </FormControl>
        <div className="login-error">
          {!!emailError && showError && (
            <FormHelperText>{emailError}</FormHelperText>
          )}
          {(otpError || validateError || validateBusinessError) && (
            <FormHelperText>
              {otpError || validateError || validateBusinessError}
            </FormHelperText>
          )}
        </div>
      </div>

      {showOtp && (
        <div className={styles["work-otp"]}>
          <h4 className={styles["work-otp__title"]}>
            {t("enterEmailOtp")}
          </h4>
          <OTPInput
            value={otpVal}
            placeholder={""}
            onChange={handleOtpChange}
            numInputs={6}
            inputType="number"
            inputStyle={styles["otp-input"]}
            containerStyle={styles["otp-container"]}
            renderInput={(props) => <input {...props} />}
          />
          {otpErrorMsg && (
            <p className={styles["otp-error"]}>{otpErrorMsg}</p>
          )}
          <div className={styles["work-otp__resend"]}>
            {throttledUpto !== 0 ? (
              <IntervalTimer
                onComplete={() => {
                  setThrottledUpto(0);
                }}
                startingValue={throttledUpto}
              />
            ) : (
              <Button
                theme="transparent"
                className={`${styles["resend-btn"]}`}
                action={handleResendOtp}
              >
                {t("resendOtp")}
              </Button>
            )}
          </div>
        </div>
      )}
      <div className={styles["login-container__btn-wrap"]}>
        <Button
          theme="at-work-primary"
          className={styles["otp-btn"]}
          action={showOtp ? handleVerify : handleSendOtp}
          isChatBot={true}
          attribues={{
            disabled:
              verifyLogInOtpDataLoading ||
              verifySignUpOtpDataLoading ||
              (showOtp ? otpVal?.length < 6 : !email || buttonDisabled),
          }}
          isFixedBottom={true}
        >
          {showOtp ? (
            t("verify")
          ) : isOTPLoaderOn ? (
            <CircularProgress
              style={{ color: "#fff", width: "25px", height: "25px" }}
            />
          ) : (
            t("continue")
          )}
        </Button>
      </div>
    </div>
  );
};

export default AccountLogin;
