import { gql } from "@apollo/client";

export const GENERATE_SIGNUP_OTP_QUERY = gql`
  mutation SignupGenerateOtp($input: GenerateOTPMutationInput!) {
    signupGenerateOtp(input: $input) {
      data {
        status
        message
        authSession
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const SIGNUP_OTP_VERIFY_QUERY = gql`
  mutation SignupVerifyOtp($input: VerificationOTPMutationInput!) {
    signupVerifyOtp(input: $input) {
      data {
        status
        message
        authSession
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const GENERATE_LOGIN_OTP_QUERY = gql`
  mutation LoginGenerateOtp($input: GenerateOTPMutationInput!) {
    loginGenerateOtp(input: $input) {
      data {
        status
        message
        authSession
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const LOGIN_OTP_VERIFY_QUERY = gql`
  mutation LoginVerifyOtp($input: VerificationOTPMutationInput!) {
    loginVerifyOtp(input: $input) {
      data {
        status
        message
        authTokens {
          accessToken
          refreshToken
        }
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const CREATE_PROFILE_QUERY = gql`
  mutation SignupFulfillment($input: AuthSignupCompletionMutationInput!) {
    signupFulfillment(input: $input) {
      data {
        status
        message
        authTokens {
          accessToken
          refreshToken
        }
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const VALIDATE_USER_QUERY = gql`
  mutation ValiddateUserExists($input: UserValidateByEmailMutationInput!) {
    validateUserExists(input: $input) {
      data {
        status
        message
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const VALIDATE_BUSINESS_USER_QUERY = gql`
  mutation ValidateBusinessUserExists($input: UserValidateByEmailMutationInput!) {
    validateBusinessUserExists(input: $input) {
      data {
        status
        message
        redirectUrl
      }
      errors {
        field
        message
        code
      }
    }
  }
`;

export const LOGOUT_USER_QUERY = gql`
mutation RevokeToken($input:AuthRefreshTokenInput) {
  revokeToken(input: $input) {
      data {
          status
          message
      }
      errors {
          field
          message
          code
          status
      }
  }
}
`;