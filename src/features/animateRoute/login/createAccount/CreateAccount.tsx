import { imageBaseUrl } from "@constants/envVariables";
import useApp from "@features/common/common.context";
import { WorkComponentProps } from "@interfaces/common.inteface";
import { useAppDispatch } from "@redux/hooks";
import GoogleTagManager from "@utils/googleTagManager";
import { useTranslation } from "next-i18next";
import { ChangeEvent, useCallback, useEffect, useState } from "react";
import { getCountryCallingCode } from "react-phone-number-input";
import useAuthAPI from "../authAPI";
import { fetchUserDetails } from "@features/common/commonAPI";
import {
  setShowSnackBarError,
  setTokenInfo,
} from "@features/common/commonSlice";
import {
  CT_EVENTS,
  CT_PROPERTIES,
  GTM_PROPERTIES,
} from "@constants/clevertap";
import {
  AUTH_SESSION_EXPIRED,
  BASE_PATH,
  COMPONENT_ID,
  PLATFORM_TYPE,
} from "@constants/common";
import { pushCleverTapEvent } from "@features/common/clevertap/clevertap.services";
import { isAlphabetRegex } from "@utils/isAlphabet";
import { debounce } from "lodash";
import { FormControl, InputAdornment, TextField } from "@mui/material";
import MobileNumberInput from "@features/common/mobileNumberInput/MobileNumberInput";
import styles from "./CreateAccount.module.scss";
import SuccessSnackbar from "@features/common/successSnackbar/SuccessSnackbar";
import Button from "@features/common/button/Button";

const CreateAccount = ({
  onContinue,
  onCancel,
  componentId,
  localeWithRegion,
}: WorkComponentProps) => {
  const { t } = useTranslation("common");
  const { state, dispatch } = useApp();
  const appDispatch = useAppDispatch();

  // #.Push data to GTM
  const { push: gtmPush } = GoogleTagManager();

  const storeCode = state?.activeRegion?.code || "";

  const profileIcon = `${imageBaseUrl}/icons/profile.svg`;
  const designationIcon = `${imageBaseUrl}/icons/designation.svg`;
  const companyIcon = `${imageBaseUrl}/icons/company.svg`;
  const departmentIcon = `${imageBaseUrl}/icons/department.svg`;
  const backButton = `${imageBaseUrl}/icons/revamp/back-btn.svg`;

  const maxCharLength = 84;
  const minCharLength = 2;

  const defaultDialCode = () => {
    const defaultCountryCode: any = state?.activeRegion?.country?.code;
    return `+${getCountryCallingCode(defaultCountryCode)}`;
  };

  const [formData, setFormData] = useState({
    name: "",
    designation: "",
    organization: "",
    department: "",
    recipientMobile: "",
    countryCode: state?.activeRegion?.node?.country?.code,
    dialCode: defaultDialCode(),
  });

  const [errors, setErrors] = useState({
    name: "",
    designation: "",
    organization: "",
    department: "",
    recipientMobile: "",
  });

  const [showErrors, setShowErrors] = useState(false);

  const { createProfile, createProfileDataLoading } = useAuthAPI();

  const [authToken, setAuthToken] = useState<any>(null);
  const { refetchUserDetails } = fetchUserDetails(
    authToken?.accessToken,
    storeCode,
  );

  useEffect(() => {
    if (authToken) {
      refetchUserDetails()
        .then((userDetails) => {
          appDispatch(
            setTokenInfo({
              AccessToken: authToken?.accessToken,
              RefreshToken: authToken?.refreshToken,
              userAttributes: userDetails?.data?.userDetails,
              isUserSignedIn: true,
              isGuestUser: false,
              isGuestEnabled: false,
            }),
          );
          // CleverTap Event for Singup
          window?.clevertap?.onUserLogin?.push({
            Site: {
              [CT_PROPERTIES.AT_WORK_EMAIL]: state?.login?.email,
              [CT_PROPERTIES.AT_WORK_COMPANY_NAME]: formData?.organization,
            },
          });

          const clevertapData = {
            [CT_PROPERTIES.AT_WORK_EMAIL]: state?.login?.email,
            [CT_PROPERTIES.AT_WORK_COMPANY_NAME]: formData?.organization,
            [CT_PROPERTIES.STORE]:
              state?.activeRegion?.node?.country?.code?.toUpperCase(),
            [CT_PROPERTIES.PLATFORM]: PLATFORM_TYPE.WEB.toUpperCase(),
          };
          pushCleverTapEvent(CT_EVENTS.AT_WORK_SIGN_UP, clevertapData);

          // #. GA4 Event
          const gtmData = {
            [GTM_PROPERTIES.AT_WORK_COMPANY_NAME]: formData?.organization,
          };

          gtmPush("@work_sign_up", {
            store: state?.activeRegion?.node?.country?.code?.toUpperCase(),
            items: [{ ...gtmData }],
          });

          // onLoginSuccess &&
          //   onLoginSuccess(userDetails?.data?.userDetails);
        })
        .catch((err) => {
          console.error("Error fetching user details:", err);
          appDispatch(
            setShowSnackBarError({
              show: true,
              message: err?.message || t("somethingWrong"),
            }),
          );
        });
    }
  }, [authToken]);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({ ...prev, [name]: value.trimStart() }));

    debouncedValidateField(name, value);
  };

  const validateField = (name: string, value: string) => {
    let error = "";
    if (!value.trim()) {
      error = t("fieldRequiredError");
    } else if (value.length < minCharLength) {
      error = t("fieldErrorMsg");
    } else if (name !== "organization" && !isAlphabetRegex.test(value)) {
      error = t("alphabetsOnly");
    }
    return error;
  };

  const debouncedValidateField = useCallback(
    debounce((name: string, value: string) => {
      const error = validateField(name, value);
      setErrors((prev) => ({ ...prev, [name]: error }));
    }, 500),
    [],
  );

  const handleContinue = async () => {
    setShowErrors(true);
    const { recipientMobile, countryCode, dialCode, ...rest } = formData;

    const allErrors = Object.fromEntries(
      Object.entries(rest).map(([key, value]) => [
        key,
        validateField(key, value),
      ]),
    ) as typeof errors;

    setErrors(allErrors);

    // Check if there are any errors
    const hasErrors = Object.values(allErrors).some(
      (error) => error !== "",
    );
    if (!hasErrors && !validatePhoneNumber) {
      const {
        name,
        designation,
        organization,
        department,
        recipientMobile,
        dialCode,
      } = formData;

      try {
        const response = await createProfile({
          context: {
            clientName: "at-work",
            headers: {
              "access-locale": storeCode,
              "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
            },
            credentials: "include",
          },
          variables: {
            input: {
              name,
              designation,
              organization,
              department,
              mdc: recipientMobile ? dialCode : "",
              phoneNumber: recipientMobile || "",
              authSession: state?.login?.authSessionToken,
            },
          },
        });

        const authTokens =
          response?.data?.signupFulfillment?.data?.authTokens;

        const isError =
          response?.data?.signupFulfillment?.errors?.length > 0;

        const createProfileError =
          response?.data?.signupFulfillment?.errors[0];

        if (createProfileError?.code === AUTH_SESSION_EXPIRED) {
          appDispatch(
            setShowSnackBarError({
              show: true,
              message: createProfileError?.message || t("somethingWrong"),
            }),
          );
          onCancel(COMPONENT_ID.LOGIN);
          return;
        }

        if (isError) {
          return;
        }

        setAuthToken(authTokens);
        window.location.href = `${window.location.origin}/${BASE_PATH}/${localeWithRegion}/cart/`;
      } catch (err: any) {
        console.error("create profile", err);
        appDispatch(
          setShowSnackBarError({
            show: true,
            message: err?.message || t("somethingWrong"),
          }),
        );
      }
    }
  };
  const [checkField, setcheckField] = useState<boolean>(false);
  const [validatePhoneNumber, setValidatePhoneNumber] = useState("");

  const onInputChange = (e: any) => {
    setFormData((prev) => ({
      ...prev,
      recipientMobile: e.target.value,
    }));
  };

  const onInputError = (val: any) => {
    setErrors((prev) => ({
      ...prev,
      recipientMobile: val?.recipientMobile,
    }));
  };

  const setDialCode = (countryCode: string, dialCode: string) => {
    setFormData((prev) => ({
      ...prev,
      recipientMobile: "",
      countryCode: countryCode,
      dialCode: dialCode,
    }));
  };

  // Only check required fields for button disable
  const requiredFields: Array<keyof typeof formData> = [
    "name",
    "designation",
    "organization",
    "department",
  ];
  const btnDisabled = requiredFields.some(
    (field) => !formData[field] || !formData[field].trim(),
  );

  return (
    <>
      <div className={`create-profile-work ${styles["create-profile"]}`}>
        <FormControl>
          <TextField
            name="name"
            value={formData.name}
            placeholder={t("fullName")}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <img src={profileIcon} alt="profile" />
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: maxCharLength,
            }}
            fullWidth
            onChange={handleChange}
          />
          {showErrors && errors?.name && (
            <p className={styles["error-text"]}>{errors?.name}</p>
          )}
        </FormControl>
        <FormControl className="user-phone-number">
          <MobileNumberInput
            mobile={formData.recipientMobile}
            onInputChange={onInputChange}
            onInputError={onInputError}
            fieldRequired={false}
            checkField={checkField}
            setcheckField={setcheckField}
            setDialCode={setDialCode}
            dialCode={formData.dialCode}
            countryCode={formData.countryCode}
            validatePhoneNumber={""}
            setValidatePhoneNumber={setValidatePhoneNumber}
            setRecipient={setFormData}
            isRecipient={false}
          />
          {showErrors && validatePhoneNumber && (
            <p className={styles["error-text"]}>{validatePhoneNumber}</p>
          )}
        </FormControl>
        <FormControl>
          <TextField
            name="designation"
            value={formData.designation}
            placeholder={t("designation")}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <img src={designationIcon} alt="designation" />
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: maxCharLength,
            }}
            fullWidth
            onChange={handleChange}
          />
          {showErrors && errors.designation && (
            <p className={styles["error-text"]}>{errors.designation}</p>
          )}
        </FormControl>
        <FormControl>
          <TextField
            name="organization"
            value={formData.organization}
            placeholder={t("company")}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <img src={companyIcon} alt="company" />
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: maxCharLength,
            }}
            fullWidth
            onChange={handleChange}
          />
          {showErrors && errors?.organization && (
            <p className={styles["error-text"]}>{errors?.organization}</p>
          )}

          <div className={styles["info-cont"]}>
            <img
              width={17}
              height={17}
              src={`${imageBaseUrl}/icons/info-circle-black-icon.svg`}
              alt={"info-image"}
            />
            <p className={styles["info-text"]}>
              {t("enterCompanyLegalName")}
            </p>
          </div>
        </FormControl>
        <FormControl>
          <TextField
            name="department"
            value={formData.department}
            placeholder={t("department")}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <img src={departmentIcon} alt="department" />
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: maxCharLength,
            }}
            fullWidth
            onChange={handleChange}
          />
          {showErrors && errors.department && (
            <p className={styles["error-text"]}>{errors.department}</p>
          )}
        </FormControl>
        <Button
          attribues={{ disabled: btnDisabled || createProfileDataLoading }}
          className={styles["continue-btn"]}
          isChatBot={true}
          theme="at-work-primary"
          isFixedBottom={true}
          action={handleContinue}
        >
          {t("continue")}
        </Button>
      </div>
      <SuccessSnackbar showError={true} />
    </>
  );
};

export default CreateAccount;
