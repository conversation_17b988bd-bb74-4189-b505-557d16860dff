@import "@styles/mixins";
@import "@styles/variables";

.gift-types-container {
  position: relative;

  .types-wrapper {
    display: flex;
    width: min-content;
    gap: 16px;
    min-height: 340px;
  }

  .continue-btn-wrapper {
    top: 336px;
    width: 243px;

    @include rtl-styles {
      top: 366px;
    }
  }

  .continue-btn {
    width: 243px;
  }
}

.details-loading {
  display: flex;
  width: min-content;
  gap: 16px;

  .loader {
    display: flex;
    flex-direction: column;
    gap: 8px;
    border: 2px solid rgba(14, 15, 12, 0.2);
    border-radius: 16px;
    padding: 12px 12px 24px;
  }
}

.mobile-card-note {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;

  &__text-box {
    margin-inline-start: 66px;
    display: flex;
    padding: 12px 12px 12px 16px;
    align-items: center;
    gap: 16px;
    border-radius: 16px 16px 16px 0;
    background: #f7f7f7;
    color: $review-text-grey;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: -0.12px;
    margin-top: -16px;
  }
}
