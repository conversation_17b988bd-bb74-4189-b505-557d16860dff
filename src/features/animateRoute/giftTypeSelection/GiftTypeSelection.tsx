import React, { useEffect, useRef, useState } from "react";
import styles from "./GiftTypeSelection.module.scss";
import TypeCard from "./typeCard/TypeCard";
import { Swiper, SwiperSlide } from "swiper/react";
import SwiperCore from "swiper";
import useGifTypeAPI from "./giftTypeAPI";
import useApp, { AppContextAction } from "@features/common/common.context";
import { BRAND_CARD_TYPE, BRAND_FORM_STATE } from "@constants/common";
import { Skeleton } from "@mui/material";
import { useTranslation } from "next-i18next";

const GiftTypeSelection = ({ onContinue }: any) => {
  const { t } = useTranslation("common");

  const [selectedGiftType, setSelectedGiftType] = useState("");

  const handleSwiper = (swiper: SwiperCore) => {
    swiperRef.current = swiper;
  };

  const swiperRef = useRef<SwiperCore | null>(null);

  const {
    state,
    state: { localeCode, activeRegion },
    dispatch,
  } = useApp();

  const { fetchLandingDetails } = useGifTypeAPI();
  const { landingDetailsData, landingDetailsLoading } =
    fetchLandingDetails(activeRegion?.code);

  const giftTypes = landingDetailsData?.landingDetails?.map(
    (card: any) => ({
      type: card?.cardType,
      title: card?.title,
      image: card?.cardImage || card?.brand?.brandImageData,
      hasInfo: card?.cardType !== BRAND_CARD_TYPE.NON_GENERIC,
      slug: card?.brand?.slug,
      isOffer: card?.brand?.isOffer,
      description: card?.description,
    }),
  );

  const handleGiftTypeSelection = (e: any, selectedGift: any) => {
    if (selectedGift?.type !== BRAND_CARD_TYPE.HAPPY_YOU_NORMAL) {
      return;
    }

    if (state?.card?.formState === BRAND_FORM_STATE.CREATE) {
      dispatch({
        type: AppContextAction.RESET_CARD_DATA,
      });
    }

    dispatch({
      type: AppContextAction.GIFT_TYPE,
      payload: {
        value: selectedGift?.type,
        name: selectedGift?.title,
        slug: selectedGift?.slug,
        image: selectedGift?.image,
      },
    });

    dispatch({
      type: AppContextAction.BRAND_INFO,
      payload: {
        brandImage: selectedGift?.image,
        brandSlug: selectedGift?.slug,
      },
    });
    setSelectedGiftType(selectedGift?.type);
  };

  useEffect(() => {
    if (
      selectedGiftType &&
      selectedGiftType === state?.giftTypeSelection?.value
    ) {
      onContinue();
    }
  }, [selectedGiftType, state?.giftTypeSelection?.value]);

  return (
    <>
      <div className={styles["mobile-card-note"]}>
        <p className={styles["mobile-card-note__text-box"]}>
          {t("mobileVersionIsLimited")}
        </p>
      </div>

      {!landingDetailsLoading ? (
        <Swiper
          spaceBetween={16}
          slidesPerView={"auto"}
          centeredSlides={false}
          centeredSlidesBounds={true}
          navigation
          className={`gift-type-swiper ${styles["gift-type-swiper"]}`}
          onSwiper={handleSwiper}
        >
          {giftTypes?.map((item: any, index: number) => (
            <SwiperSlide key={index} style={{ width: "228px" }}>
              <TypeCard
                giftType={item}
                key={index}
                handleGiftTypeSelection={handleGiftTypeSelection}
                locale={localeCode}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      ) : (
        <div className={`${styles["details-loading"]}`}>
          {[1, 2].map((items, key) => (
            <div className={styles["loader"]} key={key}>
              <Skeleton variant="rectangular" width={212} height={138} />
              <Skeleton height={30} width={150} />
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default GiftTypeSelection;
