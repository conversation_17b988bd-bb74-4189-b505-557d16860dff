import {
  LANDING_QUERY,

} from "./giftType.query";
import { useQuery } from "@apollo/client";
import { PLATFORM_TYPE } from "@constants/common";
import * as Sentry from "@sentry/nextjs"
interface BrandAPI {
  personalisation?: {
    onSuccess: (response: PersonalisedSuccessResponce) => void;
    onError: (error: PersonalisedErrorResponce) => void;
  };
}

export interface PersonalisedSuccessResponce {
  createModel: {
    referenceId: string;
  };
}

export interface PersonalisedErrorResponce {
  status: string;
  error: {
    code: string;
    message: string;
  };
}

const useGifTypeAPI = (params?: BrandAPI) => {

  const fetchLandingDetails = (storeCode: any) => {
    const {
      loading: landingDetailsLoading,
      error: landingDetailsError,
      data: landingDetailsData,
      refetch: refetchLandingDetails
    } = useQuery<any>(LANDING_QUERY, {
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": storeCode,
          "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
        },
      },
      fetchPolicy: "no-cache",
      skip: !storeCode
    });

    if (landingDetailsError) {
      Sentry.captureException(`Error in fetchLandingDetails: ${landingDetailsError}`, {
        tags: { location: "GiftTypeSelection" },
      });
      console.log("Error in fetchLandingDetails:", landingDetailsError);
    }

    return {
      landingDetailsLoading,
      landingDetailsError,
      landingDetailsData,
      refetchLandingDetails,
    };
  };
  return {
    fetchLandingDetails
  };
};

export default useGifTypeAPI
