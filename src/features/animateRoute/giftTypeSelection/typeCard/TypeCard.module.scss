@import "@styles/variables";
@import "@styles/mixins";

.type-card-container {
  border-radius: 16px;
  padding: 8px 8px 24px 8px;
  width: 210px;
  position: relative;
  cursor: pointer;
  height: min-content;
  transition: transform 0.3s ease-in-out;
  z-index: 0;
  border: 1px solid transparent;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -2;
    border-radius: inherit;
    background: linear-gradient(to bottom right, #0e0f0ca3, #cbc9c959);
  }

  &::after {
    content: "";
    position: absolute;
    inset: 1px;
    z-index: -1;
    border-radius: 15px;
    background: #fff;
  }

  .description-txt {
    margin-top: 16px;
    color: $review-text-grey;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;

    span {
      unicode-bidi: plaintext;
    }

    @include rtl-styles {
      font-family: var(--font-noto-kufi);
    }
  }

  .available-on-desktop {
    display: flex;
    padding: 4px 8px;
    justify-content: center;
    align-items: center;
    border-radius: 40px;
    background: rgba(255, 119, 0, 0.07);
    width: 158px;
    margin-top: 24px;

    p {
      margin: 0;
      color: #f70;
      text-align: center;
      font-size: 10px;
      font-style: normal;
      font-weight: 700;
      line-height: 8px;
      letter-spacing: -0.1px;
      text-transform: uppercase;

      @include rtl-styles {
        font-family: var(--font-noto-kufi);
      }
    }
  }

  .title {
    color: var(--dark-charcoal);
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    margin: 0;

    @include rtl-styles {
      font-family: var(--font-noto-kufi);
    }
  }

  .card-img {
    position: relative;
    display: flex;
    justify-content: center;
  }

  .content {
    display: flex;
    justify-content: space-between;
    margin: 16px 0px 0px 0px;
  }

  .image {
    border-radius: 12px;
  }

  &__extra {
    padding-bottom: 49px;

    @include rtl-styles {
      padding-bottom: 63px;
    }
  }
}
