import React from "react";
import styles from "./TypeCard.module.scss";
import Image from "next/image";
import { useTranslation } from "next-i18next";
import Marquee from "react-fast-marquee";
import { BRAND_CARD_TYPE } from "@constants/common";

interface TypeCardInterface {
  giftType: {
    type: string;
    title: string;
    image: string;
    hasInfo: boolean;
    slug: string;
    isOffer: boolean;
    description: string;
  };
  handleGiftTypeSelection: (e: any, type: any) => void;
  locale: string;
}

const TypeCard: React.FC<TypeCardInterface> = ({
  giftType,
  handleGiftTypeSelection,
  locale,
}) => {
  const { t } = useTranslation("common");

  return (
    <div
      onClick={(e) => handleGiftTypeSelection(e, giftType)}
      className={`${styles["type-card-container"]} ${giftType.type === BRAND_CARD_TYPE.HAPPY_YOU_NORMAL ? styles["type-card-container__extra"] : ""}`}
    >
      <div className={styles["card-img"]}>
        <Image
          id="gift-type"
          width={212}
          height={138.2}
          className={styles["image"]}
          src={giftType?.image}
          alt={giftType?.title}
        />
      </div>
      <div className={styles["content"]}>
          <h5 className={styles["title"]}>{giftType?.title}</h5>
      </div>
      <div
        className={styles["description-txt"]}
        dangerouslySetInnerHTML={{ __html: giftType?.description }}
      ></div>
      {giftType?.type !== BRAND_CARD_TYPE?.HAPPY_YOU_NORMAL && (
        <div className={styles["available-on-desktop"]}>
          <p>{t("availableOnDesktop")}</p>
        </div>
      )}
    </div>
  );
};

export default TypeCard;
