import React from "react";
import styles from "./Tile.module.scss";
import Image from "next/image";
import { useTranslation } from "next-i18next";
import ChatbotList from "../chatbotList/ChatbotList";
import BulkDelivery from "../bulkDelivery/BulkDelivery";
import { imageBaseUrl } from "@constants/envVariables";

interface TileInterface {
  info: {
    title: string;
    description: string;
    image: string;
    imageWidth: number;
    imageHeight: number;
    key: string;
  };
  onContinue?: () => void;
  activeAgents?: any[];
  chatbotImage?: string;
}

const Tile: React.FC<TileInterface> = ({
  info,
  onContinue,
  activeAgents,
  chatbotImage,
}) => {
  const { t } = useTranslation("common");
  let locale = "en"; // Default locale, replace with actual locale
  return (
    <div
      className={info.key === "assisted-sales" ? "" : styles["landing-tile"]}
    >
      <div className={styles["tile-container"]}>
        <div className={styles["top"]}>
          <h2
            className={`${
              info?.title === "payByBank" ||
              (info?.title === "vatInvoice" && locale === "ar")
                ? styles["no-word-space"]
                : ""
            } ${styles["title"]}`}
          >
            {t(info?.title)}
          </h2>
        </div>
        <p
          className={`${styles["description"]} ${
            info.key === "assisted-sales" && styles["sales-description"]
          }`}
          dangerouslySetInnerHTML={{ __html: t(info?.description) }}
        ></p>
        {info.key === "assisted-sales" && <ChatbotList />}
        {info.key === "bulk-order" && <BulkDelivery />}
        {info.key === "custom-design" && info.image && (
          <Image
            width={info?.imageWidth}
            height={info?.imageHeight}
            className={styles["image"]}
            src={`${imageBaseUrl}${info?.image}`}
            alt={info?.title}
          />
        )}
      </div>
    </div>
  );
};

export default Tile;
