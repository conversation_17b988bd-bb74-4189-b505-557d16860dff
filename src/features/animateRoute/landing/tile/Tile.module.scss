@import "/src/styles/variables";
@import "@styles/mixins";

.landing-tile {
  height: 100%;
}

.tile-container {
  background: transparent;
  padding: 24px 24px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 12px;
  height: 100%;
  border-radius: 24px;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 24px;
    padding: 1px;
    background: linear-gradient(135deg, #99c6ff, #fff, #99c6ff);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    z-index: 1;
  }

  .top {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .title {
    // width: 167px;
    color: #0e0f0c;
    font-family: $bricolage-font-family;
    font-optical-sizing: none;
    font-size: 24px;
    font-weight: 800;
    line-height: 32px;
    margin: 0;

    &.no-word-space {
      word-spacing: 0;
    }
  }

  .description {
    color: #868785;
    font-family: $default-font-family;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;
    margin: 0;

    span {
      font-size: 10px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: -0.1px;
      margin-top: 6px;
      display: block;
    }

    @include rtl-styles {
      font-family: $arabic-font-family;
    }
  }

  .sales-description {
   width: calc(100% - 30px);
  }

  .custom-image {
    margin: 0 auto;
  }
}
