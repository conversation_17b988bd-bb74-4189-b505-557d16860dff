@import '@styles/variables';
@import '@styles/mixins';

.bulk-delivery {
    position: relative;
    width: 100%;
    height: 100%;
    padding-inline-start: 24px;
    padding-top: 30px;

    @include rtl-styles {
        padding-top: 0;
    }

    &__grey-box {
        padding: 15px;
        background-color: $grey-primary;
        border-radius: 16px 16px 0px 0px;
        height: 150px;
        position: relative;

        &-text {
            color: $light-sky-blue;
            font-family: "Mona Sans";
            font-size: 11px;
            font-weight: 700;
            line-height: 18px;
            letter-spacing: -0.165px;
            text-align: center;
        }
    }

    &__send-circle {
        position: absolute;
        top: 0;
        right: 14px;
        border-radius: 100px;
        padding: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FFF;
        box-shadow: 0px 0px 1px 0px rgba(0, 113, 255, 0.10), 0px 4px 16px 0px rgba(0, 113, 255, 0.04); 

        @include rtl-styles {
            right: unset;
            left: 14px;
            transform: scaleX(-1);
        }
    }

    &__delivery {
        padding: 16px;
        border-radius: 16px;
        position: absolute;
        bottom: 22px;
        left: 0;
        width: 184px;
        height: 81px;
        background-color: $white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0px 0px 1px 0px rgba(0, 113, 255, 0.10), 0px 4px 16px 0px rgba(0, 113, 255, 0.04);
        gap: 8px;

        @include rtl-styles {
            left: unset;
            right: -15px;
        }

        &-text {
            font-family: "Mona Sans";
            font-size: 8px;
            font-weight: 600;
            line-height: 8px;
            letter-spacing: -0.08px;
            text-transform: uppercase;
            color: #868785;
        }

        &-icons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
    }

    
}