import React from 'react';
import styles from './BulkDelivery.module.scss';
import { useTranslation } from 'next-i18next';
import { imageBaseUrl } from '@constants/envVariables';


const BulkDelivery = () => {
  const { t } = useTranslation('common');
  const sendIcon = `${imageBaseUrl}/icons/send-blue.svg`;
  const mailIcon = `${imageBaseUrl}/icons/icon-mail.svg`;
  const smsIcon = `${imageBaseUrl}/icons/icon-sms.svg`;
  const pdfIcon = `${imageBaseUrl}/icons/icon-pdf.svg`;
  const whatsappIcon = `${imageBaseUrl}/icons/whatsapp.svg`;
  const deliveryIcons = [mailIcon, whatsappIcon, smsIcon, pdfIcon ];

  return (
    <div className={styles['bulk-delivery']}>
      <div className={styles['bulk-delivery__grey-box']}>
        <div className={styles['bulk-delivery__grey-box-text']}>
          {t('sendInBulk')}
        </div>
      </div>
      <div className={styles['bulk-delivery__send-circle']}>
        <img height={24} width={24} src={sendIcon} />
      </div>
      <div className={styles['bulk-delivery__delivery']}>
        <div className={styles['bulk-delivery__delivery-text']}>
          {t('deliveredVia')}
        </div>
        <div className={styles['bulk-delivery__delivery-icons']}>
          {deliveryIcons.map((icon, index) => (
            <img key={index} width={32} height={32} src={icon} alt="" />
          ))}
        </div>
      </div>
    </div>
  );
};

export default BulkDelivery;
