import React from "react";
import styles from "./Landing.module.scss";
import { AT_WORK_INFO_CARDS } from "mockData/index.data";
import Tile from "@features/animateRoute/landing/tile/Tile";
import Button from "@features/common/button/Button";
import { imageBaseUrl } from "@constants/envVariables";
import { useTranslation } from "next-i18next";
import useApp from "@features/common/common.context";
import { useAppSelector } from "@redux/hooks";
import { getTokenInfo } from "@features/common/commonSlice";

const Landing = ({ onContinue, componentId }: any) => {
  const { state } = useApp();
  const store = useAppSelector(getTokenInfo);
  const { t } = useTranslation("landing");
  const activeAgents: any = [];
  const chatbotImage = undefined;
  return (
    <div className={styles["landing-page"]}>
      {AT_WORK_INFO_CARDS.map((info, index) => (
        <Tile
          onContinue={onContinue}
          info={info}
          key={index}
          chatbotImage={chatbotImage}
          activeAgents={activeAgents}
        />
      ))}
      <Button
        action={onContinue}
        wrapperClassName={styles["start-btn-wrapper"]}
        className={styles["start-btn"]}
        isChatBot={true}
        theme="at-work-primary"
        isFixedBottom={true}
      >
        {t("getStartedAtWork")}
      </Button>
    </div>
  );
};

export default Landing;
