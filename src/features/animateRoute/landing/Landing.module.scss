@import '@styles/mixins';

.landing-page {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.start-btn-wrapper {
    margin-top: 24px;
}

.start-btn.start-btn {
height: 50px;
display: flex;
padding: 16px 32px;
flex-direction: column;
justify-content: center;
align-items: center;
gap: 10px;
align-self: stretch;
border-radius: 16px;
background: linear-gradient(180deg, #0E0F0C 15.69%, rgba(51, 51, 51, 0.80) 123.39%);

    span {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px;
        letter-spacing: -0.18px;
    }

    @include rtl-styles {
        img {
        transform: rotate(180deg);
        }
    }
}