'use client'
import React from 'react'
import styles from './ChatbotList.module.scss'
import useApp from '@features/common/common.context';

const ChatbotList = () => {
  const {
    state: { atWorkSiteConfig },
  } = useApp();
  const chatbotImage = atWorkSiteConfig?.chatbotDetails?.chatbotImage;
  const activeAgents = atWorkSiteConfig?.chatbotDetails?.activeAgents ||[];
  function getRandomColor() {
    let letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
  let agentsList: string[] = activeAgents || [];
  const missingAgentsCount =
  agentsList.length <= 5 ? 5 - agentsList.length : 0;
  const missingAgents = Array.from({ length: missingAgentsCount }, () =>
    getRandomColor(),
  );
  agentsList = [...agentsList, ...missingAgents];

  return (
    <div className={styles['bot-list']}>
      <div className={styles['bot-list__wrap']}>
        <div className={styles['bot-list__row']}>
          <img
            width={58}
            height={58}
            className={`${styles['bot-list__items']} ${styles['bot-list__active']}`}
            src={chatbotImage}
          />
        </div>

        <div className={styles['bot-list__row']}>
          {agentsList?.slice(0, 2).map((botImage, index) =>
            botImage.startsWith('#') ? (
              <div
                key={index}
                className={styles['bot-list__items']}
                style={{
                  width: '47px',
                  height: '47px',
                  backgroundColor: botImage,
                  borderRadius: '50%',
                }}
              />
            ) : (
              <img
                width={47}
                height={47}
                key={index}
                className={styles['bot-list__items']}
                src={botImage}
              />
            ),
          )}
        </div>

        <div className={styles['bot-list__row']}>
          {agentsList?.slice(2, 5).map((botImage, index) =>
            botImage.startsWith('#') ? (
              <div
                key={index}
                className={styles['bot-list__items']}
                style={{
                  width: '42px',
                  height: '42px',
                  backgroundColor: botImage,
                  borderRadius: '50%',
                }}
              />
            ) : (
              <img
                width={42}
                height={42}
                key={index}
                className={styles['bot-list__items']}
                src={botImage}
              />
            ),
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatbotList