import { gql } from "@apollo/client";

export const BRAND_SKIN_QUERY = gql`
  query BrandSkinImages($BrandSlug: String!) {
    brandSkinImages(brandSlug: $BrandSlug) {
      edges {
        node {
          cardImage
          cardImageWebp
          thumbnailImage
          thumbnailImageWebp
          skinCode
          occasion {
            code
            name
          }
        }
      }
    }
  }
`;

export const ALL_CUSTOM_CARDS_QUERY = gql`
  query MyQuery($first: String!, $after: String!) {
    allCustomHappyYouCards(first: $first, after: $after) {
      totalCount
      edges {
        cursor
        node {
          bgColor
          bgType
          bgImage
          cardImage
          isDefault
          logoImage
          referenceId
        }
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
    }
  }
`;

export const GENERIC_BRAND_LIST_QUERY = gql`
  query (
    $slug: String!
    $redemptionType: String!
    $offset: Int!
    $first: Int!
  ) {
    genericBrandLists(
      slug: $slug
      redemptionType: $redemptionType
      offset: $offset
      first: $first
    ) {
      totalCount
    }
  }
`;
