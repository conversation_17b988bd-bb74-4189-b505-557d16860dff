import { useQuery } from "@apollo/client";
import { BRAND_SKIN_QUERY, GENERIC_BRAND_LIST_QUERY } from "./brandList.query";
import { PLATFORM_TYPE } from "@constants/common";
import * as Sentry from "@sentry/nextjs";
import e from "cors";

const useBrandListAPI = (params?: any) => {
  const fetchBrandSkins = (storeCode: any, brandSlug: any, skip: boolean) => {
    const {
      loading: brandSkinsLoading,
      error: brandSkinsError,
      data: brandSkinsData,
    } = useQuery(BRAND_SKIN_QUERY, {
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": storeCode,
          "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
        },
      },
      variables: {
        BrandSlug: brandSlug,
      },
      fetchPolicy: "network-only",
      skip: skip,
    });

    if (brandSkinsError) {
      Sentry.captureException(`Error in fetchBrandSkins: ${brandSkinsError}`, {
        tags: { location: "BrandList" },
        extra: { BrandSlug: brandSlug },
      });
      console.log("Error in fetchBrandSkins:", brandSkinsError);
    }

    return {
      brandSkinsLoading,
      brandSkinsError,
      brandSkinsData,
    };
  };

  const fetchAllCustomHYC = (
    storeCode: string,
    token: string,
    first: string,
    after: string,
    skip: boolean,
  ) => {
    const {
      loading: customHYCLoading,
      error: customHYCError,
      data: customHYCData,
    } = useQuery(BRAND_SKIN_QUERY, {
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": storeCode,
          "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
          authorization: `JWT ${token}`,
        },
      },
      variables: {
        first: first,
        after: after,
      },
      fetchPolicy: "network-only",
      skip: skip,
    });

    if (customHYCError) {
      Sentry.captureException(`Error in fetchBrandSkins: ${customHYCError}`, {
        tags: { location: "custom hyc" },
      });
      console.log("Error in fetchBrandSkins:", customHYCError);
    }

    return {
      customHYCLoading,
      customHYCError,
      customHYCData,
    };
  };

  /**
   * @method fetchGenericBrandList
   * @description Fetch generic brand list items
   * @returns
   */
  const fetchGenericBrandList = (
    slug: string,
    redemptionType: string,
    offset: number,
    first: number,
  ) => {
    const {
      loading: genericBrandsLoading,
      error: genericBrandsError,
      data: genericBrandsData,
      fetchMore: fetchMoreGenericBrands,
    } = useQuery<any>(GENERIC_BRAND_LIST_QUERY, {
      variables: {
        slug,
        redemptionType,
        offset,
        first,
      },
      skip: !Boolean(slug) || !Boolean(redemptionType),
    });

    // #. Don not return data, must be take from the state method
    return {
      genericBrandsLoading,
      genericBrandsError,
      genericBrandsData,
      fetchMoreGenericBrands,
      fetchGenericBrandList,
    };
  };

  return {
    fetchBrandSkins,
    fetchAllCustomHYC,
    fetchGenericBrandList,
  };
};

export default useBrandListAPI;
