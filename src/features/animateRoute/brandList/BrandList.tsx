import {
  BRAND_CARD_TYPE,
  COMPONENT_ID,
  SELECTED_CARD_TYPE,
} from "@constants/common";
import AboutCard from "@features/common/aboutCard/AboutCard";
import useApp, { AppContextAction } from "@features/common/common.context";
import React, { useEffect } from "react";
import styles from "./BrandList.module.scss";
import useBrandListAPI from "./brandListAPI";
import CardComponent from "@features/common/cardComponent/CardComponent";
import CardComponentSkelton from "@features/common/contentLoader/cardComponentSkelton/CardComponentSkelton";
import { setShowSnackBarError } from "@features/common/commonSlice";
import { useAppDispatch } from "@redux/hooks";
import { useTranslation } from "next-i18next";
import SuccessSnackbar from "@features/common/successSnackbar/SuccessSnackbar";

const BrandList = ({ onContinue }: any) => {
  const {
    state,
    state: { activeRegion, giftTypeSelection, selectedCardType },
    dispatch
  } = useApp();

  const { t } = useTranslation("common");
  const { fetchBrandSkins, fetchAllCustomHYC } = useBrandListAPI();
  const appDispatch = useAppDispatch();

  const slug = giftTypeSelection?.slug;

  const { brandSkinsData, brandSkinsLoading, brandSkinsError } =
    fetchBrandSkins(
      activeRegion?.code,
      slug,
      selectedCardType === SELECTED_CARD_TYPE.CUSTOM,
    );

  const giftSelectionImage = giftTypeSelection?.image;
  const giftSelectionName = giftTypeSelection?.name;

  useEffect(() => {
    if (brandSkinsError) {
      appDispatch(
        setShowSnackBarError({
          show: true,
          message: brandSkinsError?.message || t("somethingWrong"),
        }),
      );
    }
  }, [brandSkinsError]);

  const handleSelectionWithoutSkin = (item: any) => {
    dispatch({
      type: AppContextAction.BRAND_LIST,
      payload: {
        skinData: {
          node: { cardImage: item?.image },
          occasion: { code: "", name: "" },
        },
        name: item?.name,
        slug: "",
      },
    });
    onContinue(COMPONENT_ID.BRAND_DENOMINATION);
  };

  const handleSelectBrandSkin = (item: any) => {
    dispatch({
      type: AppContextAction.BRAND_LIST,
      payload: {
        skinData: item,
        name: item?.node?.occasion?.name,
        slug: item?.node?.skinCode,
      },
    });
    onContinue(COMPONENT_ID.BRAND_DENOMINATION);
  };

  const cardData = brandSkinsData?.brandSkinImages?.edges;

  return (
    <>
      {!brandSkinsLoading ? (
        <div className={styles["brand-list"]}>
          <CardComponent
            cardImage={giftSelectionImage}
            cardName={giftSelectionName}
            onClick={() => handleSelectionWithoutSkin(giftTypeSelection)}
          />
          {cardData?.map((item: any, index: number) => {
            const cardImage = item?.node?.cardImage;
            return (
              <CardComponent
                key={index}
                cardImage={cardImage}
                cardName={giftSelectionName}
                onClick={() => handleSelectBrandSkin(item)}
              />
            );
          })}
          <AboutCard
            cardImage={giftSelectionImage}
            cardName={giftSelectionName}
            slug={slug}
            onContinue={onContinue}
          />
        </div>
      ) : (
        <CardComponentSkelton />
      )}
      <SuccessSnackbar showError={true} />
    </>
  );
};

export default BrandList;
