import { useQuery } from "@apollo/client";
import { PLATFORM_TYPE } from "@constants/common";
import * as Sentry from "@sentry/nextjs";
import { CHANGE_CART_TYPE_QUERY } from "./cardType.query";

const useCardTypeAPI = (params?: any) => {
  const fetchChangeCards = (storeCode: any, token: any) => {
    const {
      loading: changeCardsLoading,
      error: changeCardsError,
      data: changeCardsData,
    } = useQuery(CHANGE_CART_TYPE_QUERY, {
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": storeCode,
          "app-platform": PLATFORM_TYPE.MWEB.toLowerCase(),
          authorization: `JWT ${token}`,
        },
      },
      fetchPolicy: "network-only",
      skip: !Boolean(token),
    });

    if (changeCardsError) {
      Sentry.captureException(`Error in fetchBrandSkins: ${changeCardsError}`, {
        tags: { location: "Change Cards" },
      });
      console.log("Error in fetchBrandSkins:", changeCardsError);
    }

    return {
      changeCardsLoading,
      changeCardsError,
      changeCardsData,
    };
  };

  return { fetchChangeCards };
};
export default useCardTypeAPI;
