import React, { useEffect } from "react";
import styles from "./CardTypeSelection.module.scss";
import useCardTypeAPI from "./cardTypeAPI";
import AboutCard from "@features/common/aboutCard/AboutCard";
import CardComponent from "@features/common/cardComponent/CardComponent";
import { WorkComponentProps } from "@interfaces/common.inteface";
import { COMPONENT_ID, SELECTED_CARD_TYPE } from "@constants/common";
import useApp, { AppContextAction } from "@features/common/common.context";

const CardTypeSelection = ({
  componentId,
  onContinue,
  onCancel,
}: WorkComponentProps) => {
  const { fetchChangeCards } = useCardTypeAPI();
  const {
    state,
    state: { activeRegion },
    dispatch,
  } = useApp();

  const changeCardsData = {
    changeCardTypes: [
      {
        name: "HappyYOU",
        code: "HYCWA<PERSON>",
        slug: "happy-you-at-work-gift-card-ae",
        logoImageData:
          "https://cdn.merchant-console-sandbox.yougotagift.com/media/brands/logo/53b99a33-c9d3-431c-9cf7-cab0a9e7acf2.png",
        brandImageData:
          "https://cdn.merchant-console-sandbox.yougotagift.com/media/brands/image/d3beb665-0f75-4436-9a42-89e9daa14d5d.png",
        isCustomHyc: false,
        customCardImage: null,
        hasMultipleCustomCards: false,
      },
      {
        name: "HappyYOU",
        code: "HCWAE",
        slug: "happy-you-custom-at-work-gift-card-ae",
        logoImageData:
          "https://cdn.merchant-console-sandbox.yougotagift.com/media/brands/logo/86b09575-3ad6-4f6d-b274-1da8d832c0ef.png",
        brandImageData:
          "https://cdn.merchant-console-sandbox.yougotagift.com/media/brands/image/5b03ecbf-0bec-4cb8-8039-006aff200ced.png",
        isCustomHyc: true,
        customCardImage:
          "https://ygg-atwork-qa-tf.s3.me-central-1.amazonaws.com/media/custom_hyc/images/card-image/a8f54ca2-454e-4c2d-81ed-ddd42769f213.png",
        hasMultipleCustomCards: true,
      },
    ],
  };

  const cardData = changeCardsData?.changeCardTypes;

  const handleCardClick = (item: any) => {
    const isCustomCard = item.isCustomHyc;
    const hasMultipleDenominationOptions = item.hasMultipleCustomCards;

    const selectedCardType = isCustomCard
      ? SELECTED_CARD_TYPE.CUSTOM
      : SELECTED_CARD_TYPE.STANDARD;

    dispatch({
      type: AppContextAction.CARD_TYPE,
      payload: {
        selectedCardType,
      },
    });

    const nextStep =
      isCustomCard && hasMultipleDenominationOptions
        ? COMPONENT_ID.BRAND_LIST
        : COMPONENT_ID.BRAND_LIST;

    onContinue(nextStep);
  };

  useEffect(() => {
    const hasAnyCustomCard = changeCardsData?.changeCardTypes?.some(
      (item: any) => item.isCustomHyc,
    );

    if (!hasAnyCustomCard) {
      dispatch({
        type: AppContextAction.CARD_TYPE,
        payload: {
          selectedCardType: SELECTED_CARD_TYPE.STANDARD,
        },
      });
      onContinue(COMPONENT_ID.BRAND_LIST);
    }
  }, []);

  return (
    <div className={styles["card-type-selection"]}>
      {cardData?.map((item: any, index: number) => {
        const {
          name: cardName,
          isCustomHyc,
          hasMultipleCustomCards,
          customCardImage,
          brandImageData,
        } = item;
        const cardImage =
          isCustomHyc && !hasMultipleCustomCards
            ? customCardImage
            : brandImageData;

        return (
          <CardComponent
            key={index}
            cardImage={cardImage}
            cardName={cardName}
            onClick={() => handleCardClick(item)}
          />
        );
      })}
    </div>
  );
};

export default CardTypeSelection;
