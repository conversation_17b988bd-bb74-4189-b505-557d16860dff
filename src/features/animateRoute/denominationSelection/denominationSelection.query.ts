import { gql } from "@apollo/client";

export const BRAND_DENOMINATION_QUERY = gql`
  query ($slug: String!) {
    brandDenominations(brand_Slug: $slug) {
      edges {
        node {
          amount
          isDefault
        }
      }
    }
  }
`;

export const BRAND_QUERY = gql`
  query BrandQuery($slug: String!, $store: String) {
    brand(slug: $slug, store: $store) {
      ... on BrandNode {
        name
        code
        slug
        brandImageData
        logoImageData
        currency {
          name
          code
          decimalNotation
        }
        isGeneric
        currencyDecimalNotation
        buyForYourself
        denominationRange
        variableDenomination
        description
        redemptionDetails
        expiry
        maxDenominationAmount
        minDenominationAmount
        redemptionBadges {
          label
          type
        }
        isOffer
      }
      ... on ErrorNode {
        code
        status
        message
      }
    }
  }
`;
