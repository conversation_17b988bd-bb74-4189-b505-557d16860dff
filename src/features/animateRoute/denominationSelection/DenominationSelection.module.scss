@import "@styles/variables";
@import "@styles/mixins";

.denomination {
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    &__title {
        color: #0E0F0C;
        font-family: var(--font-bricolage);
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: 24px;
        margin: 0;
    }

    &__change-card {
        display: flex;
        padding: 8px 12px;
        align-items: center;
        gap: 8px;
        border-radius: 100px;
        background: #F5F5F5;
        color: #0E0F0C;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 18px;
        letter-spacing: -0.14px;
        border: none;
        display: flex;
        align-items: center;
    }

    &__brand-img {
        display: flex;
        justify-content: center;
        aspect-ratio: 350 / 224;
        margin-bottom: 8px;

        img {
            border-radius: 16px;
            width: 100%;
            height: 100%;
            max-width: 350px;
            max-height: 224px;
        }

        &--skeleton {
            margin: auto;
            border-radius: 16px;
            margin-bottom: 8px;
            max-width: 350px !important;
            height: 224px !important;
        }
    }

    &--card-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 350px;
        margin: auto;

        &__validity {
            color: #0E0F0C;
            font-size: 12px;
            font-weight: 600;
            line-height: 16px;
            letter-spacing: -0.12px;
        }

        &__labels {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        &__label {
            border-radius: 6px;
            background: #F7F7F7;
            display: flex;
            width: 70px;
            height: 25px;
            padding: 12px 0;
            justify-content: center;
            align-items: center;
            gap: 10px;
            color: #0E0F0C;
            font-size: 10px;
            font-weight: 700;
            line-height: 8px;
            letter-spacing: -0.1px;
            text-transform: uppercase;
        }

        &__more {
            border-radius: 6px;
            background: $dark-charcoal;
            display: flex;
            height: 25px;
            padding: 12px 6px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            color: #fff;
            font-size: 10px;
            font-style: normal;
            font-weight: 700;
            line-height: 8px;
            letter-spacing: -0.1px;
            text-transform: uppercase;
        }

        &--skeleton {
            display: flex;
            justify-content: space-between;
        }
    }
}

.button-cont {
    border-radius: 12px 12px 0 0;
    background: #fff;
    box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.06);
    padding: 16px 20px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 82px;
}