@import "@styles/variables";
@import "@styles/mixins";

.brand-value {
    display: flex;
    justify-content: center;
    max-width: 350px;
    margin: auto;

    &__denominations {
        color: $dark-purple;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding-bottom: 64px;

        &--skeleton {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 8px;
            padding-bottom: 64px;

            @media (max-width: ($msm + 10)) {
                grid-template-columns: 1fr 1fr 1fr 1fr;
            }
        }
    }

    .container {
        height: 38px;
        min-width: 63.6px;
        white-space: nowrap;
        background-color: $grey-bg;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: $dark-charcoal;
        text-transform: uppercase;
        font-style: normal;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        letter-spacing: -0.12px;

        >span {
            padding: 0 8px;
        }
    }
}


.brand-custom-value {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 24px auto 16px auto;
    position: relative;
    z-index: 99;
    height: 64px;
    background-color: $grey-bg;
    border-radius: 16px;
    padding: 8px;
    max-width: 350px;


    a {
        display: inline-block;
        height: 48px;

        &.disabled {
            pointer-events: none;
            opacity: 0.1;
        }
    }

    img {
        width: 48px;
        height: 48px;
        cursor: pointer;
    }

    &__amount-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        width: 100%;

    }

    &__amount {
        @include font-size(24);
        font-family: var(--font-bricolage) !important;
        font-optical-sizing: none;

        color: $dark-purple;
        font-style: normal;
        font-weight: 800;
        line-height: 31px;
        letter-spacing: -0.16px;
    }

    &__amount-input {
        @include font-size(24);
        font-family: var(--font-bricolage) !important;
        font-optical-sizing: none;

        color: $dark-purple;
        font-style: normal;
        font-weight: 800;
        line-height: 32px;
        letter-spacing: -0.16px;
        border: 0;
        min-width: 3ch;
        width: 188px;
        padding: 0;
        outline: none;

        @include rtl-styles {
            font-family: $arabic-font-family !important;
        }

        &[disabled] {
            background-color: transparent;
        }

        &--full {
            min-width: 195px;
        }
    }

    &__bordered {
        outline: 2px solid #0E0F0C;
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 48px;
        background-color: $white;
        width: 100%;
        height: 100%;
        border-radius: 8px;
    }
}