import React, { useCallback, useEffect, useRef, useState } from "react";
import styles from "./BrandValue.module.scss";
import { useTranslation } from "next-i18next";
import { useQuery } from "@apollo/client";
import {
  BrandDenominations,
  BrandValueInterface,
} from "@interfaces/common.inteface";
import { BRAND_DENOMINATION_QUERY } from "../denominationSelection.query";
import { PLATFORM_TYPE } from "@constants/common";
import useApp, { AppContextAction } from "@features/common/common.context";
import { Skeleton } from "@mui/material";
import { useAppDispatch } from "@redux/hooks";
import { setShowSnackBarError } from "@features/common/commonSlice";
import SuccessSnackbar from "@features/common/successSnackbar/SuccessSnackbar";

const BrandValue = ({
  slug,
  brandCode,
  brandName,
  variableDenomination,
  maxDenominationAmount = 0,
  minDenominationAmount = 0,
  currencyCode = "",
  currencyDecimalNotation = 0,
  isInvalidAmount,
  setIsInvalidAmount,
  amountValue,
  setAmountValue
}: BrandValueInterface) => {
  const {
    state: { activeRegion, brandDenomination, card },
    dispatch,
  } = useApp();
  const appDispatch = useAppDispatch();
  const amountInput: any = useRef(null);
  const { t } = useTranslation("common");

  const [hasAmountInputFocus, setHasAmountInputFocus] =
    useState<boolean>(true);
  const [denominations, setDenominations] = useState<Array<any>>([]);

  const { loading, error, data } = useQuery<BrandDenominations>(
    BRAND_DENOMINATION_QUERY,
    {
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": activeRegion?.code,
          "app-platform": PLATFORM_TYPE.MWEB.toLowerCase(),
        },
      },
      variables: {
        slug: slug,
      },
      skip: !Boolean(slug),
      fetchPolicy: "cache-first",
    },
  );

  useEffect(() => {
    if (!loading && data?.brandDenominations?.edges) {
      let _denominations = data?.brandDenominations?.edges || [];
      if (variableDenomination && _denominations) {
        _denominations = [
          { node: { amount: -1, isDefault: false } },
          ..._denominations,
        ];
      }

      setDenominations(_denominations);
      dispatch({
        type: AppContextAction.BRAND_DENOMINATION,
        payload: {
          ...brandDenomination,
          denominations: data?.brandDenominations?.edges.map(
            (item) => item.node.amount,
          ),
        },
      });
    }
  }, [loading, variableDenomination]);

  /**
   * @method onKeyDown
   * @param event
   * @returns
   */
  const onKeyDown = (event: any) => {
    const value = event?.target?.value;

    if (
      currencyDecimalNotation === 0 &&
      (event.which === 190 || event.which === 110)
    ) {
      // preventing decimalpoint if the card has decimalnotaion 0
      event.preventDefault();
      return false;
    }
    // #. Prevent adding char "e" from input
    // #. "e" char allowed in numeric field by default
    if (event.which === 69 || event.which === 107 || event.which === 109) {
      event.preventDefault();
      return false;
    }
  };

  /**
   * @method updateFocusState
   */
  const updateFocusState = (isFocused: boolean) => {
    setHasAmountInputFocus(isFocused);
  };

  /**
   * @method isValidDenomination
   * @param amount
   */
  const isValidDenomination = (amount: number) => {
    return (
      amount >= minDenominationAmount && amount <= maxDenominationAmount
    );
  };

  const onInvalidDenominationUpdated = (
    isInvaidAmont: boolean,
    amountValue: string,
  ) => {
    setIsInvalidAmount(isInvaidAmont);
  };

  /**
   * @method addCustomDenomination
   */
  const addCustomDenomination = useCallback(
    (value: string) => {
      let amount: any = parseFloat(value);
      let isInvalid = false;
      if (!isNaN(amount)) {
        isInvalid = !isValidDenomination(amount);
        dispatchBrandContext({
          cardValue: amount,
          isDirty: true,
          isInvalidCardValue: isInvalid,
        });
      } else {
        dispatchBrandContext({
          cardValue: -1,
          isDirty: true,
          isInvalidCardValue: true,
        });

        isInvalid = true;
      }

      onInvalidDenominationUpdated &&
        onInvalidDenominationUpdated(isInvalid, amount);
    },
    [
      minDenominationAmount,
      maxDenominationAmount,
      card?.brandCode,
      card?.cardValue,
    ],
  );

  /**
   * @method onDenominationSelected
   * @param amount
   */
  const onCustomDenominationChanged = (event: any) => {
    let value = event.target.value;
    const maxLength = String(maxDenominationAmount).length || 4;
    if (
      value &&
      value?.split(".")[0]?.length > maxLength &&
      (Number(value) > Number(maxDenominationAmount) ||
        Number(value) <= 0) &&
      event.which !== 8 &&
      event.which !== 46 &&
      (Number(value) < Number(maxDenominationAmount) ||
        Number(value) > Number(minDenominationAmount))
    ) {
      event.preventDefault();
      return false;
    }

    const precPosition = value.indexOf(".");
    if (
      currencyDecimalNotation > 0 &&
      precPosition > -1 &&
      value.substr(precPosition + 1).length > currencyDecimalNotation
    ) {
      event.preventDefault();
      return false;
    }

    setAmountValue(event.target.value);
    // #. Call the debouncer
    addCustomDenomination(value);
  };

  const dispatchBrandContext = (value: any) => {
    dispatch({ type: AppContextAction.CARD, payload: value });
  };

  /**
   * @method onDenominationSelected
   * @param amount
   */
  const onDenominationSelected = (amount: number) => {
    const amountValue = Number(amount);
    const isInvalid = amountValue === -1 ? true : false;
    setIsInvalidAmount(isInvalid);

    // #. Note: Quantity will be updated based on the stock checking result
    dispatchBrandContext({
      cardValue: amountValue,
      isDirty: true,
      isInvalidCardValue: isInvalid,
    });

    setAmountValue(amountValue);
  };

  useEffect(() => {
    if (isInvalidAmount) {
      appDispatch(
        setShowSnackBarError({
          show: true,
          message: t("plsProvideAmount", {
            currency: currencyCode,
            minAmount: minDenominationAmount,
            maxAmount: maxDenominationAmount,
          }),
        }),
      );
    }
  }, [isInvalidAmount]);

  return (
    <>
      <div
        className={`brand-custom-value ${styles["brand-custom-value"]}`}
      >
        <div
          className={`${styles["brand-custom-value__content"]} ${
            variableDenomination && hasAmountInputFocus
              ? styles["brand-custom-value__bordered"]
              : ""
          } `}
          onClick={() => {
            if (
              amountInput?.current &&
              variableDenomination &&
              document.activeElement !== amountInput.current
            ) {
              amountInput?.current?.focus();
            }
          }}
        >
          <div className={styles["brand-custom-value__amount-wrapper"]}>
            <span className={styles["brand-custom-value__amount"]}>
              {currencyCode}
            </span>
            <input
              type="number"
              placeholder={t("enterTheAmount")}
              className={`${styles["brand-custom-value__amount-input"]}  
             ${
               !amountValue
                 ? styles["brand-custom-value__amount-input--full"]
                 : ""
             }
            `}
              value={amountValue === -1 ? "" : amountValue}
              onChange={onCustomDenominationChanged}
              onKeyDown={onKeyDown}
              onWheel={(e: any) => e.target.blur()}
              onFocus={() => updateFocusState(true)}
              onBlur={() => updateFocusState(false)}
              style={{ width: String(amountValue)?.length + "ch" }}
              readOnly={!variableDenomination}
              ref={amountInput}
              max={maxDenominationAmount}
              min={minDenominationAmount}
              pattern="/^\d*\.?\d*$/"
              lang="en"
            />
          </div>
        </div>
      </div>
      <div className={`brand-value ${styles["brand-value"]}`}>
        {!loading ? (
          <div className={`${styles["brand-value__denominations"]} `}>
            {denominations.map((denomination, index) => {
              if (denomination.node.amount === -1) return;
              return (
                <a
                  key={denomination.node.amount}
                  className={`rounded ${styles["container"]}`}
                  onClick={() => {
                    onDenominationSelected(denomination.node.amount);
                  }}
                  data-testid={"brandValueDenom" + index}
                >
                  <span>
                    {denomination.node.amount !== -1 ? (
                      <span className={styles["value"]}>
                        <span>{currencyCode + " "}</span>
                        <span>{denomination.node.amount}</span>
                      </span>
                    ) : (
                      <span className={styles["custom"]}>
                        {t("custom")}
                      </span>
                    )}
                  </span>
                </a>
              );
            })}
          </div>
        ) : (
          <div
            className={`${styles["brand-value__denominations--skeleton"]} `}
          >
            {[1, 2, 3, 4, 5].map(() => (
              <Skeleton
                variant="rectangular"
                width={63.5}
                height={38}
                sx={{ borderRadius: "8px" }}
              />
            ))}
          </div>
        )}
      </div>
      <SuccessSnackbar showError={true} />
    </>
  );
};

export default BrandValue;
