import React, { useState } from "react";
import styles from "./DenominationSelection.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import BrandValue from "./brandValue/BrandValue";
import { useQuery } from "@apollo/client";
import { BRAND_QUERY } from "./denominationSelection.query";
import useApp, { AppContextAction } from "@features/common/common.context";
import { COMPONENT_ID, PLATFORM_TYPE } from "@constants/common";
import { useTranslation } from "next-i18next";
import Image from "next/image";
import { Skeleton } from "@mui/material";
import Button from "@features/common/button/Button";

const DenominationSelection = ({ onContinue }: any) => {
  const { t } = useTranslation("common");

  const [isInvalidAmount, setIsInvalidAmount] = useState(false);
  const [amountValue, setAmountValue] = useState<any>("");


  const {
    state,
    state: { activeRegion, localeCode, region, brandInfo, brandList },
    dispatch,
  } = useApp();

  const brandSlug = brandInfo?.brandSlug;

  const { loading, error, data } = useQuery<any>(BRAND_QUERY, {
    context: {
      clientName: "at-work",
      headers: {
        "accept-language": localeCode,
        "access-locale": activeRegion?.code,
        "app-platform": PLATFORM_TYPE.MWEB.toLowerCase(),
      },
    },
    variables: {
      slug: brandSlug,
      store: region?.toUpperCase(),
    },
    fetchPolicy: "no-cache",
    skip: !Boolean(localeCode) || !Boolean(brandSlug),
  });

  const brand = data?.brand;

  const setDenomination = () => {
    dispatch({
      type: AppContextAction.BRAND_DENOMINATION,
      payload: {
        ...state.brandDenomination,
        name: state?.card?.currencyCode + " " + state?.card?.cardValue,
      },
    });
    onContinue();
  };

  return (
    <div className={styles["denomination"]}>
      <div className={styles["denomination__header"]}>
        <h2 className={styles["denomination__title"]}>{t("placeOrder")}</h2>
        <button className={styles["denomination__change-card"]}>
          {t("changeCard")}
          <img src={`${imageBaseUrl}/icons/card.svg`} alt="card" width={18} />
        </button>
      </div>

      {!loading ? (
        <div className={styles["denomination__brand-img"]}>
          <Image
            src={brandList?.skinData?.node?.cardImage}
            alt="card"
            width={0}
            height={0}
            sizes="auto"
            blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
          />
        </div>
      ) : (
        <Skeleton
          variant="rectangular"
          className={styles["denomination__brand-img--skeleton"]}
        />
      )}

      {!loading ? (
        <div className={styles["denomination--card-info"]}>
          <p className={styles["denomination--card-info__validity"]}>{brand?.expiry}</p>
          <div className={styles["denomination--card-info__labels"]}>
            {brand?.redemptionBadges?.map((item: any, index: number) => (
              <div className={styles["denomination--card-info__label"]} key={index}>
                {item?.label}
              </div>
            ))}
            <div className={styles["denomination--card-info__more"]}>
              {t("more")} <img src={`${imageBaseUrl}/icons/more-circle.svg`} alt="card" />
            </div>
          </div>
        </div>
      ) : (
        <div className={styles["denomination--card-info--skeleton"]}>
          <Skeleton variant="rectangular" width={102} height={25} />
          <Skeleton variant="rectangular" width={210} height={25} />
        </div>
      )}

      <BrandValue
        slug={brand?.slug}
        brandCode={brand?.brandCode}
        brandName={brand?.brandName}
        variableDenomination={brand?.variableDenomination}
        minDenominationAmount={brand?.minDenominationAmount}
        maxDenominationAmount={brand?.maxDenominationAmount}
        currencyCode={brand?.currency?.code}
        currencyDecimalNotation={brand?.currencyDecimalNotation}
        isUserAuthorized={brand?.isUserAuthorized}
        renderPreviewPage={brand?.renderPreviewPage}
        setIsInvalidAmount={setIsInvalidAmount}
        isInvalidAmount={isInvalidAmount}
        amountValue={amountValue}
        setAmountValue={setAmountValue}
      />
      <div className={styles["button-cont"]}>
        <Button
          action={setDenomination}
          disabled={loading || isInvalidAmount || !amountValue}
          isChatBot={true}
          theme="at-work-primary"
          isFixedBottom={true}
        >
          {t("continue")}
        </Button>
      </div>
    </div>
  );
};

export default DenominationSelection;
