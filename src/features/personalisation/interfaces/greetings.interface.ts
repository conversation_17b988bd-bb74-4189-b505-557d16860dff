export interface GreetingsOccasionInterface {
    occasions: {
        edges: Occasions[] | any;
    };
}

export interface Occasions {
    node: {
        name: string;
        code: string;
        illustrationCount: string;
        gifIllustrationCount: string;
    };
}
export interface Illustration {
    node: {
        occasion: {
            name: string;
        };
        cardImage: string;
    };
}
export interface GifIllustration {
    node: {
        occasion: {
            name: string;
        };
        gifFile: string;
    };
}
export interface GreetingsIllustrationsInterface {
    illustrations: {
        edges: Illustration[];
    };
    gifIllustrations: {
        edges: GifIllustration[];
    };
}
