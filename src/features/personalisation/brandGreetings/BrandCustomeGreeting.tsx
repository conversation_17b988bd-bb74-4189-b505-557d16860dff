import React from "react";
import styles from "./BrandGreetings.module.scss";
import Image, { default as NextImage } from "next/image";
import { UploadIconWhite } from "misc/CustomeIcon";
import { Trans, useTranslation } from "next-i18next";
import { imageBaseUrl } from "@constants/envVariables";

const BrandCustomeGreeting = ({
  greetingsPreviewUrl,
  handleFileUploadClick,
  greetingLoading,
}: any) => {
  // translations
  const { t } = useTranslation("common");
  const workTickIcon = `${imageBaseUrl}/icons/tick-circle.svg`;

  return (
    <>
      {greetingsPreviewUrl ? (
        <div
          className={`${styles["grid-slide__greetings-preview"]}`}
          onClick={handleFileUploadClick}
        >
          {greetingsPreviewUrl && !greetingLoading && (
            <div
              className={`${
                greetingsPreviewUrl
                  ? `${styles["select-box"]} ${styles["custome"]}`
                  : styles["hide"]
              }`}
            >
              <img src={workTickIcon} height={24} width={24} />
            </div>
          )}
          <NextImage
            src={greetingsPreviewUrl}
            alt="image"
            fill
            style={{ borderRadius: "12px" }}
          />

          <div className={styles["grid-slide__greetings-preview-content"]}>
            <UploadIconWhite />
            <span>{t("change").toLocaleUpperCase()}</span>
          </div>
          <div className={styles["preview"]}></div>
        </div>
      ) : (
        <div
          style={{
            position: "relative",
          }}
          onClick={handleFileUploadClick}
        >
          <label
            htmlFor={`${"greeting-upload"}`}
            className={styles["uploadButton"]}
            style={{
              cursor: "pointer",
              display: "block",
              height: "100%",
            }}
          >
            <div className={styles["upload-icon"]}>
              <div className={styles["wrapper"]}>
                <Image
                  src={`${imageBaseUrl}/icons/upload.svg`}
                  height={24}
                  width={24}
                  alt="upload icon"
                />
                <Trans i18nKey="uploadYourDesign" />
              </div>
            </div>
            <NextImage
              src={`${imageBaseUrl}/images/greetings-upload-black.png`}
              fill
              alt="image"
              style={{ borderRadius: "12px" }}
            />
          </label>
        </div>
      )}
    </>
  );
};

export default BrandCustomeGreeting;
