import React, { useEffect, useState } from "react";
import styles from "./../BrandGreetingsAccordian.module.scss";
import Image from "next/image";
import useApp from "@features/common/common.context";
import {
  BRAND_FORM_STATE,
} from "@constants/common";
import BrandGridSkelton from "@features/personalisation/contentLoaders/brandGridSkelton/BrandGridSkelton";
import {
  GifIllustration,
  Illustration,
} from "@features/personalisation/interfaces/greetings.interface";
import { imageBaseUrl } from "@constants/envVariables";
import BrandCustomeGreeting from "../../BrandCustomeGreeting";

export default function BrandGridlist({
  illustrationData,
  illustrationsLoading,
  active,
  gif,
  onActive,
  isToggledGif,
  handleFileUploadClick,
  greetingsPreviewUrl,
  greetingLoading,
}: any) {
  const {
    state: { card },
  } = useApp();

  // #. State to handle gif or gif illustration data
  const [mapData, setMapData] = useState<Array<any>>();

  // #. Set illustrations or gif illustrations
  useEffect(() => {
    if (
      (card?.formState === BRAND_FORM_STATE.EDIT && gif) ||
      isToggledGif
    ) {
      setMapData(illustrationData?.gifIllustrations?.edges);
    } else setMapData(illustrationData?.illustrations?.edges);
  }, [illustrationData]);

  const items: Array<any> = mapData || [];

  return (
    <>
      {illustrationsLoading ? (
        <BrandGridSkelton />
      ) : (
        <div className={styles["grid-slide"]} data-testid="sliderChildren">
          <BrandCustomeGreeting
            greetingsPreviewUrl={greetingsPreviewUrl}
            handleFileUploadClick={handleFileUploadClick}
            greetingLoading={greetingLoading}
          />
          {items?.map((item: Illustration | GifIllustration | any) => (
            <div
              className={`${styles["grid-slide-item"]} ${
                active ===
                (gif ? item?.node?.gifFile : item?.node?.cardImage)
                  ? styles["active"]
                  : ""
              } `}
              key={gif ? item?.node?.gifFile : item?.node?.cardImage}
            >
              {((!gif && item?.node?.cardImage) ||
                (gif && item?.node?.gifFile)) && (
                <>
                  <Image
                    src={gif ? item?.node?.gifFile : item?.node?.cardImage}
                    blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                    placeholder="blur"
                    width={105}
                    height={147}
                    alt="Greeting image"
                    onClick={() => {
                      onActive(item, gif, "illustration");
                    }}
                    className="border-override"
                    loading="lazy"
                    unoptimized={true}
                  />
                </>
              )}
              <div
                className={`${
                  active ===
                  (gif ? item?.node?.gifFile : item?.node?.cardImage)
                    ? styles["select-box"]
                    : styles["hide"]
                }`}
              >
                <span className={styles["select-box__single-tick"]}>
                  <img src={`${imageBaseUrl}/icons/tick.svg`} alt="" />
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
}
