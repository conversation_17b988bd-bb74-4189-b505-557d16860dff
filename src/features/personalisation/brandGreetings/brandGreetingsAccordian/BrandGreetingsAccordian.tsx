import { useTranslation } from "next-i18next";
import styles from "./BrandGreetingsAccordian.module.scss";
import React, { useEffect, useRef, useState } from "react";
import { useLazyQuery } from "@apollo/client";
import BrandGridlist from "./brandGridList/BrandGridList";
import useApp, { AppContextAction } from "@features/common/common.context";
import { BRAND_FORM_STATE, CUSTOM_GIFT_CARD } from "@constants/common";
import {
  BRAND_GIF_ILLUSTRATION,
  BRAND_ILLUSTRATIONS,
} from "@features/personalisation/personalisation.query";
import { extractDimensions } from "@utils/extractDimensions";
import { getImageFormats } from "@utils/getImageFormats";
import ImageCrop from "@features/common/imageCrop/ImageCrop";

interface BrandGreetingsAccordionInterface {
  gif: boolean;
  onGreetingSelected: (value: any) => void;
  handleOcassion: (occCode: any) => void;
  isAnimated: boolean;
  language: string;
  occasionData: any;
  index: number;
  isToggledGif: boolean;
  occCode: string;
}

/**
 * @method BrandGreetingsAccordion
 * @description Render accordion component of the quick view
 * Accordion 2 - About this gift - configurable
 * todo - check the see more link functionality
 * @returns {JSX.Element}
 */
const BrandGreetingsAccordion = ({
  gif,
  onGreetingSelected,
  handleOcassion,
  isAnimated,
  language,
  occasionData,
  index,
  isToggledGif,
  occCode,
}: BrandGreetingsAccordionInterface): React.ReactElement => {
  // #. Get translations
  const { t } = useTranslation("common");

  const {
    state: { card, customeGreetings },
    dispatch,
  } = useApp();

  const [validationMsg, setValidationMsg] = useState<any>({
    open: false,
    validationMsgTitle: "",
    validationMsgInfo: "",
    singleBtnModal: false,
  });
  const [isImageCropOpen, setIsImageCropOpen] = useState<boolean>(false);
  const [isGreetingUploaded, setIsGreetingUploaded] =
    useState<boolean>(false);
  const [greetingLoading, setGreetingsLoading] = useState(false);
  const [infoFile, setInfoFile] = useState<any>();
  const [customGreeting, setCustomeGreeting] = useState<any>();
  const fallBackAspectRatio = "0.7111111111111111";
  const greetingDimension = "800w x 1125h";

  const {
    name: categoryTitle,
    code: categoryCode,
    illustrationCount,
    gifIllustrationCount,
  } = occasionData;

  const [active, setActive] = useState<string | undefined>();
  // #. Get illustartion otpions
  const [
    getIllustrations,
    { loading: illustrationsLoading, error, data: illustrationData },
  ] = useLazyQuery(
    isAnimated === true ? BRAND_GIF_ILLUSTRATION : BRAND_ILLUSTRATIONS,
  );

  /**
   * @method onActive
   * @description Set selected image
   * @param item
   * @param isGif
   */
  const onActive = (item: any, isGif: boolean, type?: string) => {
    if (type == "illustration") {
      dispatchCustomeGreetingsContext({
        customeGreetings: {
          file: "",
          urlType: "",
          name: "",
          previewUrl: "",
        },
      });
    }
    const activeItem = isGif ? item?.node?.gifFile : item?.node?.cardImage;
    setActive(activeItem);
    let sanitizedUrl: any;
    if (type == "custome") {
      sanitizedUrl = item?.replace(/\s+/g, "").trim();
    }

    // #. Dispatch the selcted details
    const filePath =
      type == "custome"
        ? sanitizedUrl
        : isGif
          ? item?.node?.gifFile
          : item?.node?.cardImage || "";
    const referenceCode = item?.node?.referenceCode || "";
    const staticGifPath = item?.node?.gifImage || "";
     const occasion = item?.node?.occasion || "";
    onGreetingSelected &&
      onGreetingSelected({ filePath, referenceCode, staticGifPath, occasion });
  };

  useEffect(() => {
    handleOcassion(categoryCode);
    getIllustrations({
      variables: {
        code: categoryCode?.toUpperCase(),
      },
      context: {
        clientName: "greetings",
        headers: {
          "accept-language": language,
        },
      },
    });
  }, [isToggledGif, categoryCode, language]);

  useEffect(() => {
    if (
      card?.formState === BRAND_FORM_STATE.EDIT ||
      card?.greetingCover?.filePath
    ) {
      card?.greetingCover?.filePath &&
        setActive(card?.greetingCover?.filePath);
    }
  }, []);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUploadClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    fileInputRef.current?.click(); // Manually trigger input
  };

  const dimensions: any = extractDimensions(greetingDimension);

  const dispatchCustomeGreetingsContext = (payload: any) => {
    dispatch({
      type: AppContextAction.CUSTOME_GREETINGS,
      payload,
    });
  };

  const handleGreetingsUpload = (event: any) => {
    setIsGreetingUploaded(false);
    const fileInput = event.target;
    const file = fileInput.files?.[0];

    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }, 100);

    if (!file) return;
    const orText = t("or");
    const fileTypes = getImageFormats(
      CUSTOM_GIFT_CARD.VALID_TYPES,
      orText,
    );
    //Validate file type
    if (!CUSTOM_GIFT_CARD.VALID_TYPES.includes(file.type)) {
      setValidationMsg({
        open: true,
        validationMsgTitle: t("invalidImgType"),
        validationMsgInfo: t("fileUploadErrorMsg", { type: fileTypes }),
        singleBtnModal: true,
      });
      return;
    }

    //size limit in KB
    const sizeLimit = 5120;

    if (file.size / 1024 > sizeLimit) {
      setValidationMsg({
        open: true,
        validationMsgTitle: t("invalidImageSize"),
        validationMsgInfo: t("fileSizeErrorMsg", { size: "5" }),
        singleBtnModal: true,
      });
      return;
    }

    // Generate preview URL to show the uploaded file immediately
    const previewUrlImage: any = URL.createObjectURL(file);

    if (previewUrlImage) {
      const img = new Image();
      img.src = previewUrlImage;
      img.onload = async () => {
        setIsImageCropOpen(true);
      };
      img.onerror = () => {
        console.error("Failed to load the image");
      };
      setCustomeGreeting(previewUrlImage);
      setIsGreetingUploaded(false);
      setInfoFile(file);
    }
  };

  const handleGreetingsStates = (val: any) => {
    setIsImageCropOpen(val?.imageCropOpen);
    onActive(val?.greetingPreview, false, "custome");
    setIsGreetingUploaded(val?.isGreetingUpload);
  };

  const handleClose = () => {
    setIsImageCropOpen(false);
  };

  return (
    <>
      {isImageCropOpen ? (
        <ImageCrop
          imgSrc={customGreeting}
          info={infoFile}
          language={"en"}
          handleGreetingsStates={handleGreetingsStates}
          isOpenPreviewImage={true}
          aspectRatio={fallBackAspectRatio}
          dimensionPreview={greetingDimension}
          isCustomeGreetings={true}
          handleClose={handleClose}
          isImageCropOpen={isImageCropOpen}
        />
      ) : null}

      <div className="greetings-accordion">
        <div
          style={{
            position: "sticky",
            top: 0,
            zIndex: 100,
            backgroundColor: "white",
            paddingBottom: "8px",
          }}
        >
          <h6
            data-testid="accordionAboutThisGiftTitle"
            className={`${styles["brand-greetings-accordion-title"]} `}
            style={{ marginBottom: "0" }}
          >
            {categoryTitle}
            <span>
              &nbsp;&#8226;&nbsp;&nbsp;&nbsp;
              {isToggledGif ? gifIllustrationCount : illustrationCount}
              &nbsp;{t("designs")}
            </span>
          </h6>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          id={`greeting-upload`}
          style={{ display: "none" }}
          onChange={(event: any) => handleGreetingsUpload(event)}
        />
        <div className="greetings-accordion-summary">
          {illustrationData?.gifIllustrations?.edges?.length === 0 ? (
            <p className={styles["empty-container"]}>
              {t("gretingCover")}
            </p>
          ) : (
            <BrandGridlist
              illustrationData={illustrationData}
              illustrationsLoading={illustrationsLoading}
              active={active || ""}
              gif={gif}
              onActive={onActive}
              isToggledGif={isToggledGif}
              categoryTitle={categoryTitle}
              count={
                isToggledGif ? gifIllustrationCount : illustrationCount
              }
              handleFileUploadClick={handleFileUploadClick}
              greetingsPreviewUrl={
                card?.formState === BRAND_FORM_STATE.EDIT
                  ? customeGreetings?.previewUrl ||
                    customeGreetings?.customeGreetings?.previewUrl
                  : customeGreetings?.customeGreetings?.previewUrl
              }
              greetingLoading={greetingLoading}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default BrandGreetingsAccordion;
