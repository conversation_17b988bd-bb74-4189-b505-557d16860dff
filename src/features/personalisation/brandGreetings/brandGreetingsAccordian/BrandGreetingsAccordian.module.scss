@import "@styles/variables";
@import "@styles/mixins";

.brand-greetings-accordion {
  padding: 16px 0 60px;

  &-title {
    margin-bottom: 16px;
    margin-top: 0;
    color: $dark-purple;
    direction: rtl;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 150% */
    letter-spacing: -0.16px;
    text-align: end;

    @include rtl-styles {
      direction: ltr;
    }

    span {
      color: #868785;
      font-size: 8px;
      font-style: normal;
      font-weight: 600;
      line-height: 6px; /* 75% */
      text-transform: uppercase;
      margin: 0 8px;
    }

    @include font-size(16);

    &-with-icon {
      margin-left: 10px !important;

      @include rtl-styles {
        margin-left: 0;
        margin-right: 10px !important;
      }
    }
  }

  &-offer-icon {
    @include rtl-rotate;

    color: $dark-purple;
  }

  &-offer-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    a {
      color: $barney-purple;

      @include font-size(10);
    }

    &-cta {
      margin-top: auto;
    }

    &-title {
      display: flex;
      flex-direction: column;
      width: 73%;
      flex-grow: 1;

      h5 {
        @include font-size(12);

        color: #545454;
        margin: 0;
      }

      h6 {
        @include font-size(10);

        font-weight: 200;
        margin: 0 !important;
      }
    }

    &-image {
      border-radius: 6px;
      width: 40px;
      height: 40px;
      object-fit: cover;
      object-position: center;
      border: solid 1px $silver !important;
    }
  }

  &-text-content {
    @include font-size(12);

    margin: 0;
    color: $hot-grey;

    a {
      color: $barney-purple;

      @include font-size(12);
    }

    ul {
      margin: 0;
      padding: 0 20px;
    }
  }

  &-bg-filled-container {
    background-color: $pale-grey;
    overflow: hidden;
  }

  &-image-stack {
    background: #f2f5f8;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px 20px;

    &-item {
      img {
        width: 100%;
        object-fit: contain;
      }
    }
  }

  &-store-locations {
    margin-top: -15px;
  }

  &-store-wrapper {
    display: grid;
    grid-template-columns: 1fr;
  }

  &-store-group {
    @include font-size(12);

    &-section {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }

    &-address {
      color: $dark-grey;
      width: 180px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &-contact {
      color: $warm-grey;
      display: flex;
      align-items: center;
      gap: 10px;

      &-icon {
        @include rtl-rotate;
      }
    }
  }

  &-redeemables {
    &-title {
      @include font-size(12);

      color: $dark-grey;
    }
  }

  .brand-phone-number {
    @include rtl-styles {
      direction: ltr;
    }
  }

  &__reviews {
    height: auto;
    max-height: 275px;
    overflow: auto;

    &-block {
      padding: 0 0 20px;
    }

    &-header {
      display: flex;
      justify-content: space-between;

      &-customer {
        @include font-size(14);

        font-weight: 600;
        color: $dark-grey;
      }

      &-mode {
        @include font-size(12);

        font-weight: 600;
        color: $barney-purple;
      }
    }

    &-review {
      @include font-size(12);

      color: $dark-grey;
      margin: 10px 0 5px;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid $warm-grey;
    }
  }
}

.grid-slide {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  overflow-y: scroll;
  margin-bottom: 16px;
  position: relative;

  @include rtl-styles {
    direction: rtl;
  }

  &__greetings-preview {
    width: 80px;
    height: 112.5px;
    position: relative;
    border-radius: 12px;
    border: 2px solid #0071ff;
    overflow: hidden;
    margin-top: 3px;
    margin-left: 3px;
    @include rtl-styles {
      margin-right: 3px;
      margin-left: 0;
    }
    cursor: pointer !important;
    .preview {
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      width: 100%;
      height: 100%;
      inset: 0;
    }
    &-content {
      position: absolute;
      z-index: 9999;
      inset: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 7px;
      span {
        font-weight: 500;
        font-size: 12px;
        color: #fff;
      }
    }
  }

  .grid-slide-item {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    position: relative;
    height: fit-content;
  }

  .grid-slide-item img {
    display: block;
    width: 100%;
    border-radius: 12px;
    // border: 4px solid transparent;
  }

  .select-box {
    z-index: 98;
    position: absolute;
    background-color: #fff;
    width: 28px;
    height: 28px;
    top: -1px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .custome {
    right: unset !important;
    top: 2px;
    left: 63px !important;
    @include rtl-styles {
      right: 0 !important;
      left: unset !important;
    }
  }

  .active {
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, 12%);
    background: $white;
  }

  .hide {
    display: none;
  }
}

.crop-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  position: absolute;
  bottom: 20px;
  z-index: 1000;
}
