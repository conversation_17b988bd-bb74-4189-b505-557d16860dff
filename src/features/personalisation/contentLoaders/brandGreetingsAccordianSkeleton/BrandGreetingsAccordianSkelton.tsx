import { Skeleton } from '@mui/material';
import React from 'react';
import styles from './BrandGreetingsAccordianSkelton.module.scss';

const BrandGreetingsAccordianSkelton = () => {
    const loopCount = 15;
    return (
        <>
            <div className={styles['accordian-skelton']}>
                <Skeleton
                    height={24}
                    sx={{ marginBottom: '20px' }}
                    width={200}
                />
                <div className={styles['box']}>
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                </div>
                <div className={styles['lists']}>
                    {[...Array(loopCount)].map((value, index) => (
                        <div
                            className={styles['accordian-skelton__item']}
                            key={index}
                        >
                            <Skeleton
                                variant="text"
                                height={24}
                                width={200}
                                sx={{
                                    marginBottom: '20px',
                                }}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default BrandGreetingsAccordianSkelton;
