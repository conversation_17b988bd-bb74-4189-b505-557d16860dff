import { Skeleton } from '@mui/material';
import React from 'react';
import styles from './BrandGridSkelton.module.scss';

const BrandGridSkelton = () => {
    return (
        <>
            <div className={styles['grid-skelton']}>
                <div className={styles['box']}>
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                    <Skeleton
                        variant="rectangular"
                        className={styles['select-box']}
                    />
                </div>
            </div>
        </>
    );
};

export default BrandGridSkelton;
