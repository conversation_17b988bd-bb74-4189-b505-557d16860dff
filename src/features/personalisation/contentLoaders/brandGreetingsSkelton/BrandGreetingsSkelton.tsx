import { Skeleton } from '@mui/material';
import React from 'react';
import styles from './BrandGreetingsSkelton.module.scss';

const BrandGreetingsSkelton = () => {
    return (
        <div className={styles['greetings-skelton']}>
            {/* <Skeleton
        variant="text"
        width={120}
        className={styles["greetings-skeleton__title"]}
      /> */}
            <div className={styles['box']}>
                <Skeleton
                    variant="rectangular"
                    className={styles['select-box1']}
                />
                <Skeleton
                    variant="rectangular"
                    className={styles['select-box2']}
                />
                <Skeleton
                    variant="rectangular"
                    className={styles['select-box2']}
                />
            </div>

            <Skeleton variant="rectangular" className={styles['grid-box']} />
        </div>
    );
};

export default BrandGreetingsSkelton;
