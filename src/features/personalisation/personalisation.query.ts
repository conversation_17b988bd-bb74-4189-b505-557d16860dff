import { gql } from "@apollo/client";
export const BRAND_OCCASIONS_QUERY = gql`
  query Occasions($gif: Boolean!) {
    occasions(gif: $gif) {
      edges {
        node {
          name
          nameEn
          code
          illustrationCount
          gifIllustrationCount
          isActive
        }
      }
    }
  }
`;

export const BRAND_ILLUSTRATIONS = gql`
  query Illustrations($code: String!) {
    illustrations(occasionCode: $code) {
      edges {
        node {
          occasion {
            name
            code
          }
          cardImage
          referenceCode
        }
      }
    }
  }
`;

export const BRAND_GIF_ILLUSTRATION = gql`
  query GIFillustration($code: String!) {
    gifIllustrations(occasionCode: $code) {
      edges {
        node {
          occasion {
            name
            code
          }
          gifFile
          referenceCode
          gifImage
        }
      }
    }
  }
`;
