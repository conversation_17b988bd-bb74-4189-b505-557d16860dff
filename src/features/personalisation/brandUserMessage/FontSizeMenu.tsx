import { useState } from "react";
import styles from "./BrandUserMessage.module.scss";
import { Menu, MenuItem } from "@mui/material";
import { USER_MESSAGE_FONT_SIZES } from "@constants/common";
/**
 * @method FontSizeMenu
 * @description Font family menu
 * @returns
 */
const FontSizeMenu = ({
  selectedSize,
  onFontSizeUpdated,
}: any): JSX.Element => {
  // #. Default unit string
  const DEFAULT_UNIT = "pt";

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);

  /**
   * @method onBGColorMenuClicked
   */
  const onFMMenuClicked = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  /**
   * @method setAnchorElEmpty
   */
  const setAnchorElEmpty = () => {
    setAnchorEl(null);
  };

  /**
   * @method onFontSelected
   */
  const onFontSelected = (fontSize: number) => {
    // #. Trigger the event
    onFontSizeUpdated && onFontSizeUpdated(fontSize);
    // #. Make the anchot empty
    setAnchorElEmpty();
  };

  return (
    <>
      <a
        id="fm-font-link"
        className={`${styles["brand-user-message__fz-menu"]} rounded-6`}
        onClick={onFMMenuClicked}
        data-testid="fontSizeMenuLink"
      >
        {selectedSize} {DEFAULT_UNIT}
      </a>
      <Menu
        id="font-menu"
        anchorEl={anchorEl}
        open={openMenu}
        onClose={setAnchorElEmpty}
        classes={{
          paper: styles["brand-user-message__fz-menu-control"],
        }}
        data-testid="fontSizeMenu"
      >
        {USER_MESSAGE_FONT_SIZES.map((size, index) => {
          return (
            <MenuItem
              onClick={() => {
                onFontSelected(size);
              }}
              key={size}
              data-testid={"fontSizeMenu" + index}
            >
              {size} {DEFAULT_UNIT}
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};

export default FontSizeMenu;
