import { useEffect, useState } from "react";
import styles from "./BrandUserMessage.module.scss";
import { Menu, MenuItem } from "@mui/material";
import {
    LOCALES,
  USER_MESSAGE_FONT_FAMILY_AR,
  USER_MESSAGE_FONT_FAMILY_EN,
} from "@constants/common";
import { imageBaseUrl } from "@constants/envVariables";

/**
 * @method FontFamilyMenu
 * @description Font family menu
 * @returns
 */
const FontFamilyMenu = ({
  selectedFont,
  onFontUpdated,
  locale,
}: any): JSX.Element => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);

  const fontFamilyData =
    locale === LOCALES.ARABIC
      ? USER_MESSAGE_FONT_FAMILY_AR
      : USER_MESSAGE_FONT_FAMILY_EN;

  /**
   * @method onBGColorMenuClicked
   */
  const onFMMenuClicked = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  /**
   * @method setAnchorElEmpty
   */
  const setAnchorElEmpty = () => {
    setAnchorEl(null);
  };

  /**
   * @method onFontSelected
   */
  const onFontSelected = (font: string) => {
    // #. Trigger the event
    onFontUpdated && onFontUpdated(font);
    // #. Update the anchor element
    setAnchorElEmpty();
  };

  useEffect(() => {
    if (locale === LOCALES.ARABIC && selectedFont === "Mona Sans") {
      onFontSelected(USER_MESSAGE_FONT_FAMILY_AR[0]);
    }
  }, [locale]);

  return (
    <>
      <a
        id="fm-font-link"
        className={styles["brand-user-message__fm-menu"]}
        onClick={onFMMenuClicked}
        data-testid="useMessageFontFamily"
      >
        <img
          src={`${imageBaseUrl}/icons/text.svg`}
          width={20}
          height={20}
          data-testid="useMessageFontFamilyIcon"
        />
        <i className={`icon-dropdown ${styles["dropdown-icon"]}`}></i>
      </a>
      <Menu
        id="font-menu"
        anchorEl={anchorEl}
        open={openMenu}
        onClose={setAnchorElEmpty}
        classes={{
          paper: styles["brand-user-message__fm-menu-control"],
        }}
        data-testid="useMessageFontFamilyMenu"
      >
        {fontFamilyData.map((font, index) => {
          return (
            <MenuItem
              onClick={() => {
                onFontSelected(font);
              }}
              key={font}
              data-testid={"fontFamMenu" + index}
            >
              {font}
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};

export default FontFamilyMenu;
