@import "@styles/variables";
@import "@styles/mixins";

.brand-user-message {
  margin: 26px 0 0;
  margin: 0;

  h5 {
    color: $dark-purple;

    @include font-size(16);

    font-weight: 600;
    margin: 0 0 15px;
  }

  &__display {
    position: relative;

    textarea {
      width: 100%;
      border: 0;
      padding: 200px 43px;
      outline: 0;
      border-radius: 12px;
      resize: none;
      color: $white;
      text-align: center;
      font-size: 20px;
      height: calc(100vh - 290px);

      @media (max-height: (735px)) {
        padding: 160px 43px;
      }
    }
  }

  &__control-panel {
    display: flex;
    align-items: center;
    justify-content: end;
    margin-top: 8px;
    padding-bottom: 20px;
    gap: 16px;
  }

  &__word-count-block {
    position: absolute;
    bottom: 12px;
    right: 12px;

    @include rtl-styles {
      right: 0;
      left: 12px;
    }
  }

  &__word-count {
    color: $dark-purple;

    @include font-size(12);
  }

  &__bg-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 10px !important;

    &-selected {
      height: 24px;
      width: 24px;
      display: inline-block;
      border-radius: 50%;
    }

    &-control {
      border-radius: 3px !important;
      color: $white;

      ul {
        padding: 0;
        width: 200px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;

        li {
          width: 50px;
        }
      }
    }
  }

  &__fm-menu,
  &__fz-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    // gap: 8px;

    &-control {
      border-radius: 3px !important;
      color: $dark-purple;

      ul {
        li {
          font-family: $default-font-family;
        }
      }
    }
  }

  &__bg-menu,
  &__fm-menu,
  &__fz-menu {
    font-size: 12px !important;
    font-weight: 600;
    cursor: pointer;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    background-color: #f5f5f5;
    color: $dark-purple;
  }

    &__button-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    align-items: center;
    height: 80px;
    right: 0;
    left: 0;
    background: $white;
    border-radius: 12px 12px 0 0;
    background: #fff;
    box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.06);
  }
}
