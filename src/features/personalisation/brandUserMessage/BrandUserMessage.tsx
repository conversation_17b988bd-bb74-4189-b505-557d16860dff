import React from "react";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import useApp, { AppContextAction } from "@features/common/common.context";
import BackGroundColorMenu from "./BackGroundColorMenu";
import styles from "./BrandUserMessage.module.scss";
import FontFamilyMenu from "./FontFamilyMenu";
import FontSizeMenu from "./FontSizeMenu";
import Button from "@features/common/button/Button";
import { useAppDispatch, useAppSelector } from "@redux/hooks";
import {
  getTokenInfo,
  setNotifierState,
} from "@features/common/commonSlice";
import debounce from "lodash/debounce";
import BrandHeader from "../brandPersonalize/brandHeader/BrandHeader";
import { FONT_FAMILIES } from "@constants/common";

/**
 * @method BrandUserMessage
 * @description Brand user custom message component
 * @returns
 */
const BrandUserMessage = ({
  onCancel,
  onContinue,
}: {
  onCancel: () => void;
  onContinue: () => void;
}): React.JSX.Element => {
  // translations
  const { t } = useTranslation("common");

  // #. Get brand context
  const {
    state: { card },
    dispatch,
  } = useApp();

  const tokens = useAppSelector(getTokenInfo);

  const activeUser = tokens?.isUserSignedIn;

  const notifierDispatch = useAppDispatch();

  const placeHolderText = t("usermessage");
  const charatersText = t("characters");
  const MAX_MESSSAGE_LENGTH = 720;

  // #. Notifier state
  const [disableContinue, setDisableContinue] = useState<boolean>(false);

  const [message, setMessage] = useState<string>(
    card?.giftMessage?.message || "",
  );

  useEffect(() => {
    if (!card?.giftMessage?.isDirty) {
      setDisableContinue(false);
    }
  }, [card?.greetingCover?.occasion?.name]);

  // #. Transition states
  const nodeRef = useRef(null);

  /**
   * @method dispatchBrandContext
   * @param value
   */
  const dispatchBrandContext = (value: object) => {
    const newValue = { giftMessage: { ...card.giftMessage, ...value } };
    dispatch({ type: AppContextAction.CARD, payload: newValue });
  };

  /**
   * @method onMessageUpdated
   */
  const onMessageUpdated = (event: any) => {
    const message = event?.target?.value;
    setMessage(message);
    dispatchMessage.current(
      message,
      card?.giftMessage?.backgroundColor,
      card?.giftMessage?.fontSize,
      card?.giftMessage?.fontFamily,
    );
  };

  /**
   * @method dispatchMessage
   * @description use a delay to send the message
   */
  const dispatchMessage = useRef(
    // #. useRef - beacuse component recreation after state update may reinit the debounce
    // #. To avoid this issue wrap the debounce with a  useRef
    debounce((message, backgroundColor, fontSize, fontFamily) => {
      dispatchBrandContext({
        message,
        backgroundColor,
        fontSize,
        fontFamily,
        isDirty: true,
      });
    }, 500),
  );

  /**
   * @method onBackGroundColorUpdated
   */
  const onBackGroundColorUpdated = (color: string) => {
    dispatchBrandContext({
      backgroundColor: color,
    });
  };

  /**
   * @method onFontSizeUpdated
   */
  const onFontSizeUpdated = (size: number) => {
    dispatchBrandContext({
      fontSize: size,
    });
  };

  /**
   * @method onFontSizeUpdated
   */
  const onFontUpdated = (font: string) => {
    switch (font) {
      case FONT_FAMILIES.POPPINS:
        font = "var(--font-poppins)";
        break;
      case FONT_FAMILIES.GEORGIA:
        font = "Georgia";
        break;
      case FONT_FAMILIES.ARIAL:
        font = "Arial";
        break;
      case FONT_FAMILIES.BRADLEY:
        font = "Bradley Hand";
        break;
      case FONT_FAMILIES.BRUSH_SCRIPT:
        font = "Brush script MT";
        break;
      case FONT_FAMILIES.BLAKA_HOLLOW:
        font = "var(--blaka-hollow)";
        break;
      case FONT_FAMILIES.TAJAWAL:
        font = "var(--tajawal)";
        break;
      case FONT_FAMILIES.REEM_KUFI:
        font = "var(--reem-kufi)";
        break;
      case FONT_FAMILIES.EL_MESSIRI:
        font = "var(--el-messiri)";
        break;
      case FONT_FAMILIES.CAIRO:
        font = `var(--font-cairo)`;
        break;
      case FONT_FAMILIES.MONA_SANS:
        font = `var(--font-mona-sans)`;
        break;
      case FONT_FAMILIES.NOTA_KUFI:
        font = `var(--font-noto-kufi)`;
        break;
    }
    dispatchBrandContext({
      fontFamily: font,
    });
  };

  /**
   * @method onContinueClicked
   */
  const onContinueClicked = () => {
    const textLength = message?.trim()?.length || 0;
    if (textLength > MAX_MESSSAGE_LENGTH) {
      // #. Show the notifier
      notifierDispatch(
        setNotifierState({
          title: t("charLimit"),
          description: t("charLimitMessage"),
          icon: "errorOutline",
        }),
      );
      return false;
    }

    dispatchBrandContext({
      message,
    });

    onContinue();
  };

  const onSkipClicked = () => {
    dispatchBrandContext({
      message: "",
    });
    onContinue();
  };

  return (
    <>
      <BrandHeader
        title={"Add Message"}
        actionLabel={t("skip")}
        action={onSkipClicked}
        onBack={onCancel}
      />
      <div
        ref={nodeRef}
        className={`${styles["brand-user-message"]} brand-accordion`}
      >
        <div className={`${styles["brand-user-message__container"]}`}>
          <div className={`${styles["brand-user-message__display"]}`}>
            <textarea
              maxLength={MAX_MESSSAGE_LENGTH}
              onChange={onMessageUpdated}
              className="brand-message-editor"
              data-testid="userMessageText"
              value={message}
              placeholder={placeHolderText}
              style={{
                backgroundColor: card?.giftMessage?.backgroundColor,
                fontSize: `${card?.giftMessage?.fontSize}pt`,
                fontFamily: `${card?.giftMessage?.fontFamily}`,
              }}
              rows={5}
            >
              {message}
            </textarea>
            <div
              className={`${styles["brand-user-message__control-panel-block"]} ${styles["brand-user-message__word-count-block"]}`}
            >
              {/* <span className={styles["brand-user-message__word-count"]}>{`${
                message?.length
              }/${MAX_MESSSAGE_LENGTH} ${charatersText}`}</span> */}
            </div>
          </div>
          <div
            className={`${styles["brand-user-message__control-panel"]}`}
          >
            <div
              className={styles["brand-user-message__control-panel-block"]}
            >
              <BackGroundColorMenu
                selectedColor={card?.giftMessage?.backgroundColor}
                onBackGroundColorUpdated={onBackGroundColorUpdated}
              />
            </div>
            <div
              className={styles["brand-user-message__control-panel-block"]}
            >
              <FontFamilyMenu
                onFontUpdated={onFontUpdated}
                locale={"en"}
                selectedFont={card?.giftMessage?.fontFamily}
              />
            </div>
            <div
              className={styles["brand-user-message__control-panel-block"]}
            >
              <FontSizeMenu
                selectedSize={card?.giftMessage?.fontSize}
                onFontSizeUpdated={onFontSizeUpdated}
              />
            </div>
          </div>
        </div>
      </div>
      <div className={`brand-button ${styles["button-container"]}`}>
        <Button
          theme="at-work-primary"
          action={onContinueClicked}
          className={`brand-button__continue ${styles["brand-button__continue"]}`}
          attribues={{
            type: "button",
            disabled: disableContinue,
          }}
        >
          {t("continue")}
        </Button>
      </div>
    </>
  );
};

export default BrandUserMessage;
