import React from "react";
import styles from "./BrandHeader.module.scss";
import { imageBaseUrl } from "@constants/envVariables";

const BrandHeader = ({
  title,
  actionLabel,
  action,
  onBack,
}: {
  title: string;
  actionLabel: string;
  action?: () => void;
  onBack?: () => void;
}) => {
  return (
    <div className={styles["brand-header"]}>
      <div className={styles["brand-header__back"]} onClick={onBack}>
        <img
          src={`${imageBaseUrl}/icons/arrow-circle-left.svg`}
          alt="image"
        />
        <h5>{title}</h5>
      </div>
      <span className={styles["brand-header__action"]} onClick={action}>
        {actionLabel}
      </span>
    </div>
  );
};

export default BrandHeader;
