@import "@styles/variables";
@import "@styles/mixins";

.brand-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: $white;
  // position: sticky;
  top: 0;
  z-index: 1000;
  margin-bottom: 24px;

  &__back {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    h5 {
      margin: 0;
      font-size: 24px;
      font-style: normal;
      font-weight: 800;
      line-height: 24px;
      font-family: $bricolage-font-family;
      font-optical-sizing: none;
    }

    img {
      width: 32px;
      height: 32px;
    }
  }

  &__action {
    color: $dark-purple;
    cursor: pointer;
    display: flex;
    height: 32px;
    padding: 4px 16px;
    justify-content: center;
    align-items: center;
    border-radius: 24px;
    background: #f5f5f5;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 128.571% */
    letter-spacing: -0.14px;
  }
}
