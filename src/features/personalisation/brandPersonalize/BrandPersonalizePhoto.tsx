import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';
import styles from './BrandPersonalize.module.scss';
import BrandPhotoEditor from './brandPhotoEditor/BrandPhotoEditor';
import { setNotifierState } from '@features/common/commonSlice';
import { fileSizeByMB } from '@utils/getFileSize';
import { useAppDispatch } from '@redux/hooks';
import { imageBaseUrl } from '@constants/envVariables';
import useApp from '@features/common/common.context';

interface BrandPersonalizePhotoInterface {
    onImageUploaded: (fileName: string, file: any,) => void;
}

/**
 * @method BrandPersonalizePhoto
 * @description Brand personalization component
 * @returns
 */
const BrandPersonalizePhoto = ({
    onImageUploaded,
}: BrandPersonalizePhotoInterface): JSX.Element => {
    // translations
    const { t } = useTranslation('common');
    const {state}= useApp();
    console.log(state?.secureConfigs);


    const addPhotoIcon = `${imageBaseUrl}/icons/photo.svg`;

    // #. state to manage the photo editor component
    const [imagePath, setImagePath] = useState('');
    const [fileName, setFileName] = useState('');
    const [openEditor, setOpenEditor] = useState(false);

    // #. File upload ref
    const fileUpload: any = useRef(null);

    // #. Max image size set as 15MB
    const MAX_IMG_FILE_SIZE = 15;

    // #. Notification dispatch
    const notifierDispatch = useAppDispatch();

    /**
     * @method onClickPhotoEditor
     */
    const onClickPhotoEditor = () => {
        if (fileUpload?.current) {
            // #. Clear the existing value, otherwise previously selected file will not opened
            fileUpload!.current!.value = '';
            // #. Trigger the click
            fileUpload?.current?.click();
        }
    };

    /**
     * @method showMaxSizeWarning
     * @description Show file size warning notification
     * @param sizeInfo
     */
    const showMaxSizeWarning = (sizeInfo: string) => {
        // #. Show the notifier
        notifierDispatch(
            setNotifierState({
                title: t('errorTitle'),
                description: t('photoFileSizeWarn', { size: sizeInfo }),
                icon: 'errorOutline',
            })
        );
    };

    /**
     * @method onFileUploadChanged
     * @param event
     */
    const onFileUploadChanged = (event: any) => {
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];

            // #. Calculate and check size for futher proceedings
            const size = fileSizeByMB(file.size);
            if (size > MAX_IMG_FILE_SIZE) {
                showMaxSizeWarning(size.toFixed(2));
                return false;
            }

            const path = URL.createObjectURL(file);
            setImagePath(path);
            setFileName(file.name);
            setOpenEditor(true);
        } else {
            setOpenEditor(false);
        }
    };

    return (
        <>
            <div
                className={`${styles['brand-personalize__container-box']}`}
                onClick={onClickPhotoEditor}
                data-testid="personalizePhotoContainer"
            >
                <input
                    type="file"
                    className={styles['brand-photo-fileupload']}
                    id="brand-photo-fileupload"
                    ref={fileUpload}
                    onChange={onFileUploadChanged}
                    accept="image/png, image/jpeg"
                    data-testid="personalizePhotoUpload"
                />
                <span className={`${styles['brand-personalize__icon']}`}>
                    <img src={addPhotoIcon} alt="add video" />
                </span>
                <a
                    className={`${styles['brand-personalize__title']}`}
                    data-testid="personalizePhotoUploadTitle"
                >
                    {t('addPhoto')}
                </a>
            </div>
            {openEditor && imagePath && (
                <BrandPhotoEditor
                    imagePath={imagePath}
                    fileName={fileName}
                    onImageUploaded={onImageUploaded}
                    setOpenEditor={setOpenEditor}
                />
            )}
        </>
    );
};

export default BrandPersonalizePhoto;
