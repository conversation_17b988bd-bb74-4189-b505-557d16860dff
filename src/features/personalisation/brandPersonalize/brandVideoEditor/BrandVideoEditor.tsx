import { createElement, useEffect, useState } from "react";
import { Dialog, DialogContent } from "@mui/material";
import Button from "@features/common/button/Button";
import styles from "./BrandVideoEditor.module.scss";
import { useTranslation } from "next-i18next";
import { fileSizeByMB, getVideoDuration } from "@utils/getFileSize";
import { useAppDispatch } from "@redux/hooks";
import { setNotifierState } from "@features/common/commonSlice";
import cameraTagUI from "@features/common/cameraTag/cameraTagUI";
import { captureException } from "@sentry/nextjs";
import useApp from "@features/common/common.context";
import { imageBaseUrl } from "@constants/envVariables";

const uploadIconUrl = `${imageBaseUrl}/icons/document-upload.svg`;
const recordIconUrl = `${imageBaseUrl}/icons/video.svg`;
const publishedIconUrl = `${imageBaseUrl}/icons/published.svg`;
const saveIconUrl = `${imageBaseUrl}/icons/save.svg`;
const recordAgainIconUrl = `${imageBaseUrl}/icons/record-again.svg`;
const previewIconUrl = `${imageBaseUrl}/icons/preview.svg`;

/**
 * @method BrandVideoEditor
 * @description Brand video editor dialog component
 * @returns
 */
const BrandVideoEditor = ({
  onCloseVideoEditor,
  onVideoProcessed,
  setOpenEditor,
}: any): JSX.Element => {
  // #. Get translations
  const { t } = useTranslation("common");

  // #. Notification dispatch
  const notifierDispatch = useAppDispatch();

  // #. Set all translated titles
  const titleContinute = t("continue");
  const titleUploading = t("uploadingVideo");
  const titleProcessing = t("processingVideo");

  const { state } = useApp();
  const { cameraTagAppId } = state?.secureConfigs || {};

  // #. Dialog state
  const [openEditorDialog, setOpenEditorDialog] = useState(false);
  const [videoInfo, setVideoInfo] = useState<any>({});
  const [proceedButtonTitle, setProceedButtonTitle] =
    useState<string>(titleContinute);

  const CAMERA_TAG_APP_ID = cameraTagAppId;
  const CAMERA_TAG_ID = "ygag-camera";

  // #. Max file size should be 100MB
  const MAX_VIDEO_FILE_SIZE = 100;
  // #. Max video length should be 30sec
  const MAX_VIDEO_DURATION = 30;

  let cameraTag: any;

  useEffect(() => {
    setOpenEditorDialog(true);

    setTimeout(() => {
      // #. Get global camera tag object
      const cameraTagRef = (window as any).CameraTag;
      try {
        if (cameraTagRef) {
          // #. Trigger the setup
          cameraTagRef.setup();
          // #. Get the initialized camera
          cameraTag = cameraTagRef.cameras[CAMERA_TAG_ID];

          // #. Listen the events
          setTimeout(() => {
            // #. Subscribe initialized event to customize the interface
            cameraTagRef.observe(CAMERA_TAG_ID, "initialized", function () {
              updateStartScreen();
            });

            cameraTagRef.observe(CAMERA_TAG_ID, "cameraDenied", function () {
              updateHardwareAccessErrorUI();
            });

            // #. Subscribe video upload started event
            cameraTagRef.observe(
              CAMERA_TAG_ID,
              "uploadFileSelected",
              function (file: any) {
                showFileTypeWarning(file?.type);
                const fileSize = fileSizeByMB(file?.size);
                getVideoDuration(file).then((duration: any) => {
                  if (fileSize > MAX_VIDEO_FILE_SIZE) {
                    showMaxSizeWarning(
                      t("videoFileSizeWarn", {
                        size: fileSize.toFixed(2),
                      }),
                    );
                  } else if (duration > MAX_VIDEO_DURATION) {
                    showMaxSizeWarning(t("videoDurationWarn"));
                  } else {
                    cameraTag?.startUpload(file);
                  }
                });

                // .# Show uploading status placeholder
                uploadingPlaceholder();
              },
            );

            // #. Subscribe video upload started event
            cameraTagRef.observe(CAMERA_TAG_ID, "uploadStarted", function () {
              setProceedButtonTitle(titleUploading);
            });

            // #. Subscribe video upload started event
            cameraTagRef.observe(CAMERA_TAG_ID, "published", function () {
              setProceedButtonTitle(titleProcessing);
            });

            // #. Subscribe processed event
            cameraTagRef.observe(
              CAMERA_TAG_ID,
              "processed",
              function (event: any) {
                const video = cameraTag.getVideo();
                const uuid = video.uuid;

                // #. Update the button title
                setProceedButtonTitle(titleContinute);

                // #. Update the video info to context
                setVideoInfo({
                  uuid,
                  medias: {
                    mp4: event?.preview_mp4_url || video?.qvga_mp4,
                    ...video?.medias,
                  },
                });
              },
            );
            cameraTagRef.observe(
              CAMERA_TAG_ID,
              "recordingStopped",
              function () {
                updateAcceptScreen();
              },
            );
            cameraTagRef.observe(CAMERA_TAG_ID, "published", function () {
              updatePublishedScreen();
            });
            cameraTagRef.observe(CAMERA_TAG_ID, "playbackStarted", function () {
              updatePreviewScreen();
            });
            cameraTagRef.observe(CAMERA_TAG_ID, "playbackStopped", function () {
              updatePreviewStoppedScreen();
            });
            cameraTagRef.observe(CAMERA_TAG_ID, "uploadStarted", function () {
              updateUploadingScreen();
            });
            cameraTagRef.observe(
              CAMERA_TAG_ID,
              "countdownStarted",
              function () {
                updateCountdownScreen();
              },
            );
            cameraTagRef.observe(
              CAMERA_TAG_ID,
              "uploadProgress",
              function (percentageVal: any) {
                updateProgressScreen(percentageVal);
              },
            );
            cameraTagRef.observe(CAMERA_TAG_ID, "publishing", function () {
              // .# Show uploading status placeholder
              uploadingPlaceholder();
            });
          }, 150);
        }
      } catch (error) {
        console.log(error);
        captureException(error);
      }
    }, 50);

    return () => {
      if (cameraTag) {
        cameraTag.destroy();
      }
    };
  }, []);

  /**
   * @method onDialogClose
   */
  const onDialogClose = () => {
    setOpenEditorDialog(false);
    setOpenEditor && setOpenEditor(false);
    onCloseVideoEditor && onCloseVideoEditor();
  };

  /**
   * @method onDialogClose
   */
  const onVideoSubmitted = () => {
    onVideoProcessed && onVideoProcessed(videoInfo);
    onDialogClose();
  };

  /**
   * @method showMaxSizeWarning
   * @description Show file size warning notification
   * @param sizeInfo
   */
  const showMaxSizeWarning = (message: string) => {
    // #. Show the notifier
    notifierDispatch(
      setNotifierState({
        title: t("errorTitle"),
        description: message,
        icon: "errorOutline",
      }),
    );
  };

  // #. Import camera tag UI custom API
  const {
    updateStartScreen,
    updateHardwareAccessErrorUI,
    updateAcceptScreen,
    updatePublishedScreen,
    updatePreviewScreen,
    updatePreviewStoppedScreen,
    updateUploadingScreen,
    updateCountdownScreen,
    updateProgressScreen,
    uploadingPlaceholder,
  } = cameraTagUI({
    t,
    uploadIconUrl,
    recordIconUrl,
    onDialogClose,
    publishedIconUrl,
    saveIconUrl,
    recordAgainIconUrl,
    previewIconUrl,
  });

  /**
   * @method showFileTypeWarning
   * @description Show file type warning
   * @param fileType
   */
  const showFileTypeWarning = (fileType: string) => {
    if (!fileType?.includes("video")) {
      // #. Show the notifier
      notifierDispatch(
        setNotifierState({
          title: t("errorTitle"),
          description: t("unsupportedFileFormat"),
          icon: "errorOutline",
        }),
      );
    }
  };

  return (
    <div className={`brand-video-editor  ${styles["brand-video-editor"]}`}>
      <Dialog
        open={openEditorDialog}
        onClose={onDialogClose}
        data-testid="brandVideoDialog"
      >
        <DialogContent>
          <div className="brand-video-editor-tool">
            {createElement("camera", {
              "data-app-id": CAMERA_TAG_APP_ID,
              id: CAMERA_TAG_ID,
              is: "x3d",
              "data-maxlength": 30,
              "data-testid": "brandVideoCamera",
              "data-enforce-mobile-length": true,
              "data-upload-on-select": false,
            })}
          </div>
        </DialogContent>
      </Dialog>
      <div className={styles["brand-video-editor__button-panel"]}>
        <Button
          theme="at-work-primary"
          action={onVideoSubmitted}
          className={`${!videoInfo?.uuid ? styles["disabled"] : ""}`}
          data-testid="brandVideoSubmit"
        >
          {proceedButtonTitle}
        </Button>
      </div>
    </div>
  );
};

export default BrandVideoEditor;
