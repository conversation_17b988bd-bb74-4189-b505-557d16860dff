import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import { useState } from 'react';
import styles from './BrandPersonalize.module.scss';
import BrandVideoEditor from './brandVideoEditor/BrandVideoEditor';
import { imageBaseUrl } from '@constants/envVariables';

interface BrandPersonalizeVideoInterface {
    onVideoProcessed: (videoInfo: any) => void;
}

/**
 * @method BrandPersonalizeVideo
 * @description Brand personalization component
 * @returns
 */

const BrandPersonalizeVideo = ({
    onVideoProcessed,
}: BrandPersonalizeVideoInterface): JSX.Element => {

    const addVideoIcon = `${imageBaseUrl}/icons/video.svg`;

    // translations
    const { t } = useTranslation('common');
    const [openEditor, setOpenEditor] = useState(false);

    /**
     * @method onVideoEditorClicked
     */
    const onVideoEditorClicked = () => {
        setOpenEditor(true);
    };

    /**
     * @method onVideoEditorClosed
     */
    /* istanbul ignore next */
    const onVideoEditorClosed = () => {
        // #. Close the dialog
        setOpenEditor(false);
    };

    /**
     * @method onVideoProcessed
     * @param videoInfo
     */
    /* istanbul ignore next */
    const onAfterVideoProcessed = (videoInfo?: any) => {
        onVideoProcessed && onVideoProcessed(videoInfo);
        // #. Close the dialog
        onVideoEditorClosed();
    };

    return (
        <>
            <div
                className={`${styles['brand-personalize__container-box']}`}
                onClick={onVideoEditorClicked}
                data-testid="personalizeVideoContainer"
            >
                <span
                    className={`${styles['brand-personalize__icon']}`}
                    data-testid="personalizeVideoIcon"
                >
                    <img src={addVideoIcon} alt="add video" />
                </span>
                <a
                    className={`${styles['brand-personalize__title']}`}
                    data-testid="personalizeVideoTitle"
                >
                    {t('addVideo')}
                </a>
            </div>
            {openEditor && (
                <BrandVideoEditor
                    onVideoProcessed={onAfterVideoProcessed}
                    onCloseVideoEditor={onVideoEditorClosed}
                />
            )}
        </>
    );
};

export default BrandPersonalizeVideo;
