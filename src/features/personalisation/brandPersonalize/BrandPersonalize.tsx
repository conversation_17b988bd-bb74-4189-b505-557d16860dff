import { useEffect, useRef, useState } from "react";
import styles from "./BrandPersonalize.module.scss";
import BrandPersonalizeEdit from "./brandPersonalizeEdit";
import BrandPersonalizePhoto from "./BrandPersonalizePhoto";
import BrandPersonalizeVideo from "./BrandPersonalizeVideo";
import { BRAND_FORM_STATE, UPLOAD_TYPES } from "@constants/common";
import PersonalizeGif from "./personalizeGif/PersonalizeGif";
import useApp, { AppContextAction } from "@features/common/common.context";
import { useTranslation } from "next-i18next";
import { imageBaseUrl } from "@constants/envVariables";
import CameraTag from "@features/common/cameraTag/CameraTag";
import BrandHeader from "./brandHeader/BrandHeader";

/**
 * @method BrandPersonalize
 * @description Brand personalization component
 * @returns
 */
const BrandPersonalize = ({
  onContinue,
  onCancel,
}: {
  onContinue: () => void;
  onCancel: () => void;
}): React.JSX.Element => {
  // #. Transition states
  const nodeRef = useRef(null);
  const { t } = useTranslation();
  // #. Get brand context
  const {
    state: { card, stepper },
    dispatch,
  } = useApp();

  // #. Destructured image and video details from saved data
  const { photo, video, gif } = card?.personalisation || {};
  const fileName = photo?.fileName || "";
  const imagePath = photo?.file || "";
  const videoUuid = video?.medias?.mp4 || "";
  const gifInfo = gif?.url;

  /**
   * @method dispatchBrandContext
   * @param value
   */
  /* istanbul ignore next */
  const dispatchBrandContext = (
    value: Object | any,
    type: UPLOAD_TYPES,
  ) => {
    let newValue = {};
    if (type === UPLOAD_TYPES.PHOTO) {
      newValue = {
        personalisation: {
          photo: {
            file: value?.file,
            fileName: value?.fileName,
            prevFile:
              card?.formState === BRAND_FORM_STATE.EDIT
                ? card?.personalisation?.photo?.fileName
                : "",
          },
          video: {},
        },
      };
    } else if (type === UPLOAD_TYPES.VIDEO) {
      newValue = {
        personalisation: {
          video: { ...value },
          photo: {
            path: "",
            fileName: "",
          },
        },
      };
    } else if (type === UPLOAD_TYPES.GIF) {
      newValue = {
        personalisation: {
          video: {},
          photo: {},
          gif: {
            url: value?.gifUrl,
            id: value?.gifId,
          },
        },
      };
    } else {
      newValue = {};
    }

    dispatch({ type: AppContextAction.CARD, payload: newValue });
  };

  /**
   * @method onVideoProcessed
   * @param videoInfo
   */
  /* istanbul ignore next */
  const onVideoProcessed = (videoInfo?: any) => {
    dispatchBrandContext(videoInfo, UPLOAD_TYPES.VIDEO);
  };

  /**
   * @method onImageUploaded
   * @param imageUrl
   */

  const onImageUploaded = (fileName: string, file: any) => {
    dispatchBrandContext({ fileName, file }, UPLOAD_TYPES.PHOTO);
  };

  /**
   * @method onSkipClicked
   */
  /* istanbul ignore next */
  const onSkipClicked = () => {
    onContinue();

    window.scrollTo(0, 0);
  };

  /**
   * @method onDiscardButtonClicked
   */
  const onDiscardButtonClicked = () => {
    clearContext();
  };

  const clearContext = () => {
    let newValue = {
      personalisation: {
        video: {},
        photo: {
          blob: "",
          file: "",
          fileName: "",
          prevFile: "",
        },
      },
    };

    dispatch({ type: AppContextAction.CARD, payload: newValue });
  };

  const onAfterPreview = () => {
    onContinue();
  };

  /**
   * @method onGifUploaded
   * @param gif
   */

  const onGifUploaded = (data: any) => {
    const gifId = data?.id || "";
    const gifUrl = data?.images?.original?.url || "";
    dispatchBrandContext({ gifId, gifUrl }, UPLOAD_TYPES.GIF);
  };

  return (
    <>
      {fileName || videoUuid || gifInfo ? (
        <BrandPersonalizeEdit
          imagePath={imagePath}
          fileName={fileName}
          videoInfo={videoUuid}
          gifInfo={gifInfo}
          onImageUploaded={onImageUploaded}
          onGifUploaded={onGifUploaded}
          onDiscardButtonClicked={onDiscardButtonClicked}
          onAfterPreview={onAfterPreview}
          onVideoProcessed={onVideoProcessed}
        />
      ) : (
        <>
          <BrandHeader
            title={"Add Media"}
            actionLabel={t("skip")}
            action={onSkipClicked}
            onBack={onCancel}
          />
          <div
            ref={nodeRef}
            className={`${styles["brand-personalize"]} brand-accordion`}
          >
            <img
              src={`${imageBaseUrl}/images/brand-media.png`}
              alt="image"
              style={{
                width: "100%",
                height: "120px",
                marginBottom: "10px",
              }}
            />
            <div className={styles["upload-container"]}>
              <span className={styles["upload-container__circle"]}>
                {t("or")}
              </span>
              <div className={styles["upload-contents"]}>
                <BrandPersonalizePhoto onImageUploaded={onImageUploaded} />
                <BrandPersonalizeVideo
                  onVideoProcessed={onVideoProcessed}
                />
                <PersonalizeGif onGifUploaded={onGifUploaded} />
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default BrandPersonalize;
