import React, { useEffect, useState } from 'react';
import styles from './PersonalizeGifEditor.module.scss';
import {
    Grid,
    SearchBar,
    SearchContext,
    SearchContextManager,
} from '@giphy/react-components';
import 'isomorphic-fetch';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { SwipeableDrawer } from '@mui/material';
import { giphyKey } from '@constants/envVariables';

const PersonalizeGifEditor = ({
    onCloseGifEditor,
    onGifProcessed,
    setOpenEditor,
    setdisableButtons,
}: any): React.JSX.Element => {
    const { t } = useTranslation();

    // #.Drawer states
    const [state, setState] = React.useState({
        bottom: false,
    });
    const [isActive, setActive] = useState<boolean>(false);

    /**
     * @method onDrawerClose
     */
    const onDrawerClose = () => {
        setOpenEditor && setOpenEditor(false);
        setdisableButtons && setdisableButtons(false);
        onCloseGifEditor && onCloseGifEditor();
    };

    /**
     * @method onGifSubmitted
     */
    const onGifSubmitted = (gif: any) => {
        onGifProcessed && onGifProcessed(gif);
        onDrawerClose();
    };

    const Components = () => {
        const { fetchGifs, term, activeChannel } =
            useContext(SearchContext) || {};
        return (
            <div>
                <SearchBar
                    className={`giphy-searchbar`}
                    placeholder={t('searchGif')}
                />
                <Grid
                    key={`${term} ${activeChannel?.user.username}`}
                    columns={2}
                    width={window ? window.innerWidth : 400}
                    onGifClick={(e) => onGifSubmitted(e)}
                    fetchGifs={fetchGifs}
                    noResultsMessage={
                        <div className="giphy-empty-message">
                            {t('noResultsFound')}
                        </div>
                    }
                    gutter={5}
                    noLink={true}
                    hideAttribution={true}
                />
            </div>
        );
    };

    useEffect(() => {
        setState({ bottom: true });
    }, []);

    /**
     * @method toggleDrawer
     * @param anchor
     * @param open
     * @param offer
     */
    const toggleDrawer =
        (anchor: 'bottom', open: boolean) =>
        (event: React.KeyboardEvent | React.MouseEvent) => {
            if (
                event &&
                event.type === 'keydown' &&
                ((event as React.KeyboardEvent).key === 'Tab' ||
                    (event as React.KeyboardEvent).key === 'Shift')
            ) {
                return;
            }
            setState({ ...state, [anchor]: open });
            setActive(!isActive);
        };

    const handleClose = () => {
        setState({ bottom: false });
        onDrawerClose();
    };

    return (
        <div className={`brand-gif-editor  ${styles['brand-gif-editor']}`}>
            <SwipeableDrawer
                className={'drawer-custom drawer-bottom-custom'}
                anchor={'bottom'}
                open={state['bottom']}
                onClose={handleClose}
                onOpen={toggleDrawer('bottom', true)}
            >
                <div className="brand-gif-editor-tool">
                    <SearchContextManager apiKey={giphyKey || ''}>
                        <Components />
                    </SearchContextManager>
                </div>
            </SwipeableDrawer>
        </div>
    );
};

export default PersonalizeGifEditor;
