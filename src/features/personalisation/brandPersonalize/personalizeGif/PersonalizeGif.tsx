import React, { useState } from 'react';
import styles from '../BrandPersonalize.module.scss';
import PersonalizeGifEditor from './personalizeGifEditor/PersonalizeGifEditor';
import { useTranslation } from 'react-i18next';
import { imageBaseUrl } from '@constants/envVariables';

interface PersonalizeGifInterface {
    onGifUploaded: (gifInfo: any) => void;
}

const PersonalizeGif = ({
    onGifUploaded,
}: PersonalizeGifInterface): JSX.Element => {
    const { t } = useTranslation();

    // #. Icons
    const gifIcon = `${imageBaseUrl}/icons/gif.svg`;

    const [openEditor, setOpenEditor] = useState(false);

    /**
     * @method onGifEditorClicked
     */
    const onGifEditorClicked = () => {
        setTimeout(() => {
            setOpenEditor(true);
        }, 100);
    };

    /**
     * @method onGifEditorClosed
     */
    /* istanbul ignore next */
    const onGifEditorClosed = () => {
        // #. Close the dialog
        setOpenEditor(false);
    };

    /**
     * @method onGifProcessed
     * @param gifInfo
     */
    /* istanbul ignore next */
    const onAfterGifProcessed = (gifInfo?: any) => {
        onGifUploaded && onGifUploaded(gifInfo);
        // #. Close the dialog
        onGifEditorClosed();
    };

    return (
        <>
            <div
                className={`${styles['upload-contents-add']} ${styles['upload-contents-add--gif']}`}
                onClick={onGifEditorClicked}
            >
                <img src={gifIcon} width="32" height="32" alt="" />
                {t('addGif')}
            </div>
            {openEditor && (
                <PersonalizeGifEditor
                    onGifProcessed={onAfterGifProcessed}
                    onCloseGifEditor={onGifEditorClosed}
                />
            )}
        </>
    );
};

export default PersonalizeGif;
