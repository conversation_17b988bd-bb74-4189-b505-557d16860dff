@import '@styles/variables';
@import '@styles/mixins';

.brand-photo-editor {
    position: absolute;

    &__button-panel {
        padding: 0 15px;
        position: fixed;
        width: 100%;
        bottom: 0;
        z-index: 1500;
        background: white;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 75px;
        gap: 17px;
        font-weight: 600;

        @include font-size(500);

        button {
            width: 100%;
            height: 45px;
        }
    }

    &__button-discard {
        color: $dark-purple !important;
        border: 1px solid $dark-purple !important;
    }

    .circular-loader {
        z-index: 9999;
        position: fixed;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
    }
}

.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.loader-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0.5;
    z-index: 9999;
    width: 100%;
    height: 100%;
}
