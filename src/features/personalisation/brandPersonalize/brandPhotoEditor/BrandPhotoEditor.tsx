import { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@mui/material";
import PhotoEditorSDK from "@features/common/photoEditorSDK/PhotoEditorSDK";
import { UIEvent, ImageFormat, ExportFormat } from "photoeditorsdk";
import Button from "@features/common/button/Button";
import styles from "./BrandPhotoEditor.module.scss";
import { useTranslation } from "next-i18next";
import CircularProgress from "@mui/material/CircularProgress";
import useApp from "@features/common/common.context";

interface BrandPhotoEditorInterface {
  imagePath: string;
  fileName: string;
  onImageUploaded: (fileName: string, file: any) => void;
  setOpenEditor: any;
}

/**
 * @method BrandPhotoEditor
 * @description Brand photo editor dialog component
 * @returns
 */
const BrandPhotoEditor = ({
  imagePath,
  fileName,
  onImageUploaded,
  setOpenEditor,
}: BrandPhotoEditorInterface): React.JSX.Element => {
  // #. Get translations
  const { t } = useTranslation("common");
  const buttonSubmitTitle = t("continue");
  const { state } = useApp();
  const { imglyLicenseKey: APP_KEYS } = state?.secureConfigs || {};


  const [isLoading, setIsLoading] = useState<boolean>(false);

  // #. Dialog state
  const [open, setOpen] = useState(false);
  const [disableButtons, setDisableButtons] = useState(false);
  const [submitButtonTitle, setSubmitButtonTitle] = useState(buttonSubmitTitle);
  let photoEditor: any = null;

  useEffect(() => {
    if (imagePath) {
      setOpen(true);
    }

    return () => {
      if (photoEditor?.close) {
        photoEditor?.close();
      }
      photoEditor = null;
    };
  }, [imagePath]);

  /**
   * @method onDialogClose
   */
  const onDialogClose = () => {
    setOpen(false);
    setOpenEditor(false);
  };

  /**
   * @method onEditorInitialized
   * @param params
   */
  const onEditorInitialized = (editor: any) => {
    if (editor) {
      photoEditor = editor;

      // #. Export event listener
      photoEditor?.on(UIEvent.EXPORT, async (image: string) => {
        await uploadPhotoToStorage(image);
        onDialogClose();
      });
    }
  };

  /**
   * @method exportEditedImage
   */
  const exportEditedImage = async () => {
    // #. Do export via custom button
    if (photoEditor) {
      const image = await photoEditor.export({
        format: ImageFormat.JPEG, // `image/png` or `image/jpeg`
        exportType: ExportFormat.DATA_URL, // `image`, `data-url` or `blob`
        quality: 0.9, // 0.1 - 1.0, defines the quality of jpeg images
        enableDownload: false, // boolean, enable or disable the automatic file download
        preventExportEvent: true, // boolean, enable or disable the UIEvent.EXPORT which is called on every export
      });

      // #. Pass the image blob
      await uploadPhotoToStorage(image);
    }
    onDialogClose();
  };

  /**
   * @method uploadPhotoToStorage
   * @param imageBlob
   */
  const uploadPhotoToStorage = async (imageURL?: string) => {
    try {
      setDisableButtons(true);
      setIsLoading(true);
      setSubmitButtonTitle(t("pleaseWait"));
      if (imageURL) {
        onImageUploaded && onImageUploaded(fileName, imageURL);
      }
      setDisableButtons(false);
      setIsLoading(false);
      setSubmitButtonTitle(buttonSubmitTitle);
    } catch (error) {
      // #. Handle exception
      setDisableButtons(false);
      setIsLoading(false);
      setSubmitButtonTitle(buttonSubmitTitle);
      console.log("Error uploading file: ", error);
    }
  };

  return (
    <div className={`${styles["brand-photo-editor"]}`}>
      {isLoading && (
        <div className={styles["circular-loader"]}>
          <CircularProgress />
        </div>
      )}
      {disableButtons && <div className={styles["loader-backdrop"]}></div>}
      <Dialog open={open} onClose={onDialogClose}>
        <DialogContent>
          <PhotoEditorSDK
            className="brand-photo-editor-tool"
            imagePath={imagePath}
            onEditorInitialized={onEditorInitialized}
            lkey={APP_KEYS || ""}
          />
        </DialogContent>
      </Dialog>
      <div className={styles["brand-photo-editor__button-panel"]}>
        <Button
          theme="dark-shadow"
          className={`${
            styles["brand-photo-editor__button-discard"]
          } ${disableButtons ? styles["disabled"] : ""}`}
          action={onDialogClose}
        >
          {t("discard")}
        </Button>
        <Button
          theme="at-work-primary"
          action={exportEditedImage}
          className={`${disableButtons ? styles["disabled"] : ""}`}
        >
          {submitButtonTitle}
        </Button>
      </div>
    </div>
  );
};

export default BrandPhotoEditor;
