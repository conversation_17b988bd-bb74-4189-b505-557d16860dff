import { useTranslation } from "next-i18next";
import { useEffect, useState } from "react";
import styles from "./BrandPersonalize.module.scss";
import BrandPhotoEditor from "./brandPhotoEditor/BrandPhotoEditor";
import Button from "@features/common/button/Button";
import BrandVideoEditor from "./brandVideoEditor/BrandVideoEditor";
import PersonalizeGifEditor from "./personalizeGif/personalizeGifEditor/PersonalizeGifEditor";
// import S3Bucket from "@utils/s3Bucket";
import useApp from "@features/common/common.context";
import { useParams, useRouter } from "next/navigation";
// import { isS3Regex } from "@utils/regex";
// import { convertBufferToDataURL } from "@utils/getDataURL";
import { imageBaseUrl } from "@constants/envVariables";
import BrandHeader from "./brandHeader/BrandHeader";
import ConfirmModal from "@features/common/confirmModal/ConfirmModal";
import { set } from "lodash";

interface BrandPersonalizeEditInterface {
  imagePath: string;
  fileName: string;
  videoInfo: any;
  gifInfo: string | undefined;
  onImageUploaded: (fileName: string, file: any) => void;
  onGifUploaded: (data: any) => void;
  onDiscardButtonClicked: () => void;
  onAfterPreview: () => void;
  onVideoProcessed: () => void;
}
/**
 * @method BrandPersonalizeEdit
 * @description Brand personalization component
 * @returns
 */
const BrandPersonalizeEdit = ({
  imagePath,
  fileName,
  videoInfo,
  gifInfo,
  onImageUploaded,
  onGifUploaded,
  onDiscardButtonClicked,
  onAfterPreview,
  onVideoProcessed,
}: BrandPersonalizeEditInterface): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  const router = useRouter();
  const params = useParams();
  const [confirmModal, setConfirmModal] = useState(false);

  // #. Get brand context
  const {
    state: { card },
  } = useApp();

  const editIcon = `${imageBaseUrl}/icons/personalisation-edit.svg`;
  const [openEditor, setOpenEditor] = useState(false);
  const [disableButtons, setdisableButtons] = useState(false);
  const [imageDataUrl, setImageDataUrl] = useState<any>("");
  const buttonSubmitTitle = t("continue");

  /**
   * @method onDialogClose
   */
  const onDialogClose = () => {
    setConfirmModal(!confirmModal);
  };

  const handleClose = () => {
    onDiscardButtonClicked && onDiscardButtonClicked();
  };

  /**
   * @method onVideoProcessed
   * @param videoInfo
   */

  const onClickedEdit = async () => {
    // if (!videoInfo) {
    //   if (
    //     router.query.pedit &&
    //     Boolean(card?.personalisation?.photo?.fileName) &&
    //     isS3Regex.test(card?.personalisation?.photo?.file || "")
    //   ) {
    //     const imageUrl = card?.personalisation?.photo?.fileName && "";
    //     //   (await fetchImage(card?.personalisation?.photo?.fileName));
    //     setImageDataUrl(imageUrl);
    //   } else {
    //     setImageDataUrl(card?.personalisation?.photo?.file);
    //   }
    // }
    setImageDataUrl(card?.personalisation?.photo?.file);
    setOpenEditor(true);
    if (gifInfo) {
      setdisableButtons(true);
    }
  };

  //   async function fetchImage(fileName: string) {
  //     try {
  //       const buffer = await S3Bucket.getImageBuffer(fileName);
  //       return convertBufferToDataURL(buffer);
  //     } catch (error) {
  //       console.error("Error fetching image:", error);
  //       return null;
  //     }
  //   }

  useEffect(() => {
    // #. Scroll the window to top
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  return (
    <>
      <BrandHeader
        title={"Review Media"}
        actionLabel={t("edit")}
        action={onClickedEdit}
        onBack={() => setConfirmModal(true)}
      />
      {confirmModal && (
        <ConfirmModal
          confirmModal={confirmModal}
          handleClose={onDialogClose}
          handleSubmit={handleClose}
        />
      )}
      <div>
        {videoInfo ? (
          <div
            className={styles["brand-personalize__edit__video-container"]}
          >
            <video
              src={videoInfo}
              width="100%"
              height="auto"
              controls
              playsInline
              autoPlay
            />
          </div>
        ) : (
          <div
            className={styles["brand-personalize__edit__image-container"]}
          >
            <img src={imagePath || gifInfo} alt="uploaded image" />
          </div>
        )}
      </div>

      {!disableButtons && !confirmModal && (
        <div className={styles["brand-photo-editor__button-panel"]}>
          <Button theme="dark-shadow" action={onDialogClose}>
            {t("discard")}
          </Button>
          <Button theme="at-work-primary" action={onAfterPreview}>
            {buttonSubmitTitle}
          </Button>
        </div>
      )}

      {openEditor && imagePath ? (
        <BrandPhotoEditor
          imagePath={imageDataUrl}
          fileName={fileName}
          onImageUploaded={onImageUploaded}
          setOpenEditor={setOpenEditor}
        />
      ) : openEditor && videoInfo ? (
        <BrandVideoEditor
          setOpenEditor={setOpenEditor}
          onVideoProcessed={onVideoProcessed}
        />
      ) : (
        openEditor &&
        gifInfo && (
          <PersonalizeGifEditor
            onGifProcessed={onGifUploaded}
            onCloseGifEditor={setOpenEditor}
            setdisableButtons={setdisableButtons}
          />
        )
      )}
    </>
  );
};

export default BrandPersonalizeEdit;
