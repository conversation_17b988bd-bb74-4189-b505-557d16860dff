@import "@styles/variables";
@import "@styles/mixins";

.brand-personalize {
  width: 100%;

  &__container {
    padding: 12px;
    border-radius: 12px;

    &-block {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 20px;
      position: relative;
    }

    &-box {
      background-color: $whitish;
      padding: 34px 20px;
      border-radius: 12px;
      display: flex;
      gap: 8px;
      flex-direction: column;
      align-items: center;
      width: 100%;
      cursor: pointer;
    }

    &-or-info {
      background-color: $pale-grey;
      color: $black-header;
      position: absolute;
      left: 0;
      right: 0;
      margin-left: auto;
      margin-right: auto;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      @include font-size(16);

      @include rtl-styles {
        padding: 10px 20px;
      }
    }
  }

  &__icon {
    > img {
      width: 32px;
      height: 32px;
    }
  }

  &__title {
    @include font-size(16);
    color: $dark-purple;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    cursor: pointer;
  }

  &__note {
    @include font-size(10);

    color: $dark-grey;
    text-align: center;
  }

  &__button-container {
    text-align: center;
    margin-top: 56px;
  }

  &__edit {
    &__image-container {
      border-radius: 16px;
      height: calc(100vh - 230px);

      > img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 16px;
        // object-fit: cover;
      }
    }

    &__video-container {
      border-radius: 16px;

      > video {
        border-radius: 16px;
      }
    }

    &__bottom {
      display: flex;
      justify-content: center;
      margin-top: 26px;

      > img {
        width: 16px;
        height: 16px;
      }

      > h5 {
        text-transform: capitalize;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 16px;
        letter-spacing: -0.14px;
        color: $dark-purple;
        margin: 0 0 0 5px;
      }
    }
  }
}

.brand-photo-fileupload {
  display: none;
  opacity: 0;
}

.brand-photo-editor {
  position: absolute;

  &__button-panel {
    padding: 0 15px;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 1500;
    background: white;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 75px;
    gap: 17px;
    font-weight: 600;

    @include font-size(500);

    button {
      width: 100%;
      height: 50px;
    }
  }

  &__button-discard {
    color: $dark-purple !important;
    border: 2px solid $dark-purple !important;
  }

  .circular-loader {
    z-index: 9999;
    position: fixed;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.loader-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0.5;
  z-index: 9999;
  width: 100%;
  height: 100%;
}

.upload-contents {
  margin-top: 14px;
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  position: relative;

  &-add {
    background-color: $whitish;
    height: 150px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
    color: $dark-purple;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    cursor: pointer;

    &--photo,
    &--video {
      grid-row: 1;
    }
    &--gif {
      grid-row: 2;
      grid-column: span 2;
    }

    input {
      display: none;
      opacity: 0;
    }
  }
}

.upload-container {
  position: relative;

  &__circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $white;
    z-index: 999;
    text-transform: lowercase;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 150% */
    letter-spacing: -0.16px;
  }
}
