import { HEADER_TYPE, PLATFORM_TYPE } from "@constants/common";
// import { GIFT_QUERY } from "@features/feedback/feedback.query";
import { initializeApollo } from "@graphql/apolloClient";
import {
  CommonDataInterface,
  ThemeSettingsInterface,
} from "@interfaces/common.inteface";
import {
  COMMON_QUERY,
  HEADER_LOGO_QUERY,
  LOGO_QUERY,
  SECURE_LICENCE_QUERY,
  SITE_CONFIG_QUERY,
  STORES_QUERY,
  THEME_SETTINGS_QUERY,
  GUEST_ENABLED,
  BLACK_LISTED_CONTRIES_QUERY,
  CAPTCHA_CONFIG_QUERY,
  COMMUNICATION_CONFIG_QUERY,
  PROFILE_SITE_CONFIG_QUERY,
  USER_DETAILS_QUERY,
} from "./common.query";
import * as Sentry from "@sentry/nextjs";
import { useQuery } from "@apollo/client";
const DEFAULT_REGION = "ae";

// Initialize appllo client
const commonDataQuery = async (
  locale: string,
  countryCode: string,
  ip: any
) => {
  const apolloClient = initializeApollo(locale, ip);
  const storeCode = countryCode?.toUpperCase() || DEFAULT_REGION.toUpperCase();

  try {
    return await apolloClient.query({
      query: COMMON_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        country_Code: storeCode,
      },
      context: {
        headers: {
          "Yg-Ip-Address": ip,
        },
        clientName: "webstore-with-cdn",
      },
    });
  } catch (e: any) {
    Sentry.captureException("Common Data fetch failed" + e?.message);
    // handle exception here
    return {
      data: null,
      networkStatus: e?.graphQLErrors[0]?.status_code,
    };
  }
};

// Get stores data
export const storesDataQuery = async (locale: string, ip: any) => {
  const apolloClient = initializeApollo(locale, ip);

  try {
    return await apolloClient.query({
      query: STORES_QUERY,
      context: {
        headers: {
          "Yg-Ip-Address": ip,
        },
        clientName: "webstore-with-cdn",
      },
    });
  } catch (e: any) {
    Sentry.captureException(
      `Stores Data fetch failed, locale ${JSON.stringify(locale)}, error ${e?.message
      }`
    );
    // handle exception here
    return {
      data: null,
      networkStatus: e?.graphQLErrors[0]?.status_code,
    };
  }
};

// Initialize appllo client
export const secureLicenceQuery = async (locale: string, ip?: any) => {
  const apolloClient = initializeApollo(locale, ip);
  return await apolloClient.query({
    query: SECURE_LICENCE_QUERY,
    context: {
      headers: {
        "Yg-Ip-Address": ip,
      },
      clientName: "webstore-with-cdn",
    },
  });
};

export const logoDataQuery = async (locale: string) => {
  const apolloClient = initializeApollo(locale);
  return await apolloClient.query({
    query: LOGO_QUERY,
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
    },
  });
};

// export const brandImageQuery = async (locale: string, slugId: string) => {
//   const apolloClient = initializeApollo(locale);
//   return await apolloClient.query({
//     query: GIFT_QUERY,
//     variables: { referenceId: slugId },
//     context: {
//       clientName: "feedback",
//     },
//   });
// };

export const logoDataQueryByHeaderType = async (
  locale: string,
  headerType: HEADER_TYPE
) => {
  const apolloClient = initializeApollo(locale);
  return await apolloClient.query({
    query: HEADER_LOGO_QUERY,
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
      headerType_Code: headerType,
    },
  });
};

/**
 * @method getACtiveRegionInfoFromCommonData
 * Get active region info from common data
 * @param data
 */
export const getACtiveRegionInfoFromCommonData = (
  commonData: CommonDataInterface,
  region: string,
  locale: string
) => {
  const supportedLocales =
    commonData?.data?.siteConfigs?.edges[0]?.node?.languages || [];

  // #. If locale from url not found in the siteconfig means invalid url entered
  // #. Redirect to 404 page
  if (supportedLocales.indexOf(locale) === -1) {
    return false;
  }

  const activeRegion =
    region || commonData?.data?.siteConfigs?.edges[0]?.node?.defaultCountry;

  const stores = commonData?.data?.stores?.edges || [];

  return stores?.find((store) => {
    return (
      String(store.node?.country?.code).toUpperCase() ===
      String(activeRegion).toUpperCase()
    );
  });
};

/**
 * @method getSiteConfig
 * Check1: returns locale info from the query slug
 * Check2: If query missed, then take it from the site config
 */
export const getSiteConfig = async (
  ip: any,
  locale: string,
  locales: Array<string> = []
) => {
  let result: any = [];
  let languageCode = "en";
  let regionCode = "";

  if (locale) {
    if (locale.indexOf("-") !== -1) {
      [languageCode, regionCode] = locale.split("-");
    } else {
      languageCode = locale;
    }
  } else {
    Sentry.captureException(
      `locale is not avialable in getSiteConfig. IP: ${ip}`
    );
  }

  try {
    const apolloClient = initializeApollo(languageCode);
    const resp = await apolloClient.query({
      query: SITE_CONFIG_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        store: regionCode ? regionCode.toUpperCase() : "",
      },
      context: {
        headers: {
          "Yg-Ip-Address": ip,
        },
      },
    });
    const { networkStatus } = resp;
    const data = resp?.data?.siteConfigs?.edges[0]?.node;

    if (!regionCode) {
      languageCode = data?.defaultLanguage;
      regionCode = data?.defaultCountry;
    }

    if (!data?.defaultCountry || !data?.defaultLanguage) {
      Sentry.captureMessage(
        `Default values are unavailable for defaultCountry or defaultLanguage ${JSON.stringify(
          data
        )}`
      );
    }

    result = [
      languageCode?.toLowerCase(),
      regionCode?.toLowerCase(),
      languageCode?.toLowerCase(),
      networkStatus,
      resp?.data,
    ];
    return result;
  } catch (error: any) {
    console.log(error);
    Sentry.captureException(`getSiteConfig fetch failed ${error.message}`);
    console.log(error);
    if (error?.graphQLErrors) {
      return [{ error: error?.graphQLErrors[0] }];
    }

    return [{ error }];
  }
};

/**
 * @method getThemeSettingsData
 * @description Get theme settings data for snowfall effect
 * @param param0
 * @returns
 */
export const getThemeSettingsData = (): any => {
  let {
    loading: themeSettingsDataLoading,
    error: themeSettingsDataError,
    data: themeSettingsData,
  } = useQuery<ThemeSettingsInterface>(THEME_SETTINGS_QUERY, {
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
    },
    context: {
      clientName: "webstore-with-cdn",
    },
  });

  return {
    themeSettingsDataLoading,
    themeSettingsDataError,
    themeSettingsData,
  };
};

/**
 * @method guestLoginEnabled
 * @description Check Guest Login enabled or not in the order admin panel
 * @returns
 */
export const guestLoginEnabled = async (
  platform: string,
  locale: string,
  regionInfo: any
) => {
  try {
    const store = "STAE";
    const apolloClient = initializeApollo(locale);
    let response = await apolloClient.query({
      query: GUEST_ENABLED,
      context: {
        headers: {
          "app-platform": platform ? platform.toLowerCase() : "web",
          "access-locale": store,
        },
        clientName: "guestLogin",
      },
    });
    console.log(response);
    const { isGuestEnabled } = response?.data?.cartConfig;
    return isGuestEnabled;
  } catch (error) {
    console.log("error ", error);
    return false;
  }
};

export const getBlacklistedCountries = async (locale: string) => {
  const apolloClient = initializeApollo(locale);
  let response = await apolloClient.query({
    query: BLACK_LISTED_CONTRIES_QUERY,
    context: {
      clientName: "ecom-users",
    },
  });
  return response?.data;
};

export default commonDataQuery;

export const getCaptchaConfig = async (locale: string) => {
  try {
    const apolloClient = initializeApollo(locale);
    let response = await apolloClient.query({
      query: CAPTCHA_CONFIG_QUERY,
      context: {
        clientName: "ecom-users",
      },
    });
    return response?.data;
  } catch (error) {
    Sentry.captureException(`Error in getCaptchaConfig: ${error}`);
    console.log("Error in getCaptchaConfig:", error);
  }
};

/**
 * @method WebConfigurables
 * @param {string} locale
 * @returns {data} object
 */
export const WebConfigurables = async (locale: string) => {
  try {
    const apolloClient = initializeApollo(locale || "en");
    const data = await apolloClient.query({
      query: PROFILE_SITE_CONFIG_QUERY,
      context: {
        clientName: "ecom-users",
      },
    });
    return data;
  } catch (error) {
    console.warn(error, "error");
    return error;
  }
};

/**
 * @method communicationConfigs
 * @param {string} locale
 * @param {string} countryCode
 * @param {string} flow
 * @param {string} channel
 * @returns {data} object
 */
export const communicationConfigs = async (
  locale: string,
  countryCode: string,
  flow: string,
  channel: string
): Promise<any> => {

  try {
    const apolloClient = initializeApollo(locale || "en");
    const data = apolloClient.query({
      query: COMMUNICATION_CONFIG_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        countryCode: countryCode,
        flow: flow,
        channel: channel,
      },
      context: {
        clientName: "ecom-users",
      },
    });
    return data;
  } catch (error) {
    console.warn(error, "error");
    return error;
  }
};

/**
 * @method fetchBlacklistedCountries
 * @description Get black listed countries
 * @returns
 */
export const fetchBlacklistedCountries = (skip = false): any => {
  let {
    loading: blacklistedCountriesDataLoading,
    error: blacklistedCountriesDataError,
    data: blacklistedCountriesData,
  } = useQuery<any>(BLACK_LISTED_CONTRIES_QUERY, {
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
    },
    context: {
      clientName: "ecom-users"
    },
    skip: skip
  });

  return {
    blacklistedCountriesDataLoading,
    blacklistedCountriesDataError,
    blacklistedCountriesData,
  };
};

export const fetchUserDetails = (token: string, storeCode: string) => {
  const {
    loading: userDetailsLoading,
    error: userDetailsError,
    data: userDetailsData,
    refetch: refetchUserDetails
  } = useQuery(USER_DETAILS_QUERY, {
    context: {
      clientName: "at-work",
      headers: {
        "access-locale": storeCode,
        "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
        "Authorization": `JWT ${token}`
      }
    },
    skip: true
  });

  return {
    userDetailsLoading,
    userDetailsError,
    userDetailsData,
    refetchUserDetails
  };
};

