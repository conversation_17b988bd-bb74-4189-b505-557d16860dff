import React, { Component } from 'react';
import * as Sentry from '@sentry/nextjs';
interface ErrorBoundaryState {
    hasError: boolean;
}

export class ErrorBoundary extends React.Component<any, ErrorBoundaryState> {
    constructor(props: any) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: any) {
        return { hasError: true };
    }

    componentDidCatch(error: any, info: any) {
        console.error({ error, info });
        Sentry.captureException({ error, info });
    }

    render() {
        if (this.state.hasError) {
            return this.props.fallback;
        }

        return this.props.children;
    }
}
