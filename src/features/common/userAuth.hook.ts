import { PLATFORM_TYPE } from "@constants/common";
import { initializeApollo } from "@graphql/apolloClient";
import { AUTH_TOKENS_WORK, USER_DETAILS_QUERY } from "./common.query";
import { authRedirectUrl, basePath } from "@constants/envVariables";

interface userAuthInterface {
  router?: any;
  regionUrl?: string;
  locale?: string;
}
// #. getting urls

/**
 * @method A hook to provide the reusable auth information
 * @returns {}
 */
const userAuth = (options?: userAuthInterface) => {
  /**
   * @method openSignInPage
   */
  const openSignInPage = (isSpecialBrand?: any) => {
    const router = options?.router;
    const locale = options?.locale;

    router.push(
      {
        pathname: `${authRedirectUrl}/${locale}/login/`,
        query: {
          rdir: `${window.location.origin.toString()}/${basePath}/${router.locale
            }/${router?.asPath}`,
          store: router?.locale,
          "special-brand": isSpecialBrand ? true : false
        }
      },
      undefined
    );
  };

  /**
   * @method getTokenInfo
   * @description generate accessToken and idTokens from referesh tokens
   * @param {any} token
   * @param {any} [locale]
   * @returns accessToken and idToken.
   */
  const getTokenInfo = async (token: any, storeCode: string, locale?: any) => {
    try {
      const apolloClient = initializeApollo(locale || "en");
      let tokenInfo = await apolloClient.query({
        query: AUTH_TOKENS_WORK,
        context: {
          clientName: "at-work",
          headers: {
            "access-locale": storeCode,
            "app-platform": PLATFORM_TYPE.WEB.toLowerCase()
          },
          credentials: "include"
        },
        variables: {
          input: {
            refreshToken: token
          }
        }
      });
      if (!!tokenInfo?.data?.authGetTokens.accessToken) {
        return {
          accessToken: tokenInfo?.data?.authGetTokens.accessToken,
          idToken: tokenInfo?.data?.authGetTokens.idToken
        };
      }
      return {
        accessToken: "",
        idToken: ""
      };
    } catch (error) {
      console.log("Error fetching token ", error);
      return {
        accessToken: "",
        idToken: ""
      };
    }
  };

  /**
   * @method fetchUserDetails
   * @description fetches user details
   * @param {any} accessToken
   * @param {any} [locale]
   * @returns user details object
   */
  const fetchUserDetails = async (
    token: string,
    storeCode: string,
    locale: any
  ) => {
    try {
      const apolloClient = initializeApollo(locale);
      let response = await apolloClient.query({
        query: USER_DETAILS_QUERY,
        context: {
          clientName: "at-work",
          headers: {
            "access-locale": storeCode,
            "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
            "Authorization": `JWT ${token}`
          }
        }
      });
      return response?.data.userDetails || {};
    } catch (error) {
      // console.warn(t("error"), SEVERITY.ERROR, t("error"));
    }
  };

  /**
   * @method getAuthInfo
   * @description fetches Tokens and user details
   * @param {any} refreshToken
   * @param {any} [locale]
   * @returns tokensInfo
   */
  const getAuthInfo = async (
    refreshToken: string,
    locale: any,
    storeCode: string
  ) => {
    try {
      const tokens = refreshToken
        ? await getTokenInfo(refreshToken, storeCode, locale)
        : null;

      const idToken = tokens?.idToken ?? "";
      const accessToken = tokens?.accessToken ?? "";
      let displayName = "",
        isUserSignedIn = false,
        userAttributes = {
          isFirstTimeUser: true
        } as any;
      if (accessToken != "") {
        isUserSignedIn = true;
        userAttributes = await fetchUserDetails(accessToken, storeCode, locale);
        displayName = userAttributes?.name;
      }
      return {
        idToken,
        accessToken,
        isUserSignedIn,
        displayName,
        userAttributes
      };
    } catch (error) {
      console.log("error is ", error);
      return {
        idToken: "",
        accessToken: "",
        isUserSignedIn: false,
        displayName: "",
        userAttributes: {
          isFirstTimeUser: true
        }
      };
    }
  };

  return {
    openSignInPage,
    getAuthInfo,
    getTokenInfo
  };
};

export default userAuth;
