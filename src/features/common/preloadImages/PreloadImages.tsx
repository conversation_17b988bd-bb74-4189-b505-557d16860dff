import { imageBaseUrl } from "@constants/envVariables";

const PreloadImages = () => {
  return (
    <>
      <link rel="preload" as="image" href={`${imageBaseUrl}/images/preload-image.jpeg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/company.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/designation.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/department.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/vat-icon.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/country.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/employees.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/industry.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/arrow-down-filled.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/phonenumber.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/information.svg`} />
      <link rel="preload" as="image" href={`${imageBaseUrl}/icons/back-btn.svg`} />
    </>
  );
};

export default PreloadImages;
