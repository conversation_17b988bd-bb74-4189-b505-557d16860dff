export function pushCleverTapEvent(eventName: string, eventData: {}) {
  try {
    window?.clevertap &&
      typeof window !== 'undefined' &&
      window?.clevertap?.event?.push(eventName, eventData);
  } catch (err) {
    console.log('error in CT pushEvent', err);
  }
}

export function updateCTProfile(lang: string, store: string) {
  try {
    window?.clevertap &&
      typeof window !== 'undefined' &&
      window?.clevertap?.profile?.push({
        Site: {
          Language: lang,
          Store: store,
        },
      });
  } catch (err) {
    console.log('error in CT updateCTProfile', err);
  }
}