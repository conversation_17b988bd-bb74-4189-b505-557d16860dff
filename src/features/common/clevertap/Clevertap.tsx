"use client";
import { useEffect, useState } from "react";
import clevertap from "clevertap-web-sdk";

function Clevertap({
  clevertapAccountId,
}: {
  clevertapAccountId: string;
}) {
  const [clevertapModule, setClevertapModule] = useState<
    typeof clevertap | null
  >(null);

  useEffect(() => {
    const clevertapInit = async () => {
      if (!clevertapModule) {
        console.log("clevertapModule is yet to be ready");
        const initializedModule = await initializeClevertap();
        setClevertapModule(initializedModule);
      }
    };

    setTimeout(() => {
      clevertapInit();
    }, 600);
  }, [clevertapModule]);

  const initializeClevertap = async () => {
    const clevertapModule = await import("clevertap-web-sdk");
    clevertapModule.default.init(clevertapAccountId);
    console.log("clevertapModule is loaded");
    clevertapModule.default.privacy.push({ optOut: false });
    clevertapModule.default.privacy.push({ useIP: true });
    clevertapModule.default.setLogLevel(0);
    return clevertapModule.default;
  };

  return null;
}

export default Clevertap;
