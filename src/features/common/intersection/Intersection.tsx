import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import ProfileImage from "../profileImage/ProfileImage";
import styles from "./Intersection.module.scss";

const InfoSection: React.FC<{
  onTypingComplete: () => void;
  text: string;
  componentId?: string;
  chatbotImage: string;
}> = ({ onTypingComplete, text, componentId, chatbotImage }) => {
  const [displayedText, setDisplayedText] = useState("");

  useEffect(() => {
    if (!text) return;

    let i = 0;

    const smoothTyping = setInterval(() => {
      i++;
      setDisplayedText(text.slice(0, i));

      if (i === text.length) {
        clearInterval(smoothTyping);
        onTypingComplete();
      }
    }, 20);

    return () => clearInterval(smoothTyping);
  }, []);

  return (
    <div className={`${styles["intersection-container"]} mb-24`}>
      <ProfileImage chatbotImage={chatbotImage} />
      <div className={styles["intersection-container__inner"]}>
        {displayedText.split(" ").map((word, index) => (
          <motion.span
            key={index}
            initial={{ opacity: 0, y: 3 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            className={styles["intersection-container__inner-span"]}
          >
            {word}
          </motion.span>
        ))}
      </div>
    </div>
  );
};

export default InfoSection;
