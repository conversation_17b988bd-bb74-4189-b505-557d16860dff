@import "@styles/variables";
@import "@styles/mixins";

.intersection-container {
  font-family: $default-font-family;
  display: flex;
  color: #0e0f0c;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
  letter-spacing: -0.14px;

  @include rtl-styles {
    font-family: $arabic-font-family;
  }

  &__inner {
    padding: 12px 12px 12px 16px;
    border-radius: 16px 16px 16px 0;
    background: #f7f7f7;

    &-span {
      display: inline-block;
      margin-inline-end: 4px;
    }
  }
}
