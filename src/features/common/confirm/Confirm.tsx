import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { useTranslation } from "next-i18next";
import styles from "./Confirm.module.scss";
import Button from "../button/Button";

interface ConfirmationDialogProps {
  icon?: string;
  title?: string;
  message: string;
  confirmText?: string;
  open: boolean;
  dialogProps?: any;
  className?: string;
  onClose: (isConfirmed: boolean) => void;
  singleBtn?: boolean;
}

/**
 * @method Confirm
 * @description A common component usding as a confirmation dialog
 * @param param0
 * @returns
 */
const Confirm = ({
  icon,
  title,
  message,
  confirmText,
  open,
  dialogProps = {},
  className,
  onClose,
  singleBtn,
}: ConfirmationDialogProps) => {
  // #. Get translations
  const { t } = useTranslation("common");

  /**
   * @method onConfirm
   */
  const onConfirm = (isConfirm: boolean) => {
    onClose && onClose(isConfirm);
  };

  return (
    <Dialog
      open={open}
      {...dialogProps}
      className={`confirm-dialog ${styles["confirm-dialog"]} ${className}`}
    >
      <div
        className={`confirm-dialog-container ${styles["confirm-dialog-container"]}`}
      >
        {icon && (
          <img
            src={icon}
            className={`confirm-dialog-icon ${styles["confirm-dialog-icon"]}`}
          />
        )}
        <div
          className={`confirm-dialog-section ${styles["confirm-dialog-section"]}`}
        >
          {title && (
            <DialogTitle
              className={`confirm-dialog-title ${styles["confirm-dialog-title"]}`}
            >
              {title}
            </DialogTitle>
          )}
          <DialogContent
            className={`confirm-dialog-content ${styles["confirm-dialog-content"]}`}
          >
            {message}
          </DialogContent>
        </div>
        <DialogActions
          className={`confirm-dialog-buttons ${styles["confirm-dialog-buttons"]}`}
        >
          {!singleBtn && (
            <Button
              action={() => onConfirm(false)}
              theme="dark-shadow"
              className={`confirm-dialog-button-cancel ${singleBtn ? `${styles["confirm-dialog-button-confirm-singleBtn"]}` : `${styles["confirm-dialog-button-cancel"]}`}`}
            >
              {t("cancel")}
            </Button>
          )}
          <Button
            action={() => onConfirm(true)}
            theme="primary"
            className={`confirm-dialog-button-confirm ${styles["confirm-dialog-button-confirm"]}`}
          >
            {confirmText ? confirmText : t("confirm")}
          </Button>
        </DialogActions>
      </div>
    </Dialog>
  );
};

export default Confirm;
