import { Skeleton } from "@mui/material";
import React from "react";
import styes from "./CardComponentSkelton.module.scss";

const CardComponentSkelton = () => {
  const data = Array(6).fill(null);

  return (
    <div className={styes["card-component-skeleton"]}>
      {data.map((_, index) => (
        <div key={index}>
          <Skeleton variant="rectangular" width={"100%"} height={107} />
          <Skeleton
            variant="rectangular"
            width={"60%"}
            height={20}
            className={styes["skeleton-text"]}
          />
        </div>
      ))}
    </div>
  );
};

export default CardComponentSkelton;
