import React from "react";
import styles from "./ProfileImage.module.scss";

export default function ProfileImage({
  chatbotImage,
}: {
  chatbotImage: string;
}) {
  return (
    <div className={styles["profile-blk"]}>
      <style jsx>{`
        .profile-blk__image {
          background: url(${chatbotImage}) no-repeat top center / cover;
          border-radius: 50%;
        }
      `}</style>
      <div
        className={`profile-blk__image ${styles["profile-blk__image"]}`}
      ></div>
    </div>
  );
}
