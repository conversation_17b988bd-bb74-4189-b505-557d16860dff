"use client";
import {
  createContext,
  use,
  useCallback,
  useContext,
  useReducer,
} from "react";
import {
  BRAND_FORM_STATE,
  BRAND_STEPPER,
  BRAND_STEPPER_STATE,
  CARD_DELIVERY_TYPE,
  CARD_PURCHASE_TYPE,
  DATE_TYPE,
  DEFAULT_PHONE_CODE,
  FONT_FAMILIES,
  GIFT_OPEN_SECTION,
  GREETING_COVER_TYPE,
  PERSONAL_TITLE,
  TIMZONE_VALUES,
  WORK_STEPPER,
  WORK_STEPPER_STATE,
} from "@constants/common";
import {
  BrandPurchaseContext,
  BrandSessionSave,
  BrandStepper,
  BrandWorkStepper,
  CustomCard,
  RecipientDetailsInterface,
  RecipientPdf,
} from "@interfaces/common.inteface";
import { Brand } from "@interfaces/common.inteface";

// #. Create the context actions and exported
export enum AppContextAction {
  CARD = "card",
  SESSION_SAVE = "session-save",
  STEPPER = "stepper",
  BRAND = "brand",
  BLACKLISTED_COUNTRIES = "blacklisted-countries",
  WORK_STEPPER = "work-stepper",
  RECIPIENT_DETAILS = "recipient-details",
  GIFT_TYPE = "gift-type",
  BRAND_LIST = "brand-list",
  BRAND_DENOMINATION = "brand-denomination",
  PDF_PREVIEW = "pdf-preview",
  EDIT_RECIPIENT = "edit-recipient",
  LOGIN = "login",
  RECIPIENT_PDF = "recipient-pdf",
  CUSTOM_GIFT_CARD = "custom-gift-card",
  PARTIAL_LANGUAGE = "partial-language",
  CART_INFO = "cart-info",
  RESET_DATA = "reset-data",
  SET_CHATBOT = "set-chatbot",
  CUSTOME_GREETINGS = "custome-greetings",
  RESET_CARD_DATA = "reset-card-data",
  PERSONALISATION = "personalisation",
  RESET_WITHOUT_CART_INFO = "reset-without-cart-info",
  RESET_PERSONALISATION = "reset-personalisation",
  ALLOWED_FILE_TYPES = "allowed-file-types",
  RECEIPIENT_VALIDATIONS = "receipient-validation",
  BRAND_INFO = "brand-info",
  RESET_RECIPIENTS = "reset-recipients",
  RESET_CUSTOM_GIFT_CARD = "reset-custom-gift-card",
  RESET_PDF_PREVIEW = "reset-pdf-preview",
  RESET_WORK_STEPPER = "reset-work-stepper",
  GIFT_OPEN_EXPERIENCE = "gift-open-experience",
  WALLET_MODAL = "wallet-modal",
  FAQ_DETAILS = "faq-details",
  CARD_TYPE = "card-type",
}

// #. Decalre the types
type Action = { type: AppContextAction; payload?: any };
type Dispatch = (action: Action) => void;
type BrandProviderProps = {
  children: React.ReactNode;
  cameraTagAppId?: string;
  imglyLicense?: string;
  globalAppData?: any;
};
type State = {
  card: BrandPurchaseContext;
  sessionSave: BrandSessionSave;
  stepper: BrandStepper;
  activeRegion: any;
  brand: Brand | any;
  blacklistedCountries: any;
  workStepper: BrandWorkStepper | any;
  recipientDetails: RecipientDetailsInterface[];
  localeCode: string;
  giftTypeSelection: {
    image: string;
    value: string;
    name: string;
    slug: string;
  };
  brandList: {
    slug: string;
    value: string;
    name: string;
    data: any;
    skinData: any;
  };
  brandDenomination: { name: string; denominations: string[] };
  editRecipient: {
    index: number;
    isEdit: boolean;
  };
  login: {
    email: string;
    authSessionToken: string;
  };
  pdfPreview: any;
  recipientPdf: RecipientPdf[];
  customGiftCard: CustomCard;
  partialLanguage: string;
  personalization: {
    name: string;
    isSkipped: boolean;
  };
  workCart: {
    cartId: string;
    cartCount: number;
    currency: any;
    extraCharge: any;
    total: any;
    baseValue: any;
    pendingPaymentData: any;
    paymentMethod: string;
    paymentProgress: boolean;
    walletCurrency: string | number;
    totalBaseInWalletCurrency: number;
  };
  allowedFileType: [];
  receipientValidation: any;
  chatbot: {
    image: string;
    name: number;
    activeAgents: [];
  };
  customeGreetings: any;
  brandInfo: {
    brandImage: string;
    brandSlug: string;
  };
  giftOpen: {
    activeSection: string;
  };
  walletModal: {
    showModal: boolean;
    modalType: string;
    requestId?: string;
  };
  faqDetails: any;
  atWorkSiteConfig: any;
  selectedCardType: string;
  region: string;
  commonData: any;
  refreshToken: string;
  idToken: string;
  accessToken: string;
  isUserSignedIn: boolean;
  displayName: string;
  userAttributes: any;
};
// #. Create the brand context
const AppContext = createContext<
  | {
      state: State;
      dispatch: Dispatch;
      APP_KEYS: { CAMERA_TAG?: string; IMGLY_LKEY?: string };
    }
  | undefined
>(undefined);

// #. Set the brand context name
AppContext.displayName = "AppContext";

// #. Create a reducer method to update the state
function BrandReducer(state: State, action: Action) {
  switch (action.type) {
    case AppContextAction.CARD: {
      return { ...state, card: { ...state.card, ...action.payload } };
    }

    case AppContextAction.STEPPER: {
      return {
        ...state,
        stepper: { ...state.stepper, ...action.payload },
      };
    }

    case AppContextAction.WORK_STEPPER: {
      return {
        ...state,
        workStepper: { ...state.workStepper, ...action.payload },
      };
    }

    case AppContextAction.PDF_PREVIEW: {
      return {
        ...state,
        pdfPreview: { ...state.pdfPreview, ...action.payload },
      };
    }

    case AppContextAction.SESSION_SAVE: {
      return {
        ...state,
        sessionSave: { ...action.payload },
      };
    }

    case AppContextAction.BRAND: {
      return {
        ...state,
        brand: { ...action.payload },
      };
    }

    case AppContextAction.BLACKLISTED_COUNTRIES: {
      return {
        ...state,
        blacklistedCountries: [...action.payload],
      };
    }

    case AppContextAction.RECIPIENT_DETAILS: {
      return {
        ...state,
        recipientDetails: action.payload,
      };
    }

    case AppContextAction.EDIT_RECIPIENT: {
      return {
        ...state,
        editRecipient: action.payload,
      };
    }

    case AppContextAction.GIFT_TYPE: {
      return {
        ...state,
        giftTypeSelection: action.payload,
      };
    }

    case AppContextAction.BRAND_LIST: {
      return {
        ...state,
        brandList: action.payload,
      };
    }

    case AppContextAction.BRAND_DENOMINATION: {
      return {
        ...state,
        brandDenomination: action.payload,
      };
    }

    case AppContextAction.LOGIN: {
      return {
        ...state,
        login: action.payload,
      };
    }
    case AppContextAction.RECIPIENT_PDF: {
      return {
        ...state,
        recipientPdf: action.payload,
      };
    }
    case AppContextAction.CUSTOM_GIFT_CARD: {
      return {
        ...state,
        customGiftCard: action.payload,
      };
    }
    case AppContextAction.PERSONALISATION: {
      return {
        ...state,
        personalization: { ...state.personalization, ...action.payload },
      };
    }
    case AppContextAction.PARTIAL_LANGUAGE: {
      return {
        ...state,
        partialLanguage: action.payload,
      };
    }
    case AppContextAction.CART_INFO: {
      return {
        ...state,
        workCart: action.payload,
      };
    }
    case AppContextAction.ALLOWED_FILE_TYPES: {
      return {
        ...state,
        allowedFileType: action.payload,
      };
    }
    case AppContextAction.RECEIPIENT_VALIDATIONS: {
      return {
        ...state,
        receipientValidation: action.payload,
      };
    }
    case AppContextAction.CUSTOME_GREETINGS: {
      return {
        ...state,
        customeGreetings: action.payload,
      };
    }
    case AppContextAction.SET_CHATBOT: {
      return {
        ...state,
        chatbot: action.payload,
      };
    }
    case AppContextAction.RESET_CARD_DATA: {
      return {
        ...state,
        card: {
          ...cardInitialState,
          purchaseType: state.card.purchaseType,
          personalisation: state.card.personalisation,
          greetingCover: state.card.greetingCover,
          giftMessage: state.card.giftMessage,
        },
      };
    }
    case AppContextAction.RESET_DATA: {
      return {
        ...state,
        ...initialValue,
      };
    }
    case AppContextAction.RESET_WITHOUT_CART_INFO: {
      return {
        ...state,
        ...initialValue,
        workCart: state.workCart,
      };
    }
    case AppContextAction.BRAND_INFO: {
      return {
        ...state,
        brandInfo: action.payload,
      };
    }
    case AppContextAction.RESET_PERSONALISATION: {
      return {
        ...state,
        pdfPreview: initialValue.pdfPreview,
        recipientDetails: initialValue.recipientDetails,
        recipientPdf: initialValue.recipientPdf,
        customeGreetings: initialValue.customeGreetings,
        card: {
          ...state.card,
          personalisation: cardInitialState.personalisation,
          giftMessage: cardInitialState.giftMessage,
          greetingCover: cardInitialState.greetingCover,
        },
        workCart: state.workCart,
        workStepper: initialValue.workStepper,
      };
    }
    case AppContextAction.RESET_RECIPIENTS: {
      return {
        ...state,
        recipientDetails: initialValue.recipientDetails,
        recipientPdf: initialValue.recipientPdf,
      };
    }
    case AppContextAction.RESET_CUSTOM_GIFT_CARD: {
      return {
        ...state,
        customGiftCard: initialValue.customGiftCard,
      };
    }
    case AppContextAction.RESET_PDF_PREVIEW: {
      return {
        ...state,
        pdfPreview: initialValue.pdfPreview,
      };
    }
    case AppContextAction.RESET_WORK_STEPPER: {
      return {
        ...state,
        workStepper: initialValue.workStepper,
      };
    }
    case AppContextAction.GIFT_OPEN_EXPERIENCE: {
      return {
        ...state,
        giftOpen: action.payload,
      };
    }
    case AppContextAction.WALLET_MODAL: {
      return {
        ...state,
        walletModal: action.payload,
      };
    }
    case AppContextAction.FAQ_DETAILS: {
      return {
        ...state,
        faqDetails: action.payload,
      };
    }
    case AppContextAction.CARD_TYPE: {
      return {
        ...state,
        ...action.payload,
      };
    }
    default: {
      return state;
    }
  }
}

const cardInitialState = {
  referenceId: "",
  brandCode: "",
  brandName: "",
  brandImage: "",
  brandSlug: "",
  cardValue: 0,
  isInvalidCardValue: false,
  currencyCode: "",
  purchaseType: CARD_PURCHASE_TYPE.EMAIL_SMS,
  printablePDFSelected: false,
  quantity: 1,
  greetingCover: {
    occasion: "",
    coverType: GREETING_COVER_TYPE.STANDARD,
    language: "",
    filePath: "",
    referenceCode: "",
    staticGifPath: "",
    isSkipGreetings: false,
  },
  personalisation: {
    video: {},
    photo: {
      blob: "",
      file: "",
      fileName: "",
      prevFile: "",
    },
    gif: {
      url: "",
      id: "",
    },
  },
  personalisationImgVideoToDelete: "",
  personalisationVideoToDelete: "",
  giftMessage: {
    message: "",
    backgroundColor: "#EB6B6B",
    fontFamily: 'var(--font-poppins)',
    fontSize: 18,
    isDirty: false,
  },
  sendToPerson: {
    title: PERSONAL_TITLE.MISTER,
    name: "",
    email: "",
    phoneCode: DEFAULT_PHONE_CODE,
    phoneNumber: "",
    isValidPhoneNumber: false,
    isValidEmail: false,
  },
  delivery: {
    type: CARD_DELIVERY_TYPE.INSTANT,
    dateType: DATE_TYPE.TODAY,
    date: new Date(),
    time: new Date(),
    timezone: TIMZONE_VALUES.GMT,
  },
  availableStock: 0,
  outOfStock: { yes: false, amount: 0 },
  isDirty: false,
  formState: BRAND_FORM_STATE.CREATE,
  storiesCount: 0,
  nonModulusBrandValues: [],
  faqDetails: [],
  selectedCardType: "",
  localeCode: "",
  region: "",
};

// #. Initial value for the context
const initialValue = {
  card: cardInitialState,
  sessionSave: {
    data: undefined,
    clickMode: 0,
  },
  stepper: {
    activeStep: BRAND_STEPPER.CARD_VALUE,
    openStep: BRAND_STEPPER.GREETING,
    [BRAND_STEPPER.CARD_VALUE]: {
      stepState: BRAND_STEPPER_STATE.NOT_OPENED,
    },
    [BRAND_STEPPER.GREETING]: {
      stepState: BRAND_STEPPER_STATE.NOT_OPENED,
    },
    [BRAND_STEPPER.PERSONALIZE]: {
      stepState: BRAND_STEPPER_STATE.NOT_OPENED,
    },
    [BRAND_STEPPER.USER_MESSAGE]: {
      stepState: BRAND_STEPPER_STATE.NOT_OPENED,
    },
    [BRAND_STEPPER.DELIVERY]: {
      stepState: BRAND_STEPPER_STATE.NOT_OPENED,
    },
  },
  workStepper: {
    activeStep: WORK_STEPPER.GREETING,
    openStep: WORK_STEPPER.PERSONALIZE,
    [WORK_STEPPER.GREETING]: {
      stepState: WORK_STEPPER_STATE.ACTIVE,
    },
    [WORK_STEPPER.PERSONALIZE]: {
      stepState: WORK_STEPPER_STATE.NOT_OPENED,
    },
    [WORK_STEPPER.USER_MESSAGE]: {
      stepState: WORK_STEPPER_STATE.NOT_OPENED,
    },
  },
  pdfPreview: {
    standardPdf: false,
    pdfConfigs: {
      logo: true,
      message: true,
    },
    logo: {
      file: "",
      urlType: "",
      name: "",
      previewUrl: "",
    },
    greetings: {
      file: "",
      urlType: "",
      name: "",
      previewUrl: "",
    },
    message: {
      text: "",
    },
    messageStyle: {
      color: "#000",
      fontFamily: "Roboto",
      textAlign: "center",
      fontStyle: "",
      fontWeight: "",
      textDecoration: "",
    },
    name: "Personalization",
  },
  brand: {},
  categoryName: "",
  redemptionType: "",
  occasionName: "",
  occasionCode: "",
  brandSkinUrl: "",
  blacklistedCountries: [],
  recipientDetails: [],
  giftTypeSelection: {
    value: "",
    name: "",
    slug: "",
    image: "",
  },
  brandList: { name: "", data: null, skinData: null, value: null },
  editRecipient: {
    index: 0,
    isEdit: false,
  },
  brandDenomination: { name: "", denominations: [] },
  personalization: { name: "personalization", isSkipped: false },
  recipient: { name: "recipientList" },
  orderSummary: { name: "orderSummary" },
  login: {
    email: "",
    authSessionToken: "",
  },
  recipientPdf: [],
  pdfRecipients: { name: "recipientList" },
  customGiftCard: {
    name: "Custom Gift Card",
    logo: null,
    background: null,
    bgColor: "",
    occasion: "",
    logoPreview: "",
    bgPreview: "",
    cardImage: "",
    referenceId: "",
  },
  partialLanguage: "en",
  workCart: {
    cartId: "",
    cartCount: 0,
    currency: "",
    extraCharge: "",
    total: "",
    baseValue: "",
    pendingPaymentData: "",
    paymentMethod: "",
    paymentProgress: false,
  },
  allowedFileType: [],
  receipientValidation: {},
  chatbot: {
    name: "",
    image: "",
    activeAgents: [],
  },
  customeGreetings: {
    file: "",
    urlType: "",
    name: "",
    previewUrl: "",
  },
  brandInfo: {
    brandImage: "",
    brandSlug: "",
  },
  giftOpen: {
    activeSection: GIFT_OPEN_SECTION.OPEN,
  },
  walletModal: {
    showModal: false,
    modalType: "",
  },
  atWorkSiteConfig: null,
  selectedCardType: "",
  localeCode: "",
  region: "",
  commonData: null,
  refreshToken: "",
  idToken: "",
  accessToken: "",
  isUserSignedIn: false,
  displayName: "",
  userAttributes: {},
};

// #. Create brand context provider
const AppContextProvider = (props: BrandProviderProps) => {
  const cbReducer = useCallback(BrandReducer, []);

  // #. Set the passed keys as constants
  const APP_KEYS = {
    CAMERA_TAG: props.cameraTagAppId,
    IMGLY_LKEY: props.imglyLicense,
  };

  //#. catalog title state
  //@ts-ignore
  const [state, dispatch] = useReducer(cbReducer, {
    ...initialValue,
    ...props.globalAppData,
  });

  const value: any = { state, dispatch, APP_KEYS };

  return (
    <AppContext.Provider value={value}>
      {props.children}
    </AppContext.Provider>
  );
};

// #. Create the context as a hook
function useApp() {
  const context = useContext(AppContext);

  if (context === undefined || context === null) {
    throw new Error("useApp must be used within an AppContextProvider");
  }

  return context;
}

//#. Export all the mandatory items
export { AppContextProvider, AppContext };
export default useApp;
