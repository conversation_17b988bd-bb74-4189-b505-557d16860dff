import React from "react";
import styles from "./Chatbot.module.scss";
import useApp from "../common.context";
import Intercom from "@intercom/messenger-js-sdk";

const Chatbot = () => {
  const {
    state: { atWorkSiteConfig, activeRegion },
  } = useApp();
  const chatbotImage =
    atWorkSiteConfig?.chatbotDetails?.chatbotImage || "";
  const chatbotName = atWorkSiteConfig?.chatbotDetails?.chatbotName || "";
  const CHAT_WIDGET_APP_ID =
    process.env.NEXT_PUBLIC_INTERCOM_CHAT_WIDGET_APP_ID || "uuvj0nnp";

  Intercom({
    app_id: CHAT_WIDGET_APP_ID,
    hide_default_launcher: true,
    language_override: activeRegion?.code,
  });

  const openChat = () => {
    if (window.Intercom) {
      window.Intercom("show");
    } else {
      console.warn("Intercom is not loaded.");
    }
  };
  return (
    <div className={styles["chat-bot"]} onClick={openChat}>
      <style jsx>{`
        .chatbot-image {
          background: url(${chatbotImage}) no-repeat top center / cover;
          border-radius: 50%;
          width: 100%;
          height: 100%;
        }
      `}</style>
      <div className={`chatbot-image`} />
      <p className={styles["chat-bot__name"]}>{chatbotName}</p>
    </div>
  );
};

export default Chatbot;
