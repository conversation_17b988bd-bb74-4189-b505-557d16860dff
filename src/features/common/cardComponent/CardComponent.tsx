import React from "react";
import styles from "./CardComponent.module.scss";
import Image from "next/image";
import { imageBaseUrl } from "@constants/envVariables";
import CardComponentSkelton from "../contentLoader/cardComponentSkelton/CardComponentSkelton";

interface CardComponentInterface {
  cardImage: string;
  cardName: string;
  onClick?: () => void;
}

const CardComponent = ({
  cardImage,
  cardName,
  onClick,
}: CardComponentInterface) => {
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;

  return (
    <div className={styles["card-component"]} onClick={onClick}>
      <div  className={styles["card-component__image-wrap"]}>
        <Image
          blurDataURL={preloadImage}
          placeholder="blur"
          src={cardImage || preloadImage}
          alt={"Card Image"}
          priority
          width={0}
          height={0}
          sizes="auto"
          className={styles["card-component__image"]}
        />
      </div>
      <div className={styles["card-component__details"]}>
        <h3>{cardName}</h3>
      </div>
    </div>
  );
};

export default CardComponent;
