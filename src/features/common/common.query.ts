import { gql } from "@apollo/client";

export const COMMON_QUERY = gql`
  query LogoQuery($platformType_Code: String!) {
    headers(platformType_Code: $platformType_Code) {
      edges {
        node {
          logo
        }
      }
    }
  }
`;

export const STORES_QUERY = gql`
  query StoresQuery {
    stores {
      edges {
        node {
          code
          name
          timezone
          country {
            code
            name
            flagImage
            codeThreeLetter
            circularStoreLogo
            defaultLanguage {
              code
            }
          }
          languages {
            edges {
              node {
                name
                code
              }
            }
          }
        }
      }
    }
  }
`;

export const LOGO_QUERY = gql`
  query ($platformType_Code: String!) {
    headers(platformType_Code: $platformType_Code) {
      edges {
        node {
          logo
        }
      }
    }
  }
`;

export const HEADER_LOGO_QUERY = gql`
  query ($platformType_Code: String!, $headerType_Code: String!) {
    headers(
      platformType_Code: $platformType_Code
      headerType_Code: $headerType_Code
    ) {
      edges {
        node {
          logo
        }
      }
    }
  }
`;

export const SECURE_LICENCE_QUERY = gql`
  {
    secureConfigs {
      imglyLicense
      cameraTagAppId
    }
  }
`;

export const PAYMENT_PARTNERS_QUERY = gql`
  query ($platformType_Code: String!, $country_Code: String!) {
    footer(platformType_Code: $platformType_Code, country_Code: $country_Code) {
      paymentPartners {
        edges {
          node {
            code
            logo
          }
        }
      }
    }
  }
`;

export const SITE_CONFIG_QUERY = gql`
  query SiteConfigQuery($platformType_Code: String!, $store: String!) {
    siteConfigs(platformType_Code: $platformType_Code, store: $store) {
      edges {
        node {
          defaultCountry
          defaultLanguage
          defaultStoreCode
          languages
          signatureTitle
          signatureSubtitle
          name
          clevertapAccountId
          recaptchaSiteKey
          chatKey
          chatEnabled
          captchaConfig {
            hasCaptchaEnabled
            actionName
          }
          homepageSiteMeta {
            id
            title
            urlPattern
            description
            keywords
          }
        }
      }
    }
  }
`;

export const GUEST_USER_LOGOUT_MUTATION = gql`
  mutation {
    guestLogout {
      logout {
        message
      }
    }
  }
`;

export const THEME_SETTINGS_QUERY = gql`
  query themeSettings {
    themeConfig {
      name
      audio
      audioEnabled
      snowFlakeCount
      snowEnabled
      speedMin
      speedMax
      windMax
      windMin
      radiusMax
      radiusMin
      useImage
      image
      rotationSpeedMin
      rotationSpeedMax
      color
    }
  }
`;

export const GUEST_ENABLED = gql`
  query {
    cartConfig {
      isGuestEnabled
    }
  }
`;

export const BLACK_LISTED_CONTRIES_QUERY = gql`
  query BlacklistedCountriesQuery {
    blacklistedCountries
  }
`;

export const CAPTCHA_CONFIG_QUERY = gql`
  query {
    captchaConfig(actionName: "update_phone_number") {
      actionName
      captchaVersion
      hasCaptchaEnabled
    }
  }
`;

export const COMMUNICATION_CONFIG_QUERY = gql`
  query communicationConfigsQuery(
    $countryCode: String!
    $flow: MessageType!
    $channel: ChannelType!
  ) {
    communicationConfigs(
      countryCode: $countryCode
      flow: $flow
      channel: $channel
    ) {
      resendDeliveryEnabled
    }
  }
`;

export const AUTH_TOKENS_WORK = gql`
  query AuthGetTokens($input: AuthRefreshTokenInput) {
    authGetTokens(input: $input) {
      accessToken
      refreshToken
    }
  }
`;

export const USER_DETAILS_QUERY = gql`
  query UserDetails {
    userDetails {
      email
      emailVerified
      organization
      designation
      department
      vatNumber
      countryCode
      languageCode
      picture
      isEnabled
      name
      industry
      organizationTier
      isFirstTimeUser
      hasStoreOrder
      mdc
      phoneNumber
      vatCertificateUrl
      companyAddress
      lastOrder{
        p3lnRefId
        isCustom
        brandSlug
        referenceId
      }
      lastOrder{
        brandSkinImage
        brandSlug
        isCustom
        referenceId
        skinSelectionRequired
        p3lnRefId
        brandSkinIdentifier
        isGeneric
      }
      wallet{
        currency
        minAmount
        balance
        status
    }
    }
  }
`;

export const PROFILE_SITE_CONFIG_QUERY = gql`
  query SiteConfigQuery {
    siteConfig {
      updatePhoneNumberEnabled
    }
  }
`;

export const WORK_SITE_CONFIG_QUERY = gql`
  query SiteConfigurations {
    siteConfigurations {
      atWorkEnabled
      chatBotEnabled
      maxNumberOfReceivers
      maxNumberOfPdfGifts
      maxSizeOfGiftsBucket
      maxLogoFileSizeMb
      logoMinHeight
      logoMaxHeight
      logoMinWidth
      logoMaxWidth
      maxImageFileSizeMb
      imageMinHeight
      imageMaxHeight
      imageMaxWidth
      imageMinWidth
      customHcThresholdPerUser
      atWorkProUrl
      profilePictureSize
      profilePictureWidth
      profilePictureHeight
      chatbotDetails {
        chatbotName
        chatbotImage
        activeAgents
      }
      isChatBotActive
      allowedLogoFileTypes {
        extension
      }
      allowedImageFileTypes {
        extension
      }
      employeesRanges {
        minEmployees
        maxEmployees
        slug
      }
      industries {
        name
        slug
      }
      landingPageSiteMeta {
        title
        urlPattern
        description
        keywords
        noIndex
      }
    }
  }
`;

export const WORK_OCASSION_QUERY = gql`
  query OccasionsGeneric($gif: Boolean!, $productCode: String!) {
    occasionsGeneric(gif: $gif, productCode: $productCode) {
      edges {
        cursor
        node {
          id
          createdOn
          modifiedOn
          name
          nameEn
          nameAr
          code
          referenceCode
          description
          message
          cardMessage
          cardMessageEn
          cardMessageAr
          orderNumber
          isCustom
          isActive
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const WORK_GIF_ILLUSTRATION_QUERY = gql`
  query GifIllustrationsGeneric($code: String!, $productCode: String!) {
    gifIllustrationsGeneric(occasionCode: $code, productCode: $productCode) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        cursor
        node {
          id
          createdOn
          modifiedOn
          gifFile
          gifImage
          referenceCode
          isArabic
          isActive
          occasion {
            id
            createdOn
            modifiedOn
            name
            nameEn
            nameAr
            code
            referenceCode
            description
            message
            cardMessage
            cardMessageEn
            cardMessageAr
            orderNumber
            isCustom
            isActive
          }
        }
      }
    }
  }
`;

export const WORK_ILLUSTRATION_QUERY = gql`
  query IllustrationsGeneric($code: String!, $productCode: String!) {
    illustrationsGeneric(occasionCode: $code, productCode: $productCode) {
      edges {
        cursor
        node {
          id
          createdOn
          modifiedOn
          cardImage
          iconImage
          cardMessageImage
          backgroundColor
          isArabic
          isActive
          referenceCode
          illustrationCount
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const PRE_SIGNED_URL_QUERY = gql`
  query PreSignedUrl(
    $fileType: PreSignedUrlSupportedFileTypeEnum!
    $urlType: PreSignedUrlFolderPathEnum!
  ) {
    preSignedUrl(fileType: $fileType, urlType: $urlType) {
      fileKey
      preSignedUrl
      urlType
      fullUrl
    }
  }
`;

export const OCCASION_ANALYTICS_QUERY = gql`
  query OccasionAnalytics($senderEmail: String!) {
    occasionAnalytics(senderEmail: $senderEmail) {
      occasion
      occasionName
      count
    }
  }
`;

export const DELIVERY_METHOD_ANALYTICS = gql`
  query DeliveryMethodAnalytics($senderEmail: String!) {
    deliveryMethodAnalytics(senderEmail: $senderEmail) {
      deliveryMethod
      percentage
    }
  }
`;
