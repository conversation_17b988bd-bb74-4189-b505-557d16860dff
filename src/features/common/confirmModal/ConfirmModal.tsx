import React, { useState } from "react";
import { SwipeableDrawer } from "@mui/material";
import styles from "./ConfirmModal.module.scss";
import Button from "../button/Button";
import { useTranslation } from "next-i18next";

const ConfirmModal = ({
  confirmModal,
  handleClose,
  handleSubmit,
}: {
  confirmModal: boolean;
  handleClose: () => void;
  handleSubmit: () => void;
}) => {
  const { t } = useTranslation("common");
  return (
    <div className={`confirm-drawer  ${styles["confirm-modal"]}`}>
      <SwipeableDrawer
        className={"confirm-drawer"}
        anchor={"bottom"}
        open={confirmModal}
        onClose={handleClose}
        onOpen={() => {}}
      >
        <div className={styles["confirm-modal__wrapper"]}>
          <div className={styles["confirm-modal__line"]}></div>
          <h5>Discard Changes</h5>
          <p>You will lose the edits you made. Do you want to discard?</p>

          <div className={styles["confirm-modal__buttons"]}>
            <Button theme="dark-shadow" action={handleClose}>
              {t("no")}
            </Button>
            <Button theme="at-work-primary" action={handleSubmit}>
              {t("yes")}
            </Button>
          </div>
        </div>
      </SwipeableDrawer>
    </div>
  );
};

export default ConfirmModal;
