@import "@styles/variables";
@import "@styles/mixins";

.confirm-modal {
  &__wrapper {
    padding: 20px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;

    h5 {
      font-family: $bricolage-font-family;
      font-optical-sizing: none;
      font-size: 24px;
      font-style: normal;
      font-weight: 800;
      line-height: 24px;
      margin-top: 24px;
    }
    p {
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
      letter-spacing: -0.16px;
      margin-top: 16px;
      margin-bottom: 16px;
    }
  }
  &__line {
    margin: 0 auto;
    width: 48px;
    height: 4px;
    background-color: #d9d9d9;
    border-radius: 12px;
  }

  &__buttons {
    margin-top: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
  }
}
