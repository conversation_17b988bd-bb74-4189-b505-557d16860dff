"use client";
import React, { useEffect, useState } from "react";
import CookieDisabled from "../cookieDisabled/CookieDisabled";
import cookieIsEnabled from "@utils/cookieEnabled";
import Loader from "../loader/Loader";

/**
 * @method CookieCheck
 * @description This is a wrapper element used to check if browser storage permissions are enabled
 * @param children
 */
const CheckCookie = ({ children }: any) => {
  //  #. State to manage cookie status
  const [isEnabled, setisEnabled] = useState<"pending" | boolean>("pending");

  useEffect(() => {
    if (cookieIsEnabled()) {
      setisEnabled(true);
    } else {
      setisEnabled(false);
    }
  }, []);

  return (
    <>
      {isEnabled === "pending" ? (
        <Loader />
      ) : isEnabled === true ? (
        (children ?? null)
      ) : (
        <CookieDisabled />
      )}
    </>
  );
};

export default CheckCookie;
