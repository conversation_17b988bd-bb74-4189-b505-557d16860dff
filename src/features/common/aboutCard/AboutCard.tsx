"use client";
import React, { useState } from "react";
import styles from "./AboutCard.module.scss";
import GiftCardDetailsDrawer from "../giftCardDetailsDrawer/GiftCardDetailsDrawer";
import { useTranslation } from "next-i18next";

interface aboutCardInterface {
  cardImage: string;
  cardName: string;
  slug: string;
  onContinue: (id: string) => void;
}

const AboutCard = ({ cardImage, cardName, slug, onContinue  }: aboutCardInterface) => {
  const { t } = useTranslation("common");

  const [toggleDrawer, setToggleDrawer] = useState(false);
  const onDrawerToggle = () => {
    setToggleDrawer((prevToggleDrawer) => !prevToggleDrawer);
  };
  const onCardItemClick = (id: string) => {
    onContinue(id);
  }
  return (
    <>
      <div
        className={styles["about"]}
        onClick={() => {
          setToggleDrawer(true);
        }}
      >
        <button className={styles["about__button"]}>
          {t("about")} {cardName}
        </button>
      </div>

      <GiftCardDetailsDrawer
        open={toggleDrawer}
        onDrawerToggle={onDrawerToggle}
        cardImage={cardImage}
        cardName={cardName}
        slug={slug}
        onCardItemClick={onCardItemClick}
      />
    </>
  );
};

export default AboutCard;
