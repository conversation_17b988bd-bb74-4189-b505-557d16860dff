import React from "react";
import SwipeableDrawer from "@mui/material/SwipeableDrawer";
import styles from "./GiftCardDetailsDrawer.module.scss";
import Image from "next/image";
import { imageBaseUrl } from "@constants/envVariables";
import useBrandListAPI from "@features/animateRoute/brandList/brandListAPI";
import { BRAND_INCLUDED_FILTER, COMPONENT_ID, PAGES } from "@constants/common";
import { useTranslation } from "next-i18next";
import { useAppDispatch } from "@redux/hooks";
import { setCurrentPage } from "../commonSlice";

interface GiftCardDetailsSheetProps {
  open: boolean;
  onDrawerToggle: () => void;
  cardImage: string;
  cardName: string;
  slug: string;
  onCardItemClick: (id: string) => void;
}

const GiftCardDetailsDrawer: React.FC<GiftCardDetailsSheetProps> = ({
  open,
  onDrawerToggle,
  cardImage,
  cardName,
  slug,
  onCardItemClick
}) => {
  const { t } = useTranslation();

  const { fetchGenericBrandList } = useBrandListAPI();
  const DATA_LIMIT = 0;

  const { genericBrandsLoading, genericBrandsData } = fetchGenericBrandList(
    slug,
    BRAND_INCLUDED_FILTER.ALL,
    0,
    DATA_LIMIT,
  );

  const totalCount = genericBrandsData?.genericBrandLists?.totalCount || 0;

  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
  const rightArrow = `${imageBaseUrl}/images/arrow-right.svg`;

  const aboutCardItems = [
    {
      id: COMPONENT_ID.HAPPY_YOU_BRANDS_INCLUDED,
      title: `${t("brandsIncluded")} | ${totalCount}`,
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_ABOUT,
      title: t("aboutThisGift"),
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_TERMS,
      title: t("termsAndConditions"),
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_OFFERS,
      title: t("exclusiveOffersForRecipient"),
    },
  ];

  const onAboutCardItemsClick = (componentId: string) => {
    onCardItemClick(componentId);
  }

  return (
    <SwipeableDrawer
      className="about-card-drawer"
      anchor="bottom"
      open={open}
      onClose={onDrawerToggle}
      onOpen={onDrawerToggle}
      disableSwipeToOpen={true}
      PaperProps={{
        sx: {
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          maxHeight: "90vh",
        },
      }}
    >
      <div className={styles["drawer"]}>
        <div className={styles["drawer__drag-handle"]}></div>
        <div className={styles["drawer__content"]}>
          <div className={styles["drawer__haeder"]}>
            <div className={styles["drawer__card-image"]}>
              <Image
                blurDataURL={preloadImage}
                placeholder="blur"
                src={cardImage}
                alt={"Card Image"}
                width={98}
                height={64}
                priority
                layout={"responsive"}
              />
            </div>
            <p className={styles["drawer__card-title"]}>{cardName}</p>
          </div>
          <div className={styles["drawer__body"]}>
            {aboutCardItems.map((item, index) => (
              <div key={index} className={styles["drawer__menu-item"]} onClick={() => onAboutCardItemsClick(item?.id)}>
                <p className={styles["drawer__menu-title"]}>{item.title}</p>
                <div className={styles["drawer__arrow-icon"]}>
                  <Image
                    blurDataURL={preloadImage}
                    placeholder="blur"
                    src={rightArrow}
                    alt={"Card Image"}
                    width={98}
                    height={64}
                    priority
                    layout={"responsive"}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </SwipeableDrawer>
  );
};

export default GiftCardDetailsDrawer;
