@import '@styles/variables';
@import '@styles/mixins';

.drawer {
  padding: 12px 20px 0 20px;
  &__drag-handle {
    border-radius: 12px;
    background: #d9d9d9;
    height: 4px;
    width: 48px;
    margin: auto;
  }

  &__content {
    margin-top: 24px;
    margin-bottom: 16px;
  }

  &__haeder {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
  }

  &__card-image {
    width: 98px;
    height: 63.894px;
    aspect-ratio: 98/63.89;
    border-radius: 4px;
    overflow: hidden;
  }

  &__card-title {
    color: #0e0f0c;
    font-family: var(--font-bricolage);
    font-size: 24px;
    font-style: normal;
    font-weight: 800;
    line-height: 32px;
    letter-spacing: -0.12px;
  }

  &__menu-item {
    display: flex;
    padding: 16px;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    background: #f5f5f5;
    margin-bottom: 8px;
  }

  &__menu-title {
    margin: 0;
    color: #0e0f0c;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%;
  }

  &__arrow-icon{
    width: 16px;
    height: 16px;

    @include rtl-styles {
      transform: rotate(180deg);
    }
  }
}
