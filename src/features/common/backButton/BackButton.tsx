import { useTranslation } from "next-i18next";
import React from "react";
import Image from "next/image";
import styles from "./BackButton.module.scss";
import { imageBaseUrl } from "@constants/envVariables";

export default function BackButton({
  handleCancel,
  label,
}: {
  handleCancel: () => void;
  label?: string;
}) {
  const { t } = useTranslation("common");
  const backButton = `${imageBaseUrl}/icons/back-btn.svg`;
  const textClass =
    "text-[#0E0F0C] font-bricolage text-[24px] not-italic leading-[24px]";
  return (
    <div
      className={`${styles["back-btn-container"]} mb-[22px]`}
      onClick={handleCancel}
    >
      <Image
        src={backButton}
        alt="back-btn"x
        width={24}
        height={24}
        priority
      />
      {label ? (
        <span className={`${textClass} font-extrabold`}>{t(label)}</span>
      ) : (
        <span className={`${textClass} unicode-bidi-plaintext`}>
          <span className="font-bold font-mona-sans">@</span>
          <span className="font-extrabold">Work</span>
        </span>
      )}
    </div>
  );
}
