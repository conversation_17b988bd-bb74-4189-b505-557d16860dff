import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { AppState } from "@redux/store";
import { NotifierInterface } from "@interfaces/common.inteface";
import { HYDRATE } from "next-redux-wrapper";
export interface CommonSlice {
    isLoading: boolean;
    notifier: NotifierInterface;
    enableLoader: boolean;
    tokenInfo: any;
    enableVolume: boolean;
    enableSnow: boolean;
    showSnackBarError: {
        show: boolean;
        message: string;
    };
    isFirstRenderForSecondTimeUser: boolean;
    isPaymentLoading: boolean;
    ip: string;
    currentPage: string
}

const initialState: CommonSlice = {
    isLoading: false,
    enableLoader: true,
    notifier: {
        title: "",
        description: "",
    },
    tokenInfo: {
        userAttributes: {
            isFirstTimeUser: true,
            wallet: null,
        },
    },
    enableVolume: true,
    enableSnow: true,
    showSnackBarError: {
        show: false,
        message: ""
    },
    isFirstRenderForSecondTimeUser: false,
    isPaymentLoading: false,
    ip: "",
    currentPage: ""
};

// #. Common slice contains the common state methods
export const commonSlice = createSlice({
    name: "common",
    initialState,
    reducers: {
        showLoader: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload;
        },
        setEnableLoader: (state, action: PayloadAction<boolean>) => {
            state.enableLoader = action.payload;
        },
        setNotifierState: (state, action: PayloadAction<NotifierInterface>) => {
            state.notifier = action.payload;
        },
        setTokenInfo: (state, action: PayloadAction<any>) => {
            state.tokenInfo = action.payload;
        },
        setEnableVolume: (state, action: PayloadAction<any>) => {
            state.enableVolume = action.payload;
        },
        setEnableSnow: (state, action: PayloadAction<any>) => {
            state.enableSnow = action.payload;
        },
        setShowSnackBarError: (state, action: PayloadAction<any>) => {
            state.showSnackBarError = action.payload;
        },
        setIsFirstRenderForSecondTimeUser: (state, action: PayloadAction<any>) => {
            state.isFirstRenderForSecondTimeUser = action.payload;
        },
        setIsPaymentLoading: (state, action: PayloadAction<boolean>) => {
            state.isPaymentLoading = action.payload;
        },
        ipAddress: (state, action: PayloadAction<any>) => {
            state.ip = action.payload.ip;
        },
        setCurrentPage: (state, action: PayloadAction<string>) => {
            state.currentPage = action.payload;
        }
    },
    extraReducers(builder) {
        builder.addCase<typeof HYDRATE, PayloadAction<AppState, typeof HYDRATE>>(
            HYDRATE,
            (state, { payload }) => ({ ...state, ...payload.common })
        );
    },
});

export const {
    showLoader,
    setNotifierState,
    setEnableLoader,
    setTokenInfo,
    setEnableVolume,
    setEnableSnow,
    setShowSnackBarError,
    setIsFirstRenderForSecondTimeUser,
    setIsPaymentLoading,
    ipAddress,
    setCurrentPage
} = commonSlice.actions;

// #. State for loader component
export const selectIsLoading = (state: AppState) => state.common.isLoading;

export const getEnableLoader = (state: AppState) => state.common.enableLoader;

// #. State of the notifier component
export const getNotifierState = (state: AppState) => state.common.notifier;

export const getTokenInfo = (state: AppState) => state.common.tokenInfo;

export const getEnableVolume = (state: AppState) => state.common.enableVolume;

export const getEnableSnow = (state: AppState) => state.common.enableSnow;

export const getShowSnackBarError = (state: AppState) => state.common.showSnackBarError

export const getIsFirstRenderForSecondTimeUser = (state: AppState) => state.common.isFirstRenderForSecondTimeUser

export const getIsPaymentLoading = (state: AppState) => state.common.isPaymentLoading;

export const selectedIp = (state: AppState) => state.common.ip;

export const getCurrentPage = (state: AppState) => state.common.currentPage;
// #. Export the reducers
export default commonSlice.reducer;
