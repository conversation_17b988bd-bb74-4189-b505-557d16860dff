import { imageBaseUrl } from "@constants/envVariables";
import React from "react";

const SocialMeta = ({ metaData, url, brandImage }: any) => {
  const ygagSocialImg = `${imageBaseUrl}/images/ygag-social.png`;
  const { title, description } = metaData;
  return (
    <>
      <meta property="og:url" content={url} />
      <meta property="og:type" content="website" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={brandImage || ygagSocialImg} />

      <meta name="twitter:card" content="summary_large_image" />
      <meta property="twitter:domain" content="yougotagift.com" />
      <meta property="twitter:url" content={url} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={brandImage || ygagSocialImg} />
    </>
  );
};

export default SocialMeta;
