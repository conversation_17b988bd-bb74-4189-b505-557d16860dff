import Head from "next/head";
import useAppRouter from "@features/common/router.context";
import { basePath, ecomHostDomain } from "@constants/envVariables";
// import SocialMeta from "./SocialMeta";

const Meta = ({ metaData, isCrawlable, pageNumber }: any) => {
  // Get router context and region
  const { router } = useAppRouter();

  if (!metaData) return null;

  const { title, description, keywords, noIndex = false } = metaData;

  // #. Router query params
  const {
    slug = [],
    id = undefined,
    orderId = undefined,
  } = router?.query || {};

  // #. Check if the url has query params
  const hasQueryParams = id !== undefined || orderId !== undefined;

  // #. Check if the query params have value
  const queryParamHasValue = id?.length > 0 || orderId?.length > 0;

  const hasPageNumber = Boolean(slug[1]);

  // #. Handle url to pass on to meta
  const handleUrl = () => {
    const url = `${ecomHostDomain}${basePath}/${router?.locale}${router?.asPath}`;
    return hasPageNumber
      ? url.split(slug[1])[0]
      : hasQueryParams
        ? url.split("?")[0]
        : url;
  };

  const url = handleUrl();

  const pageUrl = `${ecomHostDomain}${basePath}/${router?.locale}${router?.asPath}`;
  const currentLanguage = router?.locale;
  const [language, region] = currentLanguage?.split("-");

  // Generate alternate URLs for Arabic and English
  const arUrl = url?.replace(currentLanguage, "ar-sa");
  const enUrl = url?.replace(currentLanguage, "en-sa");

  return (
    <Head>
      {title && <title>{title}</title>}
      {description && <meta name="description" content={description} />}
      {keywords && <meta name="keywords" content={keywords} />}
      {/* <SocialMeta metaData={metaData} url={pageUrl} brandImage={brandImage} /> */}
      <>
        <link rel="canonical" href={url} />
        <link rel="alternate" hrefLang={currentLanguage} href={url} />
      </>

      {currentLanguage === "en-sa" && (
        <link rel="alternate" hrefLang="ar-sa" href={arUrl} />
      )}
      {currentLanguage === "ar-sa" && (
        <link rel="alternate" hrefLang="en-sa" href={enUrl} />
      )}
      {language === "en" && (
        <link rel="alternate" hrefLang="x-default" href={pageUrl} />
      )}
    </Head>
  );
};

export default Meta;
