import secondsToHms from "@utils/secondsToHms";
import { useTranslation } from "next-i18next";
import { FC, useEffect, useState } from "react";
import styles from './IntervalTimer.module.scss';

interface IntervalTimerProps {
  onComplete: () => void;
  startingValue: number
}

const IntervalTimer: FC<IntervalTimerProps> = ({ onComplete, startingValue }) => {
  // #. Get translations
  const { t } = useTranslation("common");

  // #. State for timer in seconds
  const [timerInSec, setTimerInSec] = useState(startingValue);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (timerInSec > 0) {
      timer = setTimeout(() => {
        setTimerInSec((val) => val - 1);
      }, 1000);
    } else {
      onComplete();
    }

    return () => clearTimeout(timer);
  }, [timerInSec]);

  // #. Get hour,minute & second from seconds
  const { hour, minute, second } = secondsToHms(timerInSec);
  const timeRemaining = `${minute} : ${second}`;
  return (
    <div className={styles["interval-timer"]}>
      <span className={styles["interval-timer__info"]}>
        {t("resendCodeIn")} - {' '}
      </span>
      <span
        className={styles["interval-timer__time"]}
      >
        {timeRemaining}
      </span>
    </div>
  );
};

export default IntervalTimer;
