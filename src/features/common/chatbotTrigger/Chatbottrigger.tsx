import { CHATBOT_TRIGGER_CONFIG } from "@constants/common";
import { useEffect } from "react";

const setCookie = (name: string, value: string, days: number) => {
    const expires = new Date(Date.now() + days * 86400000).toUTCString();
    document.cookie = `${name}=${value}; expires=${expires}; path=/`;
};

const getCookie = (name: string) => {
    const cookieArr = document.cookie.split("; ");
    const cookie = cookieArr.find(row => row.startsWith(`${name}=`));
    return cookie ? cookie.split("=")[1] : null;
};

const ChatbotTrigger = () => {
    useEffect(() => {
        const chatbotOpened = getCookie(CHATBOT_TRIGGER_CONFIG.COOKIE_NAME);

        if (!chatbotOpened) {
            const timer = setTimeout(() => {
                if (window.Intercom) {
                    window.Intercom("show");
                    setCookie(
                        CHATBOT_TRIGGER_CONFIG.COOKIE_NAME,
                        "true",
                        CHATBOT_TRIGGER_CONFIG.COOKIE_EXPIRY_DAYS
                    );
                } else {
                    console.log("Intercom is not loaded.");
                }
            }, CHATBOT_TRIGGER_CONFIG.SHOW_AFTER_MINUTES * 60 * 1000);

            return () => clearTimeout(timer);
        }
    }, []);

    return null;
};

export default ChatbotTrigger;
