import React, { useEffect, useRef, useState } from "react";
import styles from "./CountryPhoneCode.module.scss";
import {
  getCountries,
  getCountryCallingCode,
} from "react-phone-number-input/input";
import { ListSubheader, MenuItem, Select, TextField } from "@mui/material";
import en from "react-phone-number-input/locale/en.json";
import ar from "react-phone-number-input/locale/ar.json";
import { CountryCode } from "libphonenumber-js";
import isAlphabet from "@utils/isAlphabet";
import { useTranslation } from "next-i18next";
import getConfig from "next/config";
import { styled } from "@mui/material/styles";
import { outlinedInputClasses } from "@mui/material/OutlinedInput";
import { CircleFlag } from "react-circle-flags";
import { imageBaseUrl } from "@constants/envVariables";

const StyledTextField = styled(TextField)({
  [`& .${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]:
    {
      borderWidth: "1px !important",
      borderColor: "#d9d9d9 !important",
      borderRadius: "8px",
    },
});

const CountryPhoneCode = ({
  phoneNumberRef,
  showCountryName = true,
  defaultValue = "AE",
  setCountryCallback,
  showCountryNameInSelection = true,
  dialCode,
  enableSearch = false,
  language = "en",
  blacklistedCountries = [],
  prefill = false,
}: {
  phoneNumberRef: React.RefObject<HTMLInputElement>;
  showCountryName: boolean;
  defaultValue: CountryCode;
  setCountryCallback: any;
  showCountryNameInSelection: boolean;
  dialCode?: string;
  enableSearch?: boolean;
  language?: string;
  blacklistedCountries?: [];
  prefill?: boolean;
}) => {
  const { t } = useTranslation("common");

  // # Set the reference of the search input
  const searchRef = useRef<HTMLInputElement>(null);

  const blackList: any = blacklistedCountries;
  const countriesList = getCountries().filter(
    (country: any) => !blackList.includes(country),
  );
  const [countryCode, setCountryCode] = useState<CountryCode>(
    defaultValue && !blackList.includes(defaultValue)
      ? defaultValue
      : countriesList[0],
  );
  const [searchText, setSearchText] = useState("");
  const [countryOptions, setCountryOptions] = useState<any>(countriesList);
  const defaultLan = language == "ar" ? ar : en;

  const handleSelectOpen = () => {
    setSearchText("");
    setTimeout(() => {
      searchRef.current?.focus();
    }, 100);
  };

  const onChangeHandler = async (event: any) => {
    if (phoneNumberRef.current) phoneNumberRef.current.value = "";
    const countryCode = event?.target?.value;
    const dialCode = `+${getCountryCode(countryCode)}`;

    if (!countryCode) return;
    setCountryCode(countryCode);
    setCountryCallback({ countryCode, dialCode });
    setCountryOptions(countriesList);
  };

  const containsText = (text: string, searchText: string) =>
    text.toLowerCase().indexOf(searchText.toLowerCase()) > -1;

  useEffect(() => {
    // #. Set country code based on the dial-code
    if (dialCode && prefill) {
      for (let i = 0; i < countriesList.length; i++) {
        const countryDialCode = `+${getCountryCode(countriesList[i])}`;
        if (dialCode === countryDialCode) {
          if (!blackList.includes(countriesList[i])) {
            setCountryCode(countriesList[i]);
          } else {
            setCountryCode(countriesList[0]);
          }
          break;
        }
      }
    }
  }, [dialCode]);

  useEffect(() => {
    let filteredCountries = Object.keys(en).filter((key: any) => {
      if (
        ![
          "ext",
          "country",
          "phone",
          "ZZ",
          "AB",
          ...blacklistedCountries,
        ].includes(key)
      ) {
        let text = defaultLan[key as keyof typeof en];
        if (containsText(text, searchText)) {
          return defaultLan[key as keyof typeof defaultLan];
        }
      }
    });
    setCountryOptions(filteredCountries);
    setTimeout(() => {
      searchRef.current?.focus();
    }, 100);
  }, [searchText]);

  useEffect(() => {
    if (defaultValue) {
      setCountryCode(
        !blackList.includes(defaultValue)
          ? defaultValue
          : countriesList[0],
      );
    }
  }, [defaultValue]);

  const getCountryCode = (country: any) => {
    const isValidCountry = getCountries().includes(country?.toUpperCase());
    return isValidCountry ? getCountryCallingCode(country) : "0";
  };

  return (
    <div className={`country-phone-code ${styles["country-phone-code"]}`}>
      <Select
        value={countryCode}
        renderValue={(id) => {
          return (
            <div className={styles["country-phone-code-container"]}>
              <CircleFlag countryCode={id?.toLowerCase()} height="24" />
              {showCountryNameInSelection && (
                <span>&nbsp;{defaultLan[id]}</span>
              )}
              <span>&nbsp;+{getCountryCode(id)}</span>
            </div>
          );
        }}
        MenuProps={{
          transformOrigin: {
            vertical: "top",
            horizontal: "left",
          },
          classes: {
            paper: `paper-signin-dropdowns country-phone-code`,
          },
          PaperProps: {
            style: {
              transform: "translate(0px, -25px)",
            },
          },
        }}
        className={styles["country-select"]}
        onChange={onChangeHandler}
        onClick={() => {
          if (searchText && searchText.length == 0) {
            setCountryOptions(setCountryOptions(countriesList));
          }
        }}
        onOpen={handleSelectOpen}
      >
        {enableSearch && (
          <ListSubheader
            className={styles["country-phone-code__search-box"]}
          >
            <StyledTextField
              size="small"
              // Autofocus on textfield
              inputRef={searchRef}
              placeholder={t("searchCountry")}
              fullWidth
              InputProps={{
                startAdornment: (
                  <img
                    className={styles["country-phone-code__search-icon"]}
                    src={`${imageBaseUrl}/images/Search.svg`}
                    alt="search-icon"
                  />
                ),
              }}
              className={styles["country-phone-code__search-input"]}
              onChange={(e: any) => setSearchText(e.target.value)}
              onKeyDown={(e) => {
                if (e.key !== "Escape") {
                  // Prevents autoselecting item while typing (default Select behaviour)
                  e.stopPropagation();
                }
              }}
              onKeyPress={(e: any) => {
                if (!isAlphabet(e)) {
                  e.preventDefault();
                }
              }}
            />
          </ListSubheader>
        )}

        {countryOptions.map((country: any, index: any) => (
          <MenuItem
            key={index}
            value={country}
            className={`${
              showCountryName === true
                ? styles["country-phone-code__menu-item"]
                : styles["country-phone-code__menu-item-send_to"]
            } `}
          >
            {
              <div className={styles["country-phone-code__flag"]}>
                <CircleFlag
                  countryCode={country?.toLowerCase()}
                  width="24"
                />
              </div>
            }
            {showCountryName && (
              <span>{defaultLan[country as keyof typeof defaultLan]}</span>
            )}
            {
              <div className={styles["country-phone-code__code"]}>
                {`+${getCountryCode(country)}`}
              </div>
            }
          </MenuItem>
        ))}
      </Select>
    </div>
  );
};

export default CountryPhoneCode;
