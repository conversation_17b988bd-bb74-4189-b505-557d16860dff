@import "@styles/variables";
@import "@styles/mixins";

.country-phone-code {
  display: flex;
  &-container {
    display: flex;
    @include rtl-styles {
      gap: 5px;
      align-items: center;

      > span + span {
        unicode-bidi: embed;
        direction: ltr;
      }
      span {
        unicode-bidi: plaintext;
      }
    }
  }
  .country-select {
    width: 100% !important;
    color: $dark-purple !important;
    margin-right: 2px;
  }

  &__menu-item {
    padding: 12px 16px !important;
    display: grid !important;
    grid-template-columns: 24px 1fr 50px;
    gap: 10px;
    min-height: 23px !important;
    font-size: 14px !important;
    font-family: $default-font-family !important;
    margin-bottom: 8px;

    &:hover {
      background-color: $grey-primary !important;
      border-radius: $border-radius-min;
    }

    > span {
      color: $dark-purple !important;
      white-space: break-spaces;
    }
  }

  &__menu-item-send_to {
    padding: 7px;
    display: grid;
    grid-template-columns: 70px auto;
    gap: 10px;
    min-height: 23px;
    font-size: 14px;

    &:hover {
      background-color: $light-purple;
      border-radius: $border-radius-min;
    }

    > div {
      > img {
        width: 30px;
        height: 30px;
        object-fit: contain;
        border-radius: 3px;
      }

      > span {
        @include font-size(20);
      }
    }
  }

  &__search-box {
    padding: 16px 16px 8px 16px;
  }

  &__search-input {
    div {
      padding-left: 10px !important;

      @include rtl-styles {
        padding-right: 10px !important;
      }
    }

    input {
      padding: 0;
      padding-left: 11px !important;
      height: 50px;
      min-height: 23px;
      border-radius: 8px;
      @include font-size(14);

      @include rtl-styles {
        padding-right: 11px !important;
      }

      &::placeholder {
        @include font-size(14);
        @include rtl-styles {
          text-align: right;
        }

        margin: 0 0 0 14px;
        opacity: 1 !important;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 18px;
        letter-spacing: normal;
        text-align: left;
        color: $placeholder-gray;
      }
    }
  }

  &__search-icon {
    width: 24px;
    height: 24px;
  }

  &__flag {
    width: 24px;
    height: 24px;

    > span {
      @include font-size(14);
    }

    img {
      max-height: 24px;
    }
  }

  &__code {
    text-align: right;

    @include rtl-styles {
      text-align: left;
    }
  }
}
