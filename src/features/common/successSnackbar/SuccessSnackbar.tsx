import * as React from 'react';
import { Snackbar, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import getConfig from 'next/config';
import styles from './SuccessSnackbar.module.scss';
import { useTranslation } from 'next-i18next';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import {
  getShowSnackBarError,
  setShowSnackBarError,
} from '@features/common/commonSlice';
import { imageBaseUrl } from '@constants/envVariables';

function SuccessSnackbar({
  open,
  setOpen,
  text,
  showError = false,
  type = 'success',
  noAutoClose = false,
}: any) {
  const errorState = useAppSelector(getShowSnackBarError);
  const dispatch = useAppDispatch();
  const { t } = useTranslation('common');

  const handleClose = (
    event: React.SyntheticEvent | Event,
    reason?: string,
  ) => {
    if (reason === 'clickaway') return;

    if (showError) {
      dispatch(
        setShowSnackBarError({
          show: false,
          message: '',
        }),
      );
    } else {
      setOpen(false);
    }
  };

  const content = (
    <div className={`${styles['success-toast__content']} ${noAutoClose ? styles['success-toast__content--auto-close'] : ''}`}>
      <img
        src={
          showError || type !== 'success'
            ? `${imageBaseUrl}/icons/error-icon.svg`
            : `${imageBaseUrl}/icons/success-icon.svg`
        }
        alt="status-icon"
      />
      {showError ? errorState?.message : text}
      {noAutoClose && <IconButton
        onClick={handleClose}
        size="small"
        className='close-icon'
        sx={{
          position: 'absolute',
          top: 6,
          color: '#FFFFFF',
        }}>
        <CloseIcon sx={{ fontSize: 18, 
          fontWeight: 'bold', filter: "brightness(200%)"}} />
      </IconButton>}
    </div>
  );

  return (
    <div className="success-toast">
      <Snackbar
        open={showError ? errorState?.show : open}
        autoHideDuration={noAutoClose ? null : 3000}
        onClose={handleClose}
        message={content}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      />
    </div>
  );
}

export default SuccessSnackbar;
