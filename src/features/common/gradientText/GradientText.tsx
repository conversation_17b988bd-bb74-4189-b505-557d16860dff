import { useTranslation } from "next-i18next";
import React from "react";
import useApp from "../common.context";
import styles from "./GradientText.module.scss";

export default function GradientText() {
  const { t } = useTranslation();
  const { state } = useApp();
  return (
    <div className={styles["gradient-text"]}>
      <p className={styles["gradient-text-welcome"]}>{t("welcomeBack")}</p>
      <p className={styles["gradient-text-email"]}>
        {state?.login?.email || "<EMAIL>"}
      </p>
    </div>
  );
}
