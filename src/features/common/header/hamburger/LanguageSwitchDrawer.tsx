import { SwipeableDrawer } from "@mui/material";
import { useTranslation } from "next-i18next";
import React from "react";
import styles from "./HamburgerMenu.module.scss";
import { imageBaseUrl } from "@constants/envVariables";

interface LanguageSwitchDrawerProps {
  open: boolean;
  onDrawerToggle: () => void;
  localeCode: string;
  action: (lang: string) => void;
}

const LanguageSwitchDrawer = ({
  open,
  onDrawerToggle,
  localeCode,
  action,
}: LanguageSwitchDrawerProps) => {
  const { t } = useTranslation();
  const checkedIcon = `${imageBaseUrl}/icons/checked.svg`;

  const languages = [
    { code: "ar", label: t("arabic") },
    { code: "en", label: t("english") },
  ];

  return (
    <SwipeableDrawer
      className="language-switcher-drawer"
      anchor="bottom"
      open={open}
      onClose={onDrawerToggle}
      onOpen={onDrawerToggle}
      disableSwipeToOpen
      PaperProps={{
        sx: {
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          maxHeight: "90vh",
        },
      }}
    >
      <div className="px-[20px] pt-[12px] pb-[8px]">
        <div className="h-[4px] w-[48px] bg-[#D9D9D9] mx-auto mb-[24px]" />

        <p className={styles["language-switcher__title"]}>
          {t("selectLanguage")}
        </p>

        <div className={styles["language-switcher__content"]}>
          {languages.map(({ code, label }) => (
            <div
              key={code}
              onClick={() => action(code)}
              className={styles["language-switcher__item"]}
            >
              <p>{label}</p>
              {localeCode === code && (
                <img src={checkedIcon} alt="checked" />
              )}
            </div>
          ))}
        </div>
      </div>
    </SwipeableDrawer>
  );
};

export default LanguageSwitchDrawer;
