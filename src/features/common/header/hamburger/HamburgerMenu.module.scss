@import "@styles/variables";
@import "@styles/mixins";

.menu {
  width: 100%;
  height: 100vh;

  &__header {
    padding: 24px 20px;
    display: flex;
    align-items: center;
    gap: 8px;   
    h5 {
      margin: 0;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px; /* 133.333% */
      text-transform: capitalize;
    }
  }

  &__items {
    &-wrapper {
      background-color: #fff;
      border-radius: 12px;
      padding: 0 16px;

      a,
      > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 0;
        border-bottom: 1px solid #d9d9d9;
        font-family: var(--font-bricolage);
        font-weight: 800;
        font-size: 18px;
        line-height: 20px; /* 125% */
        letter-spacing: -0.16px;

        &:last-child {
          border-bottom: none;
        }

        span {
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px; /* 133.333% */
          letter-spacing: -0.12px;
          font-family: var(--font-mona-sans);
        }

        > div {
          display: flex;
          align-items: center;
          gap: 8px;

          p {
            margin: 0;
          }
        }
      }

      &__balance {
        display: flex;
        padding: 2px 8px;
        justify-content: center;
        border-radius: 40px;
        background: #99c6ff;
        font-size: 14px !important;
        font-style: normal;
        font-weight: 600;
        line-height: 18px; /* 128.571% */
        letter-spacing: -0.14px;
        gap: 8px !important;
      }

      &__flag {
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }
    }

    &-signout {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      border-radius: 12px;
      padding: 24px 16px;
      margin-top: 32px;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px; /* 125% */
      letter-spacing: -0.16px;
      > div {
        display: flex;
        align-items: center;
        gap: 8px;

        p {
          margin: 0;
        }
      }
    }
  }

  &__footer {
    margin-top: 32px;
    // position: absolute;
    // bottom: 5px;
    // left: 50%;
    // transform: translate(-50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;
    text-align: center;
    align-items: center;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 160% */
    letter-spacing: -0.1px;
    color: #0e0f0c;
    p {
      margin: 0;
    }
  }
}

.language-switcher {
  &__title {
    font-family: var(--font-bricolage);
    color: #0e0f0c;
    font-size: 24px;
    font-style: normal;
    font-weight: 800;
    letter-spacing: -0.12px;
    margin-bottom: 16px;
  }
  &__content {
    display: flex;
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 12px;
    background: #f5f5f5;
  }
  &__item {
    background: #fff;
    padding: 13px 8px;
    width: 100%;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    p {
      color: #0e0f0c;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
    }
  }
}
