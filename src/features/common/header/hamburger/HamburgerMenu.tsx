"use client";

import {
  ecomUrl,
  groupGiftUrl,
  imageBaseUrl,
  solutionsHubURL,
} from "@constants/envVariables";
import styles from "./HamburgerMenu.module.scss";
import Link from "next/link";
import React, { MouseEventHand<PERSON>, useState } from "react";
import { PAGEURLS, WORKPAGE_URLS } from "@constants/common";
import WorkAccount from "../workAccount/WorkAccount";
import { useTranslation } from "next-i18next";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import LanguageSwitchSwiper from "./LanguageSwitchDrawer";
import LanguageSwitchDrawer from "./LanguageSwitchDrawer";

interface HamburgerMenuProps {
  activeRegion: any;
  drawerChange: (
    anchor: "left" | "right",
    open: boolean,
  ) => MouseEventHandler<HTMLAnchorElement>;
  localeCode: string;
  isUserSignedIn: boolean;
  wallet: walletInterface;
}

interface walletInterface {
  currency: string;
  balance: string;
  minAmount: number;
  status: boolean;
}
/**
 * @method HamburgerMenu
 * @description Hamburger Menu component
 * @returns {JSX.Element}
 */
const HamburgerMenu = ({
  isUserSignedIn,
  activeRegion,
  drawerChange,
  localeCode,
  wallet,
}: HamburgerMenuProps): React.JSX.Element => {
  const { t } = useTranslation("common");
  const pathname = usePathname();
  const router = useRouter();

  const [toggleDrawer, setToggleDrawer] = useState(false);

  const code = activeRegion?.country?.code;
  const currentLanguage = localeCode === "en" ? t("english") : t("arabic");
  const isLanguageSwitchingAllowed = code == "SA";
  const groupGiftSiteURL =
    groupGiftUrl + `${localeCode}-${code.toLowerCase()}`;
  const giftingUrl =
    ecomUrl + `${PAGEURLS.SHOP}/${localeCode}-${code.toLowerCase()}`;
  const offersUrl =
    ecomUrl +
    `${PAGEURLS.SHOP}/${localeCode}-${code.toLowerCase()}${PAGEURLS.offers}`;

  const languageSwitcher = (locale: string) => {
    if (localeCode !== locale) {
      const segments = pathname.split("/");
      segments[1] = `${locale}-${code.toLowerCase()}`;
      const newPath = segments.join("/");
      router.push(newPath);
    }
    onDrawerToggle();
  };

  const onDrawerToggle = () => {
    setToggleDrawer(!toggleDrawer);
  };

  return (
    <>
      <div className={`${styles["menu"]}`}>
        {isUserSignedIn && <WorkAccount wallet={wallet} />}
        <div className={`${styles["menu__items"]}`}>
          <div className={`${styles["menu__items-wrapper"]}`}>
            {isLanguageSwitchingAllowed && (
              <div onClick={onDrawerToggle}>
                <p>{t("changeLanguage")}</p>
                <span>{currentLanguage}</span>
              </div>
            )}
            <Link href={giftingUrl}>
              <p>{t("gifting")}</p>
            </Link>
            <Link href={groupGiftSiteURL}>
              <p>{t("groupGifting")}</p>
            </Link>
            <Link href={offersUrl}>
              <p>{t("happyYouOffers")}</p>
            </Link>
            <Link href={solutionsHubURL || "#"}>
              <p>{t("solutionHub")}</p>
            </Link>
          </div>
        </div>
      </div>
      <LanguageSwitchDrawer
        open={toggleDrawer}
        onDrawerToggle={onDrawerToggle}
        localeCode={localeCode}
        action={languageSwitcher}
      />
    </>
  );
};

export default HamburgerMenu;
