import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import styles from "./Header.module.scss";
// import useAppRouter from "@features/common/router.context";
// import { PAGEURLS } from "@constants/common";
// import useCartAPI, { fetchBasicCartSummary } from "@features/cart/cartAPI";
// import { useAppDispatch, useAppSelector } from "@redux/hooks";
// // import useCart from "@features/cart/cartSlice";
import getConfig from "next/config";
import { imageBaseUrl } from "@constants/envVariables";
// import { getTokenInfo, getIpAddress } from "../commonSlice";
// const PendingPaymentConfirm = dynamic(
//   () =>
//     import("@features/cart/pendingPaymentConfirm/PendingPaymentConfirm"),
// );

interface CartInfoInterface {
  onCartIconClicked?: () => void;
  showCartIcon?: boolean;
}

/**
 * @method CartInfo
 * @description Componenet to select stores
 */
const CartInfo = ({
  onCartIconClicked,
  showCartIcon = true,
}: CartInfoInterface): JSX.Element => {
  const cartIcon = `${imageBaseUrl}/icons/cart-black.svg`;
  // const ipAddress = useAppSelector(getIpAddress);

  // // badge content
  const [badgeContent, setBadgeContent] = useState(0);

  // // use app dispatch
  // const dispatch = useAppDispatch();

  // const {
  //   state: { activeRegion, locale },
  //   router,
  // } = useAppRouter();

  // const { FetchCartSummary } = useCartAPI();
  // const tokens = useAppSelector(getTokenInfo);

  // const { cartSummaryLoading, cartSummaryData, refetchCartSummary } =
  //   FetchCartSummary(
  //     activeRegion?.node?.code,
  //     tokens?.isUserSignedIn,
  //     tokens,
  //     ipAddress?.ip,
  //   );

  // const {
  //   setCartSummary,
  //   updatedCartItem,
  //   setCartItemForUpdate,
  //   setUpdatedCartItem,
  //   setIsCartSummaryLoading,
  // } = useCart();

  // // #. Get removed cart item info
  // const updatedCartItemInfo = useAppSelector(updatedCartItem);

  // // #. Fetch cart data on store changes
  // useEffect(() => {
  //   if (activeRegion?.node?.code) {
  //     fetchBasicData();
  //   }
  // }, [activeRegion?.node?.code]);

  // const fetchBasicData = async () => {
  //   if (
  //     tokens?.isGuestUser ||
  //     (tokens && tokens?.AccessToken && tokens.AccessToken.length)
  //   ) {
  //     const { data: cartSummaryData } = await fetchBasicCartSummary(
  //       locale,
  //       activeRegion?.node?.code,
  //       tokens?.isUserSignedIn,
  //       tokens,
  //       ipAddress?.ip,
  //     );
  //     // #. Store the summary info to cart slice
  //     dispatch(setCartSummary(cartSummaryData?.cart));
  //     // #. Set count info to cart icon badge
  //     const cartCount = cartSummaryData?.cart?.totalQuantity || 0;
  //     setBadgeContent(cartCount);
  //   }
  // };

  // //#. Update cart summary data changes to components

  // useEffect(() => {
  //   if (
  //     (tokens?.isUserSignedIn || tokens?.isGuestUser) &&
  //     cartSummaryData
  //   ) {
  //     if (!cartSummaryLoading) {
  //       // #. Store the summary info to cart slice
  //       dispatch(setCartSummary(cartSummaryData?.cart));
  //       // #. Set count info to cart icon badge
  //       const cartCount = cartSummaryData?.cart?.totalQuantity || 0;
  //       setBadgeContent(cartCount);
  //     }
  //   }

  //   return () => {
  //     dispatch(setCartSummary({}));
  //   };
  // }, [cartSummaryData]);

  // //#. Refetch cart summary once the item removed from the cart
  // useEffect(() => {
  //   const cartInfo: any = updatedCartItemInfo;
  //   if (cartInfo?.item && cartInfo?.success) {
  //     refetchCartSummary().then(() => {
  //       // #. Clear removed info from the store
  //       dispatch(setCartItemForUpdate({ type: "", item: "" }));
  //       dispatch(setUpdatedCartItem({ item: "" }));
  //     });
  //   }
  // }, [updatedCartItemInfo]);

  // const onCartClicked = () => {
  //   onCartIconClicked && onCartIconClicked();
  //   router?.push(`${PAGEURLS.CART}`, undefined);
  // };

  return (
    <>
      {showCartIcon && (
        <a className={styles["cart-info"]} onClick={() => {}}>
          <img src={cartIcon} alt="Cart" />
          {!!badgeContent && (
            <span className={styles["cart-info__count"]}>
              {badgeContent}
            </span>
          )}
        </a>
      )}

      {/* {cartSummaryData?.cart?.pendingPaymentData?.orderReference && (
        <PendingPaymentConfirm
          pendingPaymentData={cartSummaryData?.cart?.pendingPaymentData}
        />
      )} */}
    </>
  );
};

export default CartInfo;
