@import "@styles/variables";
@import "@styles/mixins";

.wrapper {
  position: sticky;
  z-index: 1000;
  top: 0;
  background-color: $white;
  padding: 0 20px;
  border-bottom: 4px solid #99c6ff;
}

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  gap: 10px;

  &__left {
    display: block;
    width: 50px;
  }

  &__logo-container {
    > a {
      > img {
        display: block;
        width: 60.142px;
        height: 40px;
        flex-shrink: 0;
        aspect-ratio: 60.14/40;
      }
    }
  }

  &__right-view {
    display: flex;
    align-items: center;

    .cart-logo {
      width: 24px;
      height: 24px;
      margin-right: 15px;

      @include rtl-styles {
        margin-right: 0;
        margin-left: 15px;
      }
    }
  }

  &__hamburger {
    display: flex;
    justify-content: end;
    width: 24px;
    cursor: pointer;

    @include rtl-styles {
      margin-left: 0;
    }
  }
}

.right-view__cart {
  @include rtl-rotate;
}

.selected-country {
  display: flex;
  align-items: center;
  cursor: pointer;

  > span {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 600;
    color: $dark-purple;

    @include rtl-styles {
      margin-left: 0;
      margin-right: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 70%;

      @media (max-width: ( $msm + 40)) {
        width: 60%;
      }

      @media (max-width: ( $msm)) {
        width: 55%;
      }
    }
  }

  img {
    width: 32px;
    height: 32px;
    border-radius: 100px;
    object-fit: cover;
  }

  @include rtl-styles {
    margin: 0 0 0 24px;
  }
}

.search-home {
  img {
    width: 24px;
    height: 24px;
  }
}

.region-selector {
  padding: 20px;
  width: 100%;

  > h3 {
    font-size: 14px;
    color: $dark-purple;
    letter-spacing: normal;
    font-weight: medium;
    font-family: "Poppins";
    margin: 0 0 15px;
  }
}

.selector {
  background: $pale-grey3;
  border-radius: 16px;
  padding: 0 20px;
}

.selector-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid $light-grey;

  &:last-child {
    border-bottom: none;
  }

  &__item-left {
    font-size: 14px;
    color: $dark-purple;

    > img {
      width: 25px;
      margin-right: 20px;
      object-fit: cover;
      object-position: left;
      border-radius: 4px;

      @include rtl-styles {
        margin-right: 0;
        margin-left: 20px;
      }
    }
  }

  > div {
    display: flex;
    align-items: center;
  }
}

.cart-info {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-right: 24px;

  @include rtl-rotate;

  img {
    width: 24px;
    height: 24px;
  }

  &__count {
    position: absolute;
    background: #b800c4;
    border-radius: 50%;
    width: 14px;
    height: 14px;
    font-size: 8px;
    color: #fff;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #fff;
    left: 18px;
    top: -8px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include rtl-rotate;
  }

  @include rtl-styles {
    margin: 0 0 0 24px;
  }
}

.offers {
  border-color: $offer-orange !important;
}

.clear-border {
  border-bottom: 4px solid $white;
}
