@import "@styles/variables";
@import "@styles/mixins";

.WorkAccount {
  &__title {
    &--work {
      font-family: var(--font-bricolage);
    }
  }

  &__menu-wrapper {
    background: #fff;
    border-radius: 12px;
    padding: 0 16px;

    a,
    > div {
      padding: 24px 0;
      display: flex;
      font-family: var(--font-bricolage);
      font-size: 18px;
      font-weight: 800;
      line-height: 24px;
      letter-spacing: -0.09px;
      justify-content: space-between;
      border-bottom: 0.5px solid #d9d9d9;

      &:last-child {
        border-bottom: none;
      }

      img{
        @include rtl-styles {
          transform: rotate(180deg);
        }
      }
    }
  }

  &__balance {
    display: flex;
    gap: 8px;
    span {
      background: #99c6ff;
      padding: 0px 8px;
      font-size: 14px;
      font-weight: 600;
      height: 22px;
      letter-spacing: -0.14px;
      border-radius: 40px;
    }
  }
}
