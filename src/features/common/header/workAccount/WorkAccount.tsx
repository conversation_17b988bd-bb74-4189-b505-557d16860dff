"use client";
import { imageBaseUrl } from "@constants/envVariables";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
} from "@mui/material";
import React, { useState } from "react";
import styles from "./WorkAccount.module.scss";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { WORKPAGE_URLS } from "@constants/common";
import Confirm from "@features/common/confirm/Confirm";

interface walletInterface {
  currency: string;
  balance: string;
  minAmount: number;
  status: boolean;
}

interface WorkAccountProps {
  wallet: walletInterface;
}

const WorkAccount = ({ wallet }: WorkAccountProps) => {
  const { t } = useTranslation("common");
  const expand = `${imageBaseUrl}/icons/arrow-down.svg`;
  const info = `${imageBaseUrl}/icons/info-circle-black-icon.svg`;

  const [openModal, setOpenModal] = useState(false);
  const [isSingleBtn, setIsSingleBtn] = useState(true);
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [confirmText, setConfirmText] = useState(t("done"));

  const handleMenuClick = (title: string) => {
    switch (title) {
      case t("creditBalance"):
        setTitle(t("creditBalance"));
        setMessage(t("creditBalanceMessage"));
        break;
      case t("customHappyYou"):
        setTitle(t("customHappyYou"));
        setMessage(t("customHappyYouMessage"));
        break;
      case t("savedCards"):
        setTitle(t("savedCards"));
        setMessage(t("savedCardsMessage"));
        break;
      case t("signOut"):
        setConfirmText(t("proceed"));
        setIsSingleBtn(false);
        setTitle(t("signOut"));
        setMessage(t("signOutMessage"));
        break;
      default:
    }
    setOpenModal(true);
  };

  const handleClose = (isConfirmed: boolean) => {
    if (isConfirmed && title === t("signOut")) {
      // Handle sign out logic here
      console.log("User signed out");
    } else {
      setOpenModal(false);
    }
    setIsSingleBtn(true);
    setConfirmText(t("done"));
  };

  return (
    <div className={`work-account-accordion`}>
      <Accordion>
        <AccordionSummary
          expandIcon={<img src={expand} alt="expand icon" />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <p
            className={`text-[24px] font-[800] ${styles["WorkAccount__title"]}`}
          >
            <span>@</span>
            <span className={`${styles["WorkAccount__title--work"]}`}>
              {t("workDetails")}
            </span>
          </p>{" "}
        </AccordionSummary>
        <AccordionDetails>
          <div className={`${styles["WorkAccount__menu-wrapper"]}`}>
            <Link href={WORKPAGE_URLS?.PROFILE}>
              <p>{t("profile")}</p>
              <img
                src={`${imageBaseUrl}/icons/arrow-right.svg`}
                alt="arrow"
                height={16}
                width={16}
              />
            </Link>
            <Link href="#">
              <p>{t("myOrders")}</p>
              <img
                src={`${imageBaseUrl}/icons/arrow-right.svg`}
                alt="arrow"
                height={16}
                width={16}
              />
            </Link>
            <div>
              <p>{t("creditBalance")}</p>
              <div className={`${styles["WorkAccount__balance"]}`}>
                {parseFloat(wallet?.balance ?? "0") > 0 && (
                  <span>
                    {wallet?.currency} {wallet?.balance || 0}
                  </span>
                )}

                <img
                  onClick={() => handleMenuClick(t("creditBalance"))}
                  src={info}
                  alt=""
                  height={16}
                  width={16}
                />
              </div>
            </div>
            <div>
              <p>{t("customHappyYou")}</p>
              <img
                onClick={() => handleMenuClick(t("customHappyYou"))}
                src={info}
                alt="info"
                height={16}
                width={16}
              />
            </div>
            <div>
              <p>{t("savedCards")}</p>
              <img
                onClick={() => handleMenuClick(t("savedCards"))}
                src={info}
                alt="info"
                height={16}
                width={16}
              />
            </div>
            <div onClick={() => handleMenuClick(t("signOut"))}>
              <p className="text-[#e74848]">{t("signOut")}</p>
              <img
                src={`${imageBaseUrl}/icons/arrow-right.svg`}
                alt="arrow"
                height={16}
                width={16}
              />
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
      <Confirm
        open={openModal}
        message={message}
        title={title}
        onClose={handleClose}
        singleBtn={isSingleBtn}
        confirmText={confirmText}
      />
    </div>
  );
};

export default WorkAccount;
