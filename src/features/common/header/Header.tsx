import dynamic from "next/dynamic";
import React, { useState } from "react";
import styles from "./Header.module.scss";
import { SwipeableDrawer } from "@mui/material";
import Link from "next/link";
import Image from "next/image";
import { imageBaseUrl } from "@constants/envVariables";

const HamburgerMenu = dynamic(() => import("./hamburger/HamburgerMenu"));
const RegionSelector = dynamic(() => import("./RegionSelector"));
// const CartInfo = dynamic(() => import("./CartInfo"));

interface walletInterface {
  currency: string;
  balance: string;
  minAmount: number;
  status: boolean;
}

const hamburger = `${imageBaseUrl}/icons/hamburger.svg`;
const close = `${imageBaseUrl}/icons/close-circle.svg`;
/**
 * @method Header
 * @description Header component
 * @returns {JSX.Element}
 */

const Header = ({
  headerData,
  activeRegion,
  allRegions,
  localeCode,
  isUserSignedIn,
  wallet,
}: {
  headerData: Array<{ node: { logo?: string } }>;
  activeRegion: any;
  allRegions: any;
  localeCode: string;
  isUserSignedIn: boolean;
  wallet: walletInterface;
}): React.JSX.Element => {
  // #. Imported images
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
  const logoImage = `${imageBaseUrl}/icons/logo.png`;
  const cartLogo = `${imageBaseUrl}/icons/cart.svg`;

  // Logo
  const logo =
    headerData?.edges?.length > 1 ? headerData?.edges[1].node?.logo : logoImage;

  // #. Hamburger and region selector toggle
  const [state, setState] = React.useState({
    left: false,
    right: false,
  });
  const [isActive, setActive] = useState<boolean>(false);

  // #. Toggle drawer
  const toggleDrawer =
    (anchor: "left" | "right", open: boolean) =>
    (event: React.KeyboardEvent | React.MouseEvent) => {
      if (
        event &&
        event.type === "keydown" &&
        ((event as React.KeyboardEvent).key === "Tab" ||
          (event as React.KeyboardEvent).key === "Shift")
      ) {
        return;
      }
      setState({ left: false, right: false, [anchor]: open });
      setActive(open ? true : false);
    };

  return (
    <>
      <header
        className={`
                ${styles.wrapper}
            `}
      >
        <div className={`container ${styles.container}`}>
          <div className={styles["container__right-view"]}>
            <div
              className={styles["selected-country"]}
              onClick={toggleDrawer("left", true)}
            >
              <Image
                src={
                  activeRegion?.country?.circularStoreLogo ||
                  activeRegion?.country?.flagImage ||
                  preloadImage
                }
                width={32}
                height={32}
                alt="Flag"
              />
            </div>
          </div>

          <div className={styles["container__logo-container"]}>
            <Link href={`/`}>
              <img src={logo} alt="YOUGotaGift" />
            </Link>
          </div>

          <div className={styles["container__right-view"]}>
            <img src={cartLogo} alt="" className={styles["cart-logo"]} />
            <div
              className={styles["container__hamburger"]}
              onClick={toggleDrawer("right", true)}
            >
              {!isActive ? (
                <img src={hamburger} alt="image" />
              ) : (
                <img className="w-[24] h-[24]" src={close} alt="close" />
              )}
            </div>
            <SwipeableDrawer
              className={"drawer-custom header"}
              anchor={"left"}
              open={state["left"]}
              onClose={toggleDrawer("left", false)}
              onOpen={toggleDrawer("left", true)}
            >
              <RegionSelector
                regions={allRegions}
                activeRegion={activeRegion}
                onRegionChange={toggleDrawer}
              />
            </SwipeableDrawer>
            <SwipeableDrawer
              className={"drawer-custom header drawer-custom-hamburger"}
              anchor={"right"}
              open={state["right"]}
              onClose={toggleDrawer("right", false)}
              onOpen={toggleDrawer("right", true)}
            >
              <HamburgerMenu
                drawerChange={toggleDrawer}
                activeRegion={activeRegion}
                localeCode={localeCode}
                isUserSignedIn={isUserSignedIn}
                wallet={wallet}
              />
            </SwipeableDrawer>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
