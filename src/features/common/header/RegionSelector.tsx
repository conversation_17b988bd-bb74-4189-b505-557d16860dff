import Link from "next/link";
import React, { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "react";
import styles from "./Header.module.scss";
import Image from "next/image";
import { imageBaseUrl } from "@constants/envVariables";
import { useRouter } from "next/navigation";
import useApp from "../common.context";

const checked = `${imageBaseUrl}/icons/checked.svg`;

interface RegionSelectorInterface {
  regions: Array<any>;
  activeRegion: any;
  onRegionChange: (
    anchor: "left" | "right",
    open: boolean
  ) => MouseEventHandler<HTMLAnchorElement>;
}

/**
 * @method RegionSelector
 * @description Component to select region
 */
const RegionSelector = ({
  regions,
  activeRegion,
  onRegionChange,
}: RegionSelectorInterface): React.JSX.Element => {
  const router = useRouter();
  const { state } = useApp();

  /**
   * @method getStoreLinkHref
   * @param languages
   * @param country
   * @returns string
   */
  const getStoreLinkHref = (languages: any, country: any) => {
    let _locale = state?.localeCode;

    // #. Supported language count 2 means, only has support on English
    if (state?.localeCode === "ar" && languages?.edges?.length < 2) {
      _locale = String(country?.defaultLanguage?.code || "en").toLowerCase();
    }

    return `/${_locale}-${country?.code?.toLowerCase()}`;
  };
  return (
    <div className={styles["region-selector"]}>
      <div className={styles["selector"]}>
        {regions?.edges?.map(
          ({ node: { code, name, country, languages } }, index: number) => (
            <Link
              href={getStoreLinkHref(languages, country)}
              locale={false}
              legacyBehavior
              key={index}
            >
              <a
                onClick={onRegionChange("left", false)}
                className={styles["selector-item"]}
              >
                {country?.flagImage && (
                  <div className={styles["selector-item__item-left"]}>
                    <Image
                      src={country?.flagImage}
                      alt="flag"
                      width={25}
                      height={16.72}
                    />
                    {country?.name}
                  </div>
                )}
                {code === activeRegion?.code && (
                  <img
                    src={checked}
                    alt="checked"
                    className={styles["selected"]}
                  />
                )}
              </a>
            </Link>
          )
        )}
      </div>
    </div>
  );
};

export default RegionSelector;
