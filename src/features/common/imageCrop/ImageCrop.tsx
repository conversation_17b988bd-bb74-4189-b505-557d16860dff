import React, { useState, useRef } from "react";
import <PERSON>act<PERSON><PERSON>, { centerCrop, makeAspectCrop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import styles from "./ImageCrop.module.scss";
import { CircularProgress, Dialog, DialogContent, Slider } from "@mui/material";
import { useTranslation } from "next-i18next";
import { IMAGE_CROP } from "@constants/common";
import useApp, { AppContextAction } from "../common.context";
import Button from "../button/Button";
export const IMAGE_FORMAT = {
  WITHOUT_MESSAGE: "without_message",
  WITHOUT_LOGO: "without_logo",
  WITHOUT_MESSAGE_AND_LOGO: "without_message_and_logo",
  WITH_MESSAGE_AND_LOGO: "with_message_and_logo",
};

// Helper function for center aspect crop
function centerAspectCrop(mediaWidth, mediaHeight, aspect) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: "%",
        width: 100,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

const ImageCrop = (props: any) => {
  const {
    state: { pdfPreview },
    dispatch,
  } = useApp();

  const {
    imgSrc,
    language,
    handleGreetingsStates,
    info,
    aspectRatio,
    dimensionPreview,
    isCustomeGreetings,
    isPdfGreetings,
    customCard,
    type,
    handleFileChange,
    profilePicUpload,
    handleProfilePicUpload,
    handleClose,
    isPdfLogoUpload,
    isImageCropOpen,
  } = props; // Pass necessary props
  // translations
  const { t } = useTranslation("common");
  const previewCanvasRef = useRef(null);
  const imgRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [crop, setCrop] = useState<any>();
  const [completedCrop, setCompletedCrop] = useState<any>();
  const [currentImgWidth, setCurrentImageWidth] = useState();
  const [currentImgHeight, setCurrentImageHeight] = useState();
  const [previewUrl, setPreviewUrl] = useState<string | any>(null);
  const [scale, setScale] = useState(IMAGE_CROP.SLIDER_MAX_VALUE);

  const dispatchWorkPdfpreviewContext = (payload: any) => {
    dispatch({
      type: AppContextAction.PDF_PREVIEW,
      payload,
    });
  };

  const dispatchCustomeGreetingsContext = (payload: any) => {
    dispatch({
      type: AppContextAction.CUSTOME_GREETINGS,
      payload,
    });
  };

  // On image load, set crop
  function onImageLoad(e: any) {
    if (aspectRatio) {
      const { width, height } = e.currentTarget;
      setCurrentImageWidth(width);
      setCurrentImageHeight(height);
      setCrop(centerAspectCrop(width, height, aspectRatio));
    }
  }

  const handleSlider = (_: any, newValue: number | number[]) => {
    const size = Array.isArray(newValue) ? newValue[0] : newValue;
    setScale(size);
  };

  // Create a canvas for cropping the image
  const canvasPreview = (
    image,
    canvas,
    crop,
    scale = IMAGE_CROP.SLIDER_MAX_VALUE,
  ) => {
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    const ctx = canvas.getContext("2d");

    canvas.width = crop.width * scaleX;
    canvas.height = crop.height * scaleY;

    const cropX = crop.x * scaleX;
    const cropY = crop.y * scaleY;
    const centerX = image.naturalWidth / 2;
    const centerY = image.naturalHeight / 2;
    ctx.translate(-cropX, -cropY);

    // Move the origin to the center of the original position
    ctx.translate(centerX, centerY);
    // Scale the image
    ctx.scale(scale, scale);
    // Move the center of the image to the origin (0,0)
    ctx.translate(-centerX, -centerY);
    ctx.drawImage(
      image,
      0,
      0,
      image.naturalWidth,
      image.naturalHeight,
      0,
      0,
      image.naturalWidth,
      image.naturalHeight,
    );
  };

  // API call to upload cropped image
  const onImageClickApiCall = () => {
    if (!completedCrop || !imgRef.current || !previewCanvasRef.current) {
      return;
    }
    setLoading(true);
    const image = imgRef.current;
    const canvas: any = previewCanvasRef.current;

    // Draw the cropped area on the canvas
    canvasPreview(image, canvas, completedCrop, scale);

    //# Set the file name and type from info.file (upload)
    let name: any, fileType: any;
    ({ name, type: fileType } = info);

    // Convert canvas to blob and send to the server
    canvas.toBlob(async (blob: any) => {
      if (!blob) {
        console.error("Canvas is empty");
        return;
      }

      const file = new File([blob], name, { type: fileType as string });
      // Convert file to Blob and set it in the state
      const fileBlob: any = new Blob([file], { type: file.type });
      // Generate preview URL
      const url = URL.createObjectURL(blob);
      setPreviewUrl(url); // Store the preview URL in state
      if (customCard) {
        handleFileChange(file, type);
      } else if (isCustomeGreetings) {
        dispatchCustomeGreetingsContext({
          customeGreetings: {
            file: file,
            urlType: "GREETINGS_COVER_IMAGE",
            name: file?.name,
            previewUrl: url,
          },
        });
      } else if (isPdfGreetings && !isPdfLogoUpload) {
        dispatchWorkPdfpreviewContext({
          greetings: {
            file: file,
            urlType: "PDF_GREETINGS_COVER_IMAGE",
            name: file?.name,
            previewUrl: url,
          },
        });
      } else if (isPdfLogoUpload && isPdfGreetings) {
        dispatchWorkPdfpreviewContext({
          logo: {
            file: file,
            urlType: "SENDER_LOGO_IMAGE",
            name: file?.name,
            previewUrl: url,
          },
        });
      } else if (profilePicUpload) {
        handleProfilePicUpload(file, url);
      }
      const val = {
        imageCropOpen: false,
        greetingPreview: url,
        isGreetingUpload: true,
        greetingsBlob: file,
      };
      handleGreetingsStates(val);
    }, IMAGE_CROP.FILE_TYPE);
  };

  return (
    <>
      {isImageCropOpen && (
        <div className="image-crop-container">
          <Dialog
            open={isImageCropOpen}
            onClose={handleClose}
            className="image-crop-modal"
            PaperProps={{
              style: { margin: 8 },
            }}
          >
            <DialogContent>
              <div
                className={`${styles["image-crop"]} image-crop`}
                style={{ margin: "0 auto" }}
              >
                <div className={styles["image-crop__title"]}>
                  <div className={styles["top-wrapper"]}>
                    <span className={styles["title"]}>
                      {t("dragImageToAdjust")}
                    </span>
                  </div>
                </div>
                {!!imgSrc && (
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={aspectRatio}
                    style={{
                      width: `100%`,
                      borderRadius: "16px",
                      overflow: "hidden",
                    }}
                    circularCrop={profilePicUpload}
                    keepSelection
                    minHeight={IMAGE_CROP.MIN_CROP_HEIGHT as number}
                  >
                    <img
                      ref={imgRef}
                      alt="img-crop"
                      src={imgSrc}
                      onLoad={onImageLoad}
                      style={{
                        minWidth: `100%`,
                        transform: `scale(${scale})`,
                      }}
                    />
                  </ReactCrop>
                )}
                {/* Hidden canvas to perform cropping */}
                <canvas ref={previewCanvasRef} style={{ display: "none" }} />
                <div className={styles["slider"]}>
                  <span className={styles["slider__icon"]}>&minus;</span>
                  <Slider
                    value={scale}
                    onChange={handleSlider}
                    step={IMAGE_CROP.SLIDER_STEP_VALUE}
                    min={IMAGE_CROP.SLIDER_MIN_VALUE}
                    max={IMAGE_CROP.SLIDER_MAX_VALUE}
                    color="black"
                  />
                  <span className={styles["slider__icon"]}>+</span>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <div className="crop-button">
            <Button
              theme="dark-shadow"
              className={`${styles["cancel-btn"]}`}
              action={handleClose}
              attribues={{ disabled: loading }}
            >
              {t("cancel")}
            </Button>
            <Button
              theme="at-work-primary"
              className={`${styles["done-btn"]}`}
              action={onImageClickApiCall}
            >
              {loading ? <CircularProgress color="inherit" /> : t("done")}
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default ImageCrop;
