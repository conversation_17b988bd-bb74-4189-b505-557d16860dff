@import "@styles/variables";
@import "@styles/mixins";

.image-crop {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-height: 100vh;

  img {
    max-width: 100%;
    max-height: 40vh;
    border-radius: 16px;
    object-fit: cover;
    overflow: hidden;
  }
  &__title {
    display: flex;
    margin-bottom: 16px;
    color: #000;
    position: relative;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    .top-wrapper {
      display: flex;
      align-items: center;
      column-gap: 7px;
      flex-wrap: wrap;
    }
    .title {
      font-family: $bricolage-font-family;
      font-size: 24px;
      font-style: normal;
      font-weight: 800;
      line-height: 24px; /* 100% */
    }
    .dimension {
      font-size: 12px;
    }
  }
  .crop-controls {
    display: flex;
    align-items: center;
    gap: 32px;

    .cancel-btn {
      padding: 0;
      color: $dark-charcoal;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
    }

    .done-btn {
      height: 48px;
      padding-inline: 24px;
    }
  }

  .ReactCrop__crop-mask {
    rect {
      fill-opacity: 1;
    }
  }

  .slider {
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;

    &__icon {
      color: #000;
      font-family: Inter;
      font-size: 14px;
      font-weight: 700;
    }
  }
}
