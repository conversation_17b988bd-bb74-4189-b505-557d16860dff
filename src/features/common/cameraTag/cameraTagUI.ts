/*************************************************
 * NOTE: Customize third-party plug-in camera tag,
 * Exceptional case to use DOM handlers JS API's
 *************************************************/

/**
 * @method cameraTagUI
 * @description To update the Camera Tag UI by usind DOM API's
 */
const cameraTagUI = ({
  t,
  uploadIconUrl,
  recordIconUrl,
  onDialogClose,
  publishedIconUrl,
  saveIconUrl,
  recordAgainIconUrl,
  previewIconUrl,
}: any) => {
  /**
   * @method createStartScreenWrapper
   * @private
   */
  const createStartScreenWrapper = () => {
    const wrapperId = "ygag-camera__start-el-wrapper";

    // #. Create a new wrapper
    var wrapperEl = document.createElement("div");
    wrapperEl.id = wrapperId;

    return wrapperEl;
  };

  /**
   * @method getOrPlaceholder
   * @private
   */
  const getOrPlaceholder = () => {
    // #. Create a new wrapper
    var orEl = document.createElement("div");
    orEl.classList.add("ygag-camera__or-placeholder");
    orEl.textContent = t("or");

    return orEl;
  };

  /**
   * @method updateRecordElement
   * @private
   */
  const updateRecordElement = () => {
    const recordEl = document.querySelector(
      ".cameratag_primary_link.cameratag_record",
    );

    recordEl!.querySelector(".cameratag_prompt_label")!.textContent =
      t("recordFromCamera");

    // #. Remove default upload icon
    const recordIcon = recordEl!.querySelector(".cameratag_action_icon");
    recordIcon?.remove();
    // #. Get new upload icon and append it
    const newUploadicon = getRecordIcon();
    recordEl?.appendChild(newUploadicon);

    // const cameraIcon = recordEl!.querySelector('i.fa');
    // cameraIcon!.className = 'icon-video-camera';

    return recordEl;
  };

  /**
   * @method getUploadIcon
   * @private
   */
  const getUploadIcon = () => {
    // #. Create a new wrapper
    var uploadIcon = document.createElement("img");
    uploadIcon.src = uploadIconUrl;

    return uploadIcon;
  };

  /**
   * @method getRecordIcon
   * @private
   */
  const getRecordIcon = () => {
    // #. Create a new wrapper
    var recordIcon = document.createElement("img");
    recordIcon.src = recordIconUrl;

    return recordIcon;
  };

  /**
   * @method updateUploadElement
   * @private
   */
  const updateUploadElement = () => {
    const uploadEl = document.querySelector(
      ".cameratag_primary_link.cameratag_upload",
    );

    uploadEl!.querySelector(".cameratag_prompt_label")!.textContent =
      t("uploadAFile");

    // #. Remove default upload icon
    const uploadIcon = uploadEl!.querySelector(".cameratag_action_icon");
    uploadIcon?.remove();

    // #. Get new upload icon and append it
    const newUploadicon = getUploadIcon();
    uploadEl?.appendChild(newUploadicon);

    return uploadEl;
  };

  /**
   * @method getCancelUploadElement
   * @private
   */
  const getCancelUploadElement = () => {
    // #. Create a cancel button
    var cancelEl = document.createElement("a");
    cancelEl.classList.add("brand-video-editor-cancel");
    cancelEl.textContent = t("cancel");

    cancelEl.addEventListener("click", function () {
      onDialogClose();
    });

    return cancelEl;
  };

  /**
   * @method customAcceptUI
   * @private
   */
  const customAcceptUI = () => {
    // #. Get accept screen elements
    const publishEl = document.querySelector(".cameratag_publish");
    const playEl = document.querySelector(".cameratag_play");
    const recordEl = document.querySelector(
      ".cameratag_rerecord_btn.cameratag_record",
    );

    // #. Override elements with custom UI
    publishEl!.innerHTML = `<img src="${saveIconUrl}"  />  ${t("upload")}`;
    playEl!.innerHTML = `<img src="${previewIconUrl}"  />  ${t("preview")}`;
    recordEl!.innerHTML = `<img src="${recordAgainIconUrl}"  />  ${t(
      "recordAgain",
    )}`;
  };
  /**
   * @method uploadingPlaceholder
   * @public
   */
  const uploadingPlaceholder = () => {
    // #. Show uploading status placeholder
    const graphEl = document.querySelector(".radial-progress");
    graphEl!.innerHTML = `<div class="uploading_progress">
    <progress id="file" value="0" max="100"> 0 </progress>
    <div class="bottomCont"><p>${t("uploading")}</p><p>0%</p></div></div>`;
  };

  /**
   * @method  updateStartScreen
   * @public
   */
  const updateStartScreen = () => {
    // #. Get wrapper element
    const wrapperEl = createStartScreenWrapper();

    // #. Get updated child elements
    const recordEl = updateRecordElement();
    const uploadEl = updateUploadElement();
    const cancelEl = getCancelUploadElement();

    // #. Move the children to wrapper
    Array.prototype.forEach.call(
      [recordEl, uploadEl, cancelEl],
      function (c) {
        wrapperEl.appendChild(c);
      },
    );

    // #. Append the or place holder
    wrapperEl.appendChild(getOrPlaceholder());

    // #. Append the wrapper
    document
      .getElementById("ygag-camera_start_screen")
      ?.appendChild(wrapperEl);

    // #. Change direction style to RTL if the selected language is Arabic
    const bodyElement = document.querySelector("body");
    const dirAttributeValue = bodyElement!.getAttribute("dir");
    if (dirAttributeValue === "rtl") {
      const cameraTagEl = document.querySelector(".camera_tag");
      cameraTagEl?.classList.add("rtl");
    }
  };

  /**
   * @method  updateHardwareAccessErrorUI
   * @public
   */
  const updateHardwareAccessErrorUI = () => {
    // #. Get wrapper element
    const errorEl = document.querySelector(".cameratag_error");

    //#. Clear all children
    errorEl!.innerHTML = "";

    // #. Get accept element
    const acceptEl = document.querySelector(".cameratag_accept");

    //#. Hide the element
    acceptEl?.classList.add("hide_element");

    // #. Create main header
    const mainHeader = document.createElement("h3");
    mainHeader.textContent = t("hardwareAccessDenied");

    // #. Create error message
    const message = document.createElement("span");
    message.className = "error-message";
    message.textContent = t("updateBrowserPermission");

    // #. Create a close button
    const closeButton = document.createElement("a");
    closeButton.classList.add("cameratag_error__wrapper__close-button");
    closeButton.textContent = t("close");

    closeButton.addEventListener("click", function () {
      onDialogClose();
    });

    const wrapperEl = document.createElement("div");
    wrapperEl.className = "cameratag_error__wrapper";

    // #. Append the wrapper
    wrapperEl?.appendChild(mainHeader);
    wrapperEl?.appendChild(message);
    wrapperEl?.appendChild(closeButton);
    errorEl?.appendChild(wrapperEl);
  };

  /**
   * @method  updateAcceptScreen
   * @public
   */
  const updateAcceptScreen = () => {
    // #. Show accept screen
    const acceptEl = document.querySelector(".cameratag_accept");
    acceptEl?.classList.remove("hide_element");

    // #. Show border for accept screen
    acceptEl?.classList.add("accept_border");

    // #. Customized Accept screen
    customAcceptUI();
  };

  /**
   * @method  updatePublishedScreen
   * @public
   */
  const updatePublishedScreen = () => {
    // #. Get screen elements
    const wrapperDiv = document.querySelector(".cameratag_thumb_bg");
    const checkEl = document.querySelector(".cameratag_checkmark");

    // #. Override text content of the check icon element
    checkEl!.textContent = `${t("uploaded")}`;

    // #. create image element for Published Icon
    var publishedIcon = document.createElement("img");
    publishedIcon.src = publishedIconUrl;

    // #.append img element to parent
    wrapperDiv!.appendChild(publishedIcon);
  };

  /**
   * @method  updatePreviewScreen
   * @public
   */
  const updatePreviewScreen = () => {
    // #. Get elements and hide accept screen
    const acceptEl = document.querySelector(".cameratag_accept");
    acceptEl?.classList.add("hide_element");
  };

  /**
   * @method  updatePreviewStoppedScreen
   * @public
   */
  const updatePreviewStoppedScreen = () => {
    // #. Show accept screen and add dark overlay
    const acceptEl = document.querySelector(".cameratag_accept");
    acceptEl?.classList.remove("hide_element");
    acceptEl?.classList.add("dark_overlay");

    // #. Show customized Accept screen
    customAcceptUI();
  };

  /**
   * @method  updateUploadingScreen
   * @public
   */
  const updateUploadingScreen = () => {
    // #. Remove dark overlay of the Accept screen
    const acceptEl = document.querySelector(".cameratag_accept");
    acceptEl?.classList.remove("dark_overlay");

    // #. Show border
    acceptEl?.classList.add("accept_border");

    // #. Get accept screen elements
    const publishEl = document.querySelector(".cameratag_publish");
    const playEl = document.querySelector(".cameratag_play");
    const recordEl = document.querySelector(
      ".cameratag_rerecord_btn.cameratag_record",
    );
    // #. Remove content from the elements
    publishEl!.innerHTML = "";
    playEl!.innerHTML = "";
    recordEl!.innerHTML = "";
  };

  /**
   * @method  updateCountdownScreen
   * @public
   */
  const updateCountdownScreen = () => {
    // #. Hide video accept screen
    const acceptEl = document.querySelector(".cameratag_accept");
    acceptEl?.classList.add("hide_element");
  };

  /**
   * @method  updateProgressScreen
   * @public
   */
  const updateProgressScreen = (percentageVal: any) => {
    // #. Convert to percentage with two decimal places
    const percentage = (percentageVal * 100).toFixed(0);

    // #. Get progress element
    const graphEl = document.querySelector(".radial-progress");

    // #. Override the element with custom progress bar
    graphEl!.innerHTML = `<div class="uploading_progress"><progress id="file" value=${percentage} max="100"> ${percentage} </progress>
        <div class="bottomCont"><p>${t(
          "uploading",
        )}</p><p>${percentage}%</p></div></div>`;
  };

  return {
    updateStartScreen,
    updateHardwareAccessErrorUI,
    updateAcceptScreen,
    updatePublishedScreen,
    updatePreviewScreen,
    updatePreviewStoppedScreen,
    updateUploadingScreen,
    updateCountdownScreen,
    updateProgressScreen,
    uploadingPlaceholder,
  };
};

export default cameraTagUI;
