import { imageBaseUrl } from "@constants/envVariables";
import { PhotoEditorSDKUI, ImageFormat, ExportFormat } from "photoeditorsdk";
import { useEffect } from "react";

export interface PhotoEditorSDKInterface {
  className: string;
  imagePath: string;
  lkey: string;
  onEditorInitialized: (param: any) => void;
  fileType?: any;
}

/**
 * @method PhotoEditorSDK
 * @description photo editor SDK
 * @reference https://img.ly/docs/pesdk/web/guides/react-js/
 * @returns
 */
const PhotoEditorSDK = ({
  className,
  imagePath,
  onEditorInitialized,
  lkey,
  fileType,
}: PhotoEditorSDKInterface): JSX.Element => {
  useEffect(() => {
    imagePath && initEditor();
  }, [imagePath]);

  /**
   * @method initEditor
   */
  const initEditor = async () => {
    const editor = await PhotoEditorSDKUI.init({
      container: "#photo-editor",
      image: imagePath || new Image(),
      assetBaseUrl: `${imageBaseUrl}/image-ly/assets/`,
      license: lkey,
      layout: "basic",
      export: {
        image: {
          enableDownload: false,
          format: fileType,
          exportType: ExportFormat.DATA_URL,
        },
      },
    });

    // #. Pass the editor instance to root component
    onEditorInitialized && onEditorInitialized(editor);
  };

  return <div id="photo-editor" className={className}></div>;
};

export default PhotoEditorSDK;
