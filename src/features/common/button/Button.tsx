"use client";
import React, { JSX, useEffect, useState } from "react";
import styles from "./Button.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import { ButtonInterface } from "@interfaces/common.inteface";
import Chatbot from "../chatbot/Chatbot";
/**
 * @method Button
 * @description Button component
 * @returns {JSX.Element}
 */

const Button = ({
  theme,
  className,
  action,
  children,
  borderTheme,
  arrow,
  id,
  icon = false,
  attribues = {},
  wrapperClassName,
  btnRef,
  hasCornerIcon,
  disabled = false,
  isChatBot = false,
  isFixedBottom = false,
}: ButtonInterface): JSX.Element => {
  const [isMounted, setIsMounted] = useState(false);

  // Set the mounted state to true after the component has mounted
  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timeout);
  }, []);

  const buttonClasses = [
    styles.button,
    styles[`button--${theme}`],
    borderTheme && styles.border,
    borderTheme && styles[`border--${borderTheme}`],
    arrow && styles["arrow-enabled"],
    hasCornerIcon && `has-corner-icon-bg ${styles["has-corner-icon"]}`,
    className,
    isChatBot && styles["btn-chat-bot"],
  ]
    .filter(Boolean)
    .join(" ");

  const wrapperClasses = [
    isFixedBottom && styles["fixed-bottom"],
    isChatBot && styles["wrap-chat-bot"],
    wrapperClassName,
  ]
    .filter(Boolean)
    .join(" ");

  const commonButton = (
    <button
      className={buttonClasses}
      onClick={action}
      disabled={disabled}
      id={id}
      {...attribues}
      data-testid="themedButton"
      ref={btnRef}
    >
      {icon && (
        <span className={`${styles["icon-ai"]}`}>
          <img src={`${imageBaseUrl}/icons/ai-icon.svg`} alt="img" />
        </span>
      )}
      {children && (
        <span className={`${styles["button-label"]} button-label`}>
          {children}
        </span>
      )}
      {arrow === "arrow-forward" && (
        <span
          className={`${styles[theme + "-icon-wrapper"]} ${
            styles["icon-wrapper"]
          } button-icon-wrapper`}
        >
          <i className={`icon-${arrow}`}></i>
        </span>
      )}
    </button>
  );

  return (
    <>
      {isChatBot ? (
        <div className={wrapperClasses}>
          {commonButton}
          <Chatbot />
        </div>
      ) : (
        commonButton
      )}
    </>
  );
};

export default Button;
