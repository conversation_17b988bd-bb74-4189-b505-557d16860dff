@import "@styles/variables";
@import "@styles/mixins";

%icon-wrapper {
  width: 40px;
  height: 40px;
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: transform ease-in-out 400ms;
}

.button {
  padding: 4px 4px 4px 10px;
  cursor: pointer;
  border: 0;
  border-radius: 8px;
  width: 100%;
  transform: translate(0, 0);

  &:disabled {
    box-shadow: none;
    pointer-events: none;
    opacity: 0.3;
  }

  &--primary,
  &--at-work-primary {
    background: $dark-charcoal;
    color: $white;
    animation: returnToOriginal 0.3s ease forwards;

    &.hover-enabled {
      &:hover {
        animation: bounceUp 0.2s ease forwards;
      }
    }
  }

  &--secondary {
    background: $white;
    color: $dark-charcoal;
    border: 2px solid $dark-charcoal;
    transition:
      background 0.5s ease,
      color 0.5s ease;
    &.hover-enabled {
      &:hover {
        background: $dark-charcoal;
        color: $white;
      }
    }
  }

  &--light-shadow {
    background: $white;
    color: $dark-charcoal;
    border: 2px solid $barney-purple;
    box-shadow: 0 2px 0px $barney-purple;
  }

  &--dark-shadow {
    background: $white;
    color: $dark-purple;
    border: 2px solid $dark-purple;
    box-shadow: 0 2px 0px $dark-purple;
  }

  &--light-border {
    background: $white;
    color: $dark-charcoal;
    border: 2px solid $barney-purple;
  }

  &--semi-dark-grey {
    background: $semi-dark-grey;
    color: $black;
    border: 2px solid $semi-dark-grey;
  }

  &--transparent {
    background: transparent;
    color: $barney-purple;
  }

  .icon-wrapper {
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
  }

  &:hover {
    .icon-wrapper {
      transform: translateX(4px);
      transition: all 0.4s ease;
    }
  }

  .large {
    padding: 0;
  }

  /* At work customisation */
  &--at-work-primary {
    height: 50px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 10px;
    background: $dark-charcoal;
    color: #fff;
    transition: background-color 0.3s ease;
    // width: fit-content;
    padding-inline: 15px;
    // &:hover {
    //   background: #0e0f0cd2;
    // }
  }

  &--at-work-primary-outlined {
    height: 50px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 10px;
    background: #fff;
    color: $dodger-blue;
    border: 2px solid $dark-charcoal;
    background: #fff;
    // width: fit-content;
    transition: background-color 0.3s ease;
    padding-inline: 15px;
  }

  &--at-work-secondary {
    border-radius: 10px;
    height: 50px;
    font-size: 16px;
    font-weight: 700;
    color: #0e0f0c;
    background: #f5f5f5;
    // width: fit-content;
    padding-inline: 15px;
  }

  &--dark-shadow {
    border-radius: 10px;
    font-size: 16px;
    font-weight: 700;
    // width: fit-content;
    padding-inline: 15px;
    height: 50px;
    background: $white;
    color: $dark-purple;
    border: 2px solid $dark-purple;
    box-shadow: 0 2px 0px $dark-purple;
  }

  &--at-work-secondary-outlined {
    border-radius: 10px;
    height: 50px;
    font-size: 16px;
    font-weight: 700;
    color: #0e0f0c;
    background: #f5f5f5;
    border: 2px solid #0e0f0c;
    // width: fit-content;
    padding-inline: 15px;
  }

  &.has-corner-icon {
    background-color: $white;

    &::after {
      content: "";
      display: inline-block;
      position: absolute;
      inset-inline-end: -1px;
      top: -1px;
      width: 21.051px;
      height: 21.051px;
      background-color: #000;
      border-radius: 0px 8px;

      @include rtl-styles {
        border-radius: 8px 0px;
      }
    }
  }
}

.arrow-enabled {
  display: flex;
  align-items: center;
  gap: 22px;

  .button-label {
    // padding: 5px 25px 5px 52px;
    display: inline-block;
  }
}

.border {
  border: 1px solid transparent;

  &--black {
    border-color: $dark-charcoal;
  }

  &--purple {
    border-color: $barney-purple;
  }

  &--orange {
    border-color: $error-red;
  }

  &--black {
    border-color: $black-header;
  }
}

.white {
  &-icon-wrapper {
    @extend %icon-wrapper;

    background-color: rgba($cornflower-blue, 10%);
  }
}

.button-icon-wrapper {
  display: flex;
}

.wrap-chat-bot {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  z-index: 10000;
  width: calc(100% - 40px);
  background: #fff;
  padding-top: 0;
  padding-bottom: 15px;
}

.btn-chat-bot {
  width: calc(100% - 72px);
}

// Define animations for each direction
@keyframes bounceUp {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(1px, -1px);
  }
  50% {
    transform: translate(2px, -2px);
  }
  75% {
    transform: translate(3px, -3px);
  }
  100% {
    transform: translate(4px, -4px);
  }
}

@keyframes returnToOriginal {
  0% {
    transform: translate(4px, -4px);
  }
  100% {
    transform: translate(0, 0);
  }
}
