@import "@styles/variables";
@import "@styles/mixins";

.bind-form-element {
  @include font-size(16);

  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  background: $white;
  border-radius: 8px;
  padding: 13px 16px;
  align-items: center;
  height: 50px;
  max-width: 352px;
  border: 1px solid rgba(14, 15, 12, 0.12);

  &__icon-name {
    width: 24px;
    height: 24px;
  }

  &__container {
    display: flex;
    align-items: center;
    gap: 8px;

    &--start {
      align-items: flex-start;
    }
  }

  &__brand-img {
    width: 74px;
    height: 48px;
    border-radius: 4px;
  }

  &__denomination {
    width: 100%;
  }

  &__currency {
    font-size: 10px;
    font-weight: 700;
    color: $dark-purple;
  }

  &__icon-mail {
    height: 24px;
    width: 24px;
  }

  &__icon-sms {
    height: 24px;
    width: 24px;
  }

  &__element-bar {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  &__input {
    flex-grow: 1;
    padding-inline-start: 0;
    fieldset {
      border: 0 !important;
    }
  }

  &__select {
    display: flex;
    align-items: flex-end;

    [role="button"] {
      margin: 17px 0 0;
      padding: 4px 0;
      position: relative;

      @include rtl-styles {
        padding-right: 0;
        padding-left: 24px;
      }

      &::after {
        content: "|";
        position: absolute;
        right: 6px;
        font-weight: $thin;
        @include font-size(16);

        @include rtl-styles {
          right: auto;
          left: 8px;
        }
      }
    }

    svg {
      top: calc(50% + 1px);
      right: 10px;

      @include rtl-styles {
        right: auto;
        left: 11px;
      }
    }
  }

  &-phone-field {
    @include rtl-styles {
      padding-left: 0px;
    }
  }
}

.bind-form-element-mobile {
  display: flex;
  align-items: center;
  width: 100%;

  @include font-size(16);
}
