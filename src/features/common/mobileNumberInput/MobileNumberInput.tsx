import { useTranslation } from "next-i18next";
import React, { useEffect, useRef, useState } from "react";
import useApp from "../common.context";
import isValidPhoneNumber from "@utils/isValidPhoneNumber";
import { imageBaseUrl } from "@constants/envVariables";
import { FormControl, TextField } from "@mui/material";
import styles from "./MobileNumberInput.module.scss";
import { getCountryCallingCode } from "react-phone-number-input";
import { BRAND_FORM_STATE } from "@constants/common";
import isNumber from "@utils/isNumber";
import CountryPhoneCode from "../countryPhoneCode/CountryPhoneCode";

interface PropsInterface {
  mobile: string;
  dialCode: string;
  countryCode: string;
  fieldRequired: boolean;
  checkField: boolean;
  setcheckField: (val) => void;
  onInputError: (val) => void;
  onInputChange: (e: any) => void;
  setDialCode: (countryCode, dialCode: any) => void;
  validatePhoneNumber: string;
  setValidatePhoneNumber: (val) => void;
  setRecipient: (e: any) => void;
  isRecipient?: boolean;
}

const MobileNumberInput = ({
  mobile,
  fieldRequired,
  checkField,
  setcheckField,
  onInputError,
  onInputChange,
  setDialCode,
  dialCode,
  countryCode,
  validatePhoneNumber,
  setValidatePhoneNumber,
  setRecipient,
  isRecipient = true,
}: PropsInterface) => {
  // const [validatePhoneNumber, setValidatePhoneNumber] = useState("");
  const [phoneNumberVal, setPhoneNumberVal] = useState<any>("");
  const [dialCodeVal, setDialCodeVal] = useState<any>("");
  const { t } = useTranslation("common");

  const phoneNumberRef = useRef<HTMLInputElement>(null);

  const {
    state: { card, blacklistedCountries, activeRegion, localeCode },
  } = useApp();

  // #. set mobile number validate message
  const invalidPhoneNumberMsg = t("invalidPhoneNumber");

  //set default Country Code
  const defaultCountryCode: any = countryCode
    ? countryCode
    : activeRegion?.node?.country?.code;

  /**
   * @method onPhonecodeChanged
   * @param phoneCode
   */
  const onPhonecodeChanged = ({ countryCode, dialCode }: any) => {
    setRecipient((prev) => ({ ...prev, recipientMobile: "" }));
    setPhoneNumberVal("");
    setValidatePhoneNumber("");
    setDialCodeVal(dialCode);
    setDialCode(countryCode, dialCode);
  };

  /**
   * @method onPhoneNumberChanged
   * @param event
   */
  const onPhoneNumberChanged = (event: any) => {
    const inputNumber = event?.target?.value;
    setPhoneNumberVal(event.target.value);
    const isValid = inputNumber
      ? isValidPhoneNumber(event.target.value, dialCode || dialCodeVal)
      : true;

    // Show validation message during onChange
    let msg = "";
    if (fieldRequired && !inputNumber) {
      onInputError({ [event.target.name]: true });
      setValidatePhoneNumber("Mobile number is required");
      return;
    }

    if (!isValid) {
      onInputError({ [event.target.name]: true });
      msg = invalidPhoneNumberMsg;
      setValidatePhoneNumber(msg);
      return;
    }
    onInputError({ [event.target.name]: false });
    setValidatePhoneNumber(msg);
  };

  useEffect(() => {
    if (defaultCountryCode) {
      const countryDialCode = `+${getCountryCallingCode(defaultCountryCode)}`;
      setPhoneNumberVal(
        card?.sendToPerson?.phoneNumber
          ? card?.sendToPerson?.phoneNumber
          : null,
      );
      setDialCode(countryCode, countryDialCode);
      setDialCodeVal(countryDialCode);
    }
  }, [defaultCountryCode]);

  useEffect(() => {
    if (checkField && fieldRequired && !mobile) {
      onInputError({ mobile: true });
      setValidatePhoneNumber("Mobile number is required");
      setcheckField(false);
    }
  }, [checkField]);

  const handleChange = (e) => {
    onInputChange(e);
    onPhoneNumberChanged(e);
  };

  const getIcon = () => {
    return isRecipient
      ? `${imageBaseUrl}/icons/chat-icon.svg`
      : `${imageBaseUrl}/icons/phonenumber.svg`;
  };

  return (
    <div className="add-recipient brand-send-to recipient-phone-field">
      <FormControl variant="standard" fullWidth={true}>
        <div
          className={`add-recipient-inner-wrapper ${validatePhoneNumber ? "form-element-error-border" : ""} ${styles["bind-form-element"]} ${styles["bind-form-element-phone-field"]}`}
        >
          <img
            src={getIcon()}
            className={styles["bind-form-element__icon-sms"]}
            alt="sms-icon"
            width={24}
            height={24}
          />
          <div className={styles["bind-form-element__element-bar"]}>
            <div
              className={`${styles["bind-form-element-mobile"]}  phone-code-wrapper`}
            >
              <CountryPhoneCode
                setCountryCallback={onPhonecodeChanged}
                showCountryName={true}
                showCountryNameInSelection={false}
                enableSearch={true}
                defaultValue={defaultCountryCode}
                dialCode={dialCode ? dialCode : dialCodeVal}
                phoneNumberRef={phoneNumberRef}
                language={localeCode}
                blacklistedCountries={blacklistedCountries}
                prefill={
                  card?.formState === BRAND_FORM_STATE.EDIT ? true : false
                }
              />
              <TextField
                data-testid="phoneField"
                id="mobile"
                type="text"
                variant="outlined"
                name="recipientMobile"
                placeholder={
                  isRecipient
                    ? t("receiverMobileNumber")
                    : t("enterMobNumber")
                }
                inputRef={phoneNumberRef}
                className={`brand-send-phone-text ${styles["bind-form-element__input"]}`}
                onChange={handleChange}
                onBlur={onPhoneNumberChanged}
                inputProps={{ maxLength: 12 }}
                onPaste={(event: any) => {
                  if (event.clipboardData.getData("Text").match(/[^\d]/)) {
                    event.preventDefault();
                  }
                }}
                onKeyPress={(event: any) => {
                  if (!isNumber(event)) {
                    event.preventDefault();
                  }
                }}
                value={mobile}
                helperText={validatePhoneNumber}
              />
            </div>
          </div>
        </div>
      </FormControl>
    </div>
  );
};

export default MobileNumberInput;
