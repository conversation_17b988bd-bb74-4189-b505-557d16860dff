@import "@styles/variables";
@import "@styles/mixins";

.f-bricolage {
  font-family: var(--font-bricolage);
}

.profile-header {
  width: 100%;

  &__wrapper {
    position: relative;
    &::after,
    &::before {
      content: "";
      height: 24px;
      width: 24px;
      position: absolute;
      left: 0;
      bottom: -24px;
      border-top-left-radius: 50px;
      box-shadow: -5px -6px 0 2px #99c6ff;
      background: rgba(0, 0, 0, 0);
    }

    &::before {
      left: auto;
      right: 0;
      box-shadow: 5px -6px 0 2px #99c6ff;
      border-top-left-radius: 0px;
      border-top-right-radius: 50px;
    }
  }

  &__back-button {
    @include rtl-styles {
      transform: rotate(180deg);
      left: auto;
      right: 20px;
    }
  }
}

.work-profile-details-fields {
  &__marquee {
    position: absolute;
    font-size: 16px;
    font-weight: 700;
    color: #ccc !important;
    width: calc(100% - 50px);
    left: 50px;
    top: 15px;

    @include rtl-styles {
      right: 50px;
      left: auto;
    }

    &--tooltip {
      width: calc(100% - 90px);
    }

    &--phone {
      width: calc(100% - 100px);
      left: 100px;
      @include rtl-styles {
        right: 100px;
        left: auto;
      }
    }
  }

  &__title{
    &--work{
      font-family: var(--font-bricolage);
    }
  }

  &__marquee-container {
    direction: ltr !important;
  }

  &__dial-code {
    img {
      width: 20px;
      height: 20px;
    }

    p {
      @include rtl-styles {
        margin-left: 4px;
        margin-right: 8px;
      }
    }

    p + img {
      width: 16px;
      height: 16px;
    }
  }

  &__info {
    display: flex;
    gap: 8px;
    align-items: center;
    border-radius: 16px;

    p {
      line-height: 18px;
      letter-spacing: -0.12px;
      font-size: 12px;
    }

    img {
      width: 24px;
      height: 24px;
    }
  }
}
