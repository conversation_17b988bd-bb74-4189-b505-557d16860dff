"use client";

import { imageBaseUrl } from "@constants/envVariables";
import React from "react";
import styles from "./Profile.module.scss";
import Image from "next/image";
import { useTranslation } from "next-i18next";
import { FormControl, InputAdornment, TextField } from "@mui/material";
import Marquee from "react-fast-marquee";
import { getCountries } from "react-phone-number-input";
import { getCountryCallingCode } from "react-phone-number-input/input";
import useApp from "@features/common/common.context";
import { DEFAULT_PHONE_CODE } from "@constants/common";
import { useRouter } from "next/navigation";
import en from "react-phone-number-input/locale/en.json";

const Profile = () => {
  const { t } = useTranslation("common");
  const {
    state: { activeRegion, userAttributes, localeCode },
  } = useApp();

  const router = useRouter();

  const defaultDialCode = () => {
    const defaultCountryCode: any = activeRegion?.node?.country?.code;
    return defaultCountryCode
      ? `+${getCountryCallingCode(defaultCountryCode)}`
      : DEFAULT_PHONE_CODE;
  };

  let dialCode = userAttributes?.mdc || defaultDialCode();
  const defaultCountryCode: any = activeRegion?.node?.country?.code;

  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
  const companyIcon = `${imageBaseUrl}/icons/company.svg`;
  const designationIcon = `${imageBaseUrl}/icons/designation.svg`;
  const departmentIcon = `${imageBaseUrl}/icons/department.svg`;
  const vatNoIcon = `${imageBaseUrl}/icons/vat-icon.svg`;
  const countryIcon = `${imageBaseUrl}/icons/country.svg`;
  const employeesIcon = `${imageBaseUrl}/icons/employees.svg`;
  const industryIcon = `${imageBaseUrl}/icons/industry.svg`;
  const arrowDown = `${imageBaseUrl}/icons/arrow-down-filled.svg`;
  const phonenumber = `${imageBaseUrl}/icons/phonenumber.svg`;
  const information = `${imageBaseUrl}/icons/information.svg`;

  const countryCode = userAttributes?.countryCode || defaultCountryCode;
  const countryName =
    countryCode && en.hasOwnProperty(countryCode)
      ? en[countryCode as keyof typeof en]
      : "";

  const userData = [
    {
      key: "organization",
      value: userAttributes?.organization,
      showTooltip: true,
      icon: companyIcon,
    },
    {
      key: "phoneNumber",
      dialCode: dialCode,
      value: userAttributes?.phoneNumber,
      showTooltip: false,
      icon: phonenumber,
    },
    {
      key: "designation",
      value: userAttributes?.designation,
      showTooltip: false,
      icon: designationIcon,
    },
    {
      key: "department",
      value: userAttributes?.department,
      showTooltip: false,
      icon: departmentIcon,
    },
    {
      key: "countryCode",
      value: countryName,
      showTooltip: false,
      icon: countryIcon,
    },
    {
      key: "vatNumber",
      value: userAttributes?.vatNumber,
      showTooltip: false,
      icon: vatNoIcon,
    },
    {
      key: "organizationTier",
      value: userAttributes?.organizationTier,
      showTooltip: false,
      icon: employeesIcon,
    },
    {
      key: "industry",
      value: userAttributes?.industry,
      showTooltip: false,
      icon: industryIcon,
    },
  ];

  const handleBackNavigation = () => {
    router.push("/");
  };

  return (
    <>
      <div className={`text-center ${styles["profile"]}`}>
        <div
          className={`${styles["profile-header__wrapper"]} py-[24px] bg-[#99C6FF] mt-[-24px] ml-[-20px] mr-[-20px]`}
        >
          {/* back button */}
          <div
            className={`w-[32px] h-[32px] cursor-pointer absolute left-[20px] top-[25px] ${styles["profile-header__back-button"]}`}
          >
            <img
              onClick={handleBackNavigation}
              src={`${imageBaseUrl}/icons/arrow-circle-left.svg`}
              alt="back arrow"
            />
          </div>

          {/* profile details */}
          <div>
            <div className="rounded-full h-[64px] w-[64px] aspect-ratio-[64/64] overflow-hidden mx-auto mb-[8px]">
              <Image
                blurDataURL={preloadImage}
                placeholder="blur"
                src={userAttributes?.picture || preloadImage}
                alt={"Card Image"}
                priority
                width={0}
                height={0}
                sizes="auto"
                className="h-[100%] w-[100%] overflow-hidden object-cover rounded-full"
              />
            </div>
            <p
              className={`${styles["f-bricolage"]} text-[24px] font-[800] line-height-[24px]`}
            >
              {userAttributes?.name}
            </p>
            <p className={`text-[14px] font-[700]`}>
              {userAttributes?.email}
            </p>
          </div>
        </div>
      </div>

      {/* atwork details */}
      <div className="mt-[24px]">
        <p className={`text-[24px] font-[800] mb-[24px] ${styles["work-profile-details-fields__title"]}`}>
          <span>@</span>
          <span className={`${styles["work-profile-details-fields__title--work"]}`}>{t("workDetails")}</span>
        </p>

        <div
          className={`bg-[#F5F5F5] px-[16px] py-[8px] mb-[24px] ${styles["work-profile-details-fields__info"]}`}
        >
          <img src={information} alt="info" />
          <p className="font-[500] text-[#0E0F0C]">
            {t("useDesktopToMakeChanges")}
          </p>
        </div>

        {userData?.map((item, index) => (
          <div
            key={index}
            className={`w-full mb-[16px] work-profile-details-fields`}
          >
            <FormControl fullWidth>
              <TextField
                placeholder={item?.key}
                required
                value={item?.value?.length > 35 ? "" : item?.value || ""}
                type="text"
                variant="outlined"
                disabled={true}
                inputProps={{
                  style: {
                    color: "#ccc",
                    cursor: "not-allowed",
                    width: item?.dialCode && "auto",
                    paddingLeft: "0",
                    paddingRight: "0",
                  },
                  readOnly: true,
                }}
                InputProps={{
                  startAdornment: item?.dialCode ? (
                    <InputAdornment
                      position={`${localeCode === "ar" ? "end" : "start"}`}
                      className={`${styles["work-profile-details-fields__dial-code"]}`}
                    >
                      <img
                        src={item?.icon}
                        width={24}
                        height={24}
                        alt="company"
                      />
                      <p
                        className={`text-[#CCC] text-[14px] ml-[8px] mr-[4px] font-[700]`}
                      >
                        {item?.dialCode}
                      </p>
                      <img src={arrowDown} width={24} alt="company" />
                    </InputAdornment>
                  ) : (
                    <InputAdornment
                      position={`${localeCode === "ar" ? "end" : "start"}`}
                    >
                      <img
                        src={item?.icon}
                        width={24}
                        height={24}
                        alt="company"
                        className="w-[24px] h-[24px]"
                      />
                    </InputAdornment>
                  ),
                }}
              ></TextField>
              {item?.value?.length > 35 && (
                <div
                  className={`${styles["work-profile-details-fields__marquee"]} ${item?.dialCode ? styles["work-profile-details-fields__marquee--phone"] : item.showTooltip ? styles["work-profile-details-fields__marquee--tooltip"] : ""}`}
                >
                  <Marquee
                    className={`${styles["work-profile-details-fields__marquee-container"]}`}
                    direction={localeCode === "ar" ? "right" : "left"}
                  >
                    {item?.value}
                    <span style={{ margin: "0 8px" }}></span>
                  </Marquee>
                </div>
              )}
            </FormControl>
          </div>
        ))}
      </div>
    </>
  );
};

export default Profile;
