import { BRAND_CARD_TYPE, COMPONENT_ID } from "@constants/common";
import useApp from "@features/common/common.context";
import { useTranslation } from "next-i18next";
import GiftTypeSelection from "./animateRoute/giftTypeSelection/GiftTypeSelection";
import BrandList from "./animateRoute/brandList/BrandList";
import Landing from "./animateRoute/landing/Landing";
import AccountConfirmation from "./animateRoute/login/accountConfirmation/AccountConfirmation";
import AccountLogin from "./animateRoute/login/accountLogin/AccountLogin";
import CreateAccount from "./animateRoute/login/createAccount/CreateAccount";
import VisitWebOrApp from "./animateRoute/login/visitWebOrApp/VisitWebOrApp";
import About from "./about/About";
import HappyYouOffers from "./happyYou/offers/Offers";
import HappyYouTerms from "./happyYou/termsConditions/TermsConditions";
import About<PERSON>appyYou from "./happyYou/about/about";
import BrandsIncluded from "./happyYou/brandsIncluded/BrandsIncluded";
import DenominationSelection from "./animateRoute/denominationSelection/DenominationSelection";
import BrandGreetings from "./personalisation/brandGreetings/BrandGreetings";
import BrandPersonalize from "./personalisation/brandPersonalize/BrandPersonalize";
import BrandUserMessage from "./personalisation/brandUserMessage/BrandUserMessage";

// Component list with order for the chat animations
export const useFirstTimeUserComponentList = (componentId: string) => {
  const { t } = useTranslation("common");
  const { state } = useApp();
  const chatbotName =
    state?.atWorkSiteConfig?.chatbotDetails?.chatbotName || "";
  const staticComponents = [
    {
      id: COMPONENT_ID.LANDING,
      component: Landing,
      conversation: t("landingConversationMessage", {
        name: chatbotName,
      }),
      next: state?.isUserSignedIn
        ? COMPONENT_ID.VISIT_WEB_OR_APP
        : COMPONENT_ID.GIFT_TYPE,
      prev: null,
    },
    // {
    //   id: COMPONENT_ID.LOGIN_INTRO,
    //   conversation: t("haveAnExistingAccount"),
    //   component: AccountConfirmation,
    //   next: (comp: string) => comp,
    //   prev: COMPONENT_ID.LANDING,
    // },
    {
      id: COMPONENT_ID.CREATE_ACCOUNT,
      conversation: t("provideDetails"),
      component: CreateAccount,
      prev: COMPONENT_ID.LOGIN,
      next: null,
    },
    {
      id: COMPONENT_ID.GIFT_TYPE,
      component: GiftTypeSelection,
      conversation: t("selectGiftType"),
      next: COMPONENT_ID.BRAND_LIST,
      prev: state?.isUserSignedIn
        ? COMPONENT_ID.LANDING
        : COMPONENT_ID.VISIT_WEB_OR_APP,
    },
    {
      id: COMPONENT_ID.BRAND_LIST,
      component: BrandList,
      conversation: t("happyYoumsg"),
      next: COMPONENT_ID.BRAND_DENOMINATION,
      prev: COMPONENT_ID.GIFT_TYPE,
    },
    {
      id: COMPONENT_ID.BRAND_DENOMINATION,
      component: DenominationSelection,
      conversation: t("pleaseSelectAmountAndDeliveryFormat"),
      next: COMPONENT_ID.PERSONALIZE_GREETINGS,
      prev: COMPONENT_ID.BRAND_LIST,
    },
    {
      id: COMPONENT_ID.PERSONALIZE_GREETINGS,
      component: BrandGreetings,
      isAnimated: false,
      // conversation: t("pleaseSelectAmountAndDeliveryFormat"),
      next: COMPONENT_ID.PERSONALIZE_MEDIA,
      prev: COMPONENT_ID.BRAND_DENOMINATION,
    },
    {
      id: COMPONENT_ID.PERSONALIZE_MEDIA,
      component: BrandPersonalize,
      isAnimated: false,
      // conversation: t("pleaseSelectAmountAndDeliveryFormat"),
      next: COMPONENT_ID.PERSONALIZE_MESSAGE,
      prev: COMPONENT_ID.PERSONALIZE_GREETINGS,
    },
    {
      id: COMPONENT_ID.PERSONALIZE_MESSAGE,
      component: BrandUserMessage,
      isAnimated: false,
      // conversation: t("pleaseSelectAmountAndDeliveryFormat"),
      next: COMPONENT_ID.LOGIN_INTRO,
      prev: COMPONENT_ID.PERSONALIZE_MEDIA,
    },
    {
      id: COMPONENT_ID.LOGIN_INTRO,
      conversation: t("haveAnExistingAccount"),
      component: AccountConfirmation,
    },
    {
      id: COMPONENT_ID.LOGIN,
      conversation: t("workEmailMessage"),
      component: AccountLogin,
      prev: COMPONENT_ID.GIFT_TYPE,
    },
    {
      id: COMPONENT_ID.VISIT_WEB_OR_APP,
      conversation: t("useWebsite"),
      component: VisitWebOrApp,
      prev: COMPONENT_ID.LANDING,
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_OFFERS,
      conversation: "",
      component: HappyYouOffers,
      prev: COMPONENT_ID.BRAND_LIST,
      next: "",
      isAnimated: false,
      label: t("happyYouOffers"),
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_ABOUT,
      conversation: "",
      component: AboutHappyYou,
      prev: COMPONENT_ID.BRAND_LIST,
      next: "",
      isAnimated: false,
      label: t("aboutThisCard"),
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_TERMS,
      conversation: "",
      component: HappyYouTerms,
      prev: COMPONENT_ID.BRAND_LIST,
      next: "",
      isAnimated: false,
      label: t("termsAndConditions"),
    },
    {
      id: COMPONENT_ID.HAPPY_YOU_BRANDS_INCLUDED,
      conversation: "",
      component: BrandsIncluded,
      prev: COMPONENT_ID.BRAND_LIST,
      next: "",
      isAnimated: false,
      label: t("brandsIncluded"),
    },
  ];
  return {
    firstTimeUserComponentList: staticComponents,
  };
};
