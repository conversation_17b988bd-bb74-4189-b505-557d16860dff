@import "variables";
@import "mixins";
@import "typo";
@import "common";
@import "tailwindcss";
@import "personalisation";
// css variables
:root {
  --barney-purple: #b800c4;
  --barney-purple-light: #b408a4;
  --strong-pink: #f07;
  --very-light-pink: #ffefec;
  --very-light-purple: #fae8f8;
  --light-purple: #f9ebf8;
  --light-pink: #ffe8fd;
  --medium-light-pink: #facff5;
  --error-text: #f00;
  --error-border: #f07;
  --red: #ff0028;
  --black: #000;
  --cool-grey: #a6adb4;
  --dark-grey: #202124;
  --warm-grey: #808080;
  --light-grey: #d8d8d8;
  --pale-grey: #f6f8f9;
  --hot-grey: #707070;
  --cornflower-blue: #4071d9;
  --white: #fff;
  --golden-yellow: #fabb05;
  --silver: #d9dfe4;
  --very-light-grey: #ebeded;
  --very-light-grey2: #ebebeb;
  --very-light-grey3: #fafafa;
  --medium-light-grey: #cbd0d3;
  --semi-dark-grey: #f2f5f8;
  --light-salmon: #ff9d98;
  --dark-seafoam-green: #3dbd7d;
  --border-color-sign-input: #e8ecf0;
  --pale-grey-bg: #f2f5f8;
  --carbon-black: #303030;
  --black-header: #545454;
  --after-bg-color: $default-bg-color;
  --default-bg-color: #ffd7ef;
  /* Override Tailwind v4 spacing variable to use 1px for all spacing utilities */
  --spacing: 1px;
}

.success-toast {
  .MuiSnackbarContent-message {
    padding: 0;
  }

  .MuiSnackbar-root {
    transform: translate(-50%, 0px);
    left: 50%;
    right: auto;
    .MuiSnackbarContent-root {
      border-radius: 12px;
      padding: 12px 16px;
      background-color: $dark-purple !important;
      min-height: 56px;
      min-width: 400px;
    }
  }

  .close-icon {
    right: 6px;

    @include rtl-styles {
      left: 6px;
      right: unset;
    }
  }
}

// profile
.work-account-accordion .MuiPaper-root {
  background: #99c6ff66;
  border-radius: 0 !important;
  box-shadow: none !important;
  .MuiButtonBase-root {
    padding: 24px 16px;
    .MuiAccordionSummary-content {
      margin: 0;
    }
  }
  .MuiCollapse-root
    .MuiCollapse-wrapper
    .MuiCollapse-wrapperInner
    .MuiAccordionDetails-root {
    padding: 0 16px 16px 16px;
  }
}

.drawer-custom-hamburger.MuiDrawer-root {
  z-index: 10009;
}

.confirm-dialog {
  z-index: 10099 !important;
}

// confirm modal 
.confirm-dialog {
  background: #545454;
  .MuiPaper-root.MuiPaper-rounded {
    margin: 20px;
    text-align: start;
    border-radius: 12px;
  }

  .confirm-dialog-icon {
    width: 25px;
    height: 25px;
  }

  .confirm-dialog-section {
    padding: 0 0 24px 0;
    color: $dark-purple;

    @include rtl-styles {
      direction: rtl;
    }
  }

  .confirm-dialog-title {
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 8px;
    padding: 0;
    font-family: var(--font-bricolage);
    color: #0e0f0c;
  }

  .confirm-dialog-content {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: -0.16px;
    padding: 0;

    p {
      margin: 0;
    }

    span {
      padding-top: 32px;
      display: inline-flex;
    }
  }

  .confirm-dialog-buttons {
    padding: 0;
    gap: 16px;
    display: flex;
    justify-content: center;

    > div {
      width: 100%;
    }

    button {
      height: 50px;
      font-size: 16px;
      font-weight: 700;
      border-radius: 10px;

      @include rtl-styles {
        padding: 9px 63px;
      }
    }

    .confirm-dialog-button-cancel {
      box-shadow: 0 2px 0px #0e0f0c,1px 0 0 #0e0f0c;;
    }
  }
}

// Language switcher
.language-switcher-drawer.MuiDrawer-root{
  z-index: 10099;
  background: #545454;
}
