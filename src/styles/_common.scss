/* Chrome, Safari, Edge, Opera */
/* stylelint-disable */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
/* stylelint-enable */

// base
html,
body {
  padding: 0;
  margin: 0;
  font-family: $default-font-family;
  font-weight: $regular;

  @media (max-width: 768px) {
    // overflow: auto;
  }

  & [dir="rtl"] {
    font-family: $arabic-font-family;
  }
  & [dir="rtl"] [class^="icon-"],
  [class*=" icon-"] {
    font-family: icomoon, arial, helvetica, sans-serif;
  }
  & [dir="rtl"] [class^="icon-"],
  [class*=" icon-"] {
    font-family: icomoon, arial, helvetica, sans-serif !important;
  }

  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Hides scrollbar */
  -ms-overflow-style: none; /* Hides scrollbar in IE/Edge */
}

html {
  font-size: 62.5%;
}

body {
  // padding: 24px 20px;

  color: $dark-purple;
}

input,
button,
select,
textarea:not(.brand-message-editor) {
  font-family: $default-font-family !important;

  body[dir="rtl"] & {
    font-family: $arabic-font-family !important;
  }
}

[lang="en"] {
  font-family: $default-font-family !important;
}

button[class^="icon-"],
button[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  body[dir="rtl"] & {
    font-family: $arabic-font-family !important;
  }
}

form {
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
}

a {
  color: inherit;
  text-decoration: none;

  // &:hover {
  //   color: $barney-purple;
  // }
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
}

::placeholder {
  color: $very-light-grey;
}

.work-login {
  .MuiFormControl-root {
    width: 100%;
  }

  .MuiOutlinedInput-root {
    height: 50px;
    border-radius: 12px;
    padding: 0px 16px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border: 1px solid rgba(0, 0, 0, 0.23);
      }
    }

    .MuiInputAdornment-root {
      margin: 0;
      height: auto;
    }

    .MuiInputBase-input {
      padding: 10px;
    }
  }

  &-error {
    fieldset {
      border: 1px solid $mild-red !important;
    }
  }

  .MuiInputBase-input {
    &::placeholder {
      color: #cccccc;
    }
  }

  .login-error {
    .MuiFormHelperText-root {
      margin-top: 4px;
      color: #e74848;
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: -0.12px;
      margin-inline: 0;
      text-align: inherit;
    }
  }
}

.drawer-custom .MuiDrawer-paper {
  width: 100%;
  height: calc(100% - 64px);
  top: unset;
  bottom: 0;
  box-shadow: none;
  z-index: 999;
}

.drawer-custom .MuiBackdrop-root {
  background: none;
}

.home-page {
  min-height: calc(100vh - 64px);
  overflow-y: scroll;
  padding: 24px 20px 80px;
}

.drawer-bottom-custom .MuiDrawer-paper {
  height: auto !important;
  border-radius: 12px;
  overflow: unset;
}
.unicode-bidi-plaintext {
  unicode-bidi: plaintext;
}

.country-phone-code {
  -ms-overflow-style: none;
  scrollbar-width: none;

  ::placeholder {
    opacity: 0.6;
  }

  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Hides scrollbar */
  -ms-overflow-style: none; /* Hides scrollbar in IE/Edge */

  ul {
    li.Mui-selected {
      background-color: $grey-primary !important;
    }
  }

  div {
    padding-top: 0;
    padding-bottom: 0;
    align-items: center;
  }

  .country-phone-code-flag {
    width: 24px;
    height: 24px;
    font-size: 24px;
    border-radius: 50px;
    overflow: hidden;
  }
}

.recipient-phone-field {
  .country-phone-code {
    .MuiInput-input.MuiSelect-select {
      font-family: var(--font-mona-sans);
      font-size: 14px;
      font-size: 1.4rem;
      font-weight: 700;
      display: flex;
      align-items: center;
      padding-right: 22px;
    }
    .MuiSelect-select {
      img {
        display: none;
      }
    }
  }
}

.user-phone-number {
  .add-recipient {
    img {
      height: 16px;
      width: auto;
    }

    .add-recipient-inner-wrapper {
      color: currentColor;
      box-sizing: content-box;
      background: none;
      height: 1.4375em;
      margin: 0;
      min-width: 0;
      width: 100%;
      -webkit-animation-name: mui-auto-fill-cancel;
      animation-name: mui-auto-fill-cancel;
      -webkit-animation-duration: 10ms;
      animation-duration: 10ms;
      padding: 16.5px 14px;
      max-width: calc(100% - 28px);
      border: 1px solid rgba(0, 0, 0, 0.23);
      border-radius: 12px;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;

      input {
        font-size: 16px;
        &::placeholder {
          font-size: 16px !important;
          font-style: normal;
          font-weight: 700;
          line-height: 24px;
          letter-spacing: -0.16px;
          color: $very-light-grey;
        }
      }

      .country-phone-code {
        span {
          font-size: 16px;
        }

        .MuiSelect-select:focus {
          background-color: #fff;
        }
      }
    }
  }
}

.brand-send-to {
  .MuiInput-underline:not(.Mui-error) {
    &::before {
      border: 0;
    }

    &:hover:not(.Mui-disabled)::before {
      border-bottom-width: 0px;
    }

    &:hover:not(.Mui-disabled)::before,
    &::after {
      border: 0;
    }
  }

  .MuiInput-input.MuiSelect-select {
    font-family: $default-font-family !important;
    @include font-size(14);
    font-weight: 700;
    display: flex;
    align-items: center;
    padding-right: 22px;

    @include rtl-styles {
      // padding-right: 18px;
    }
  }

  .MuiSelect-icon {
    fill: $dark-purple;
    margin-top: -1px !important;
    font-size: 22px;

    @include rtl-styles {
      margin-top: 0 !important;
    }
  }
}

.create-profile-work {
  .MuiOutlinedInput-root {
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    height: 48px;

    input::placeholder {
      color: $placeholder-gray;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
      padding: 0;
    }
  }
}
.user-phone-number .add-recipient .add-recipient-inner-wrapper {
  color: currentColor;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  min-width: 0;
  width: 100%;
  animation-name: mui-auto-fill-cancel;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  max-width: calc(100% - 28px);
  border: 1px solid rgba(0, 0, 0, 0.23);
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: -0.16px;
  height: 48px;
  padding: 0 14px;
}

// Profile page
.work-profile-details-fields
  .MuiFormControl-root
  .MuiTextField-root
  .MuiOutlinedInput-root {
  height: 50px;
  padding: 0 16px;

  fieldset {
    border-radius: 8px;
    border: 1px solid #99c6ff;
  }

  input {
    font-size: 14px;
    font-weight: 700;
    color: #ccc !important;
    font-family: var(--font-mona-sans);
  }
}

.success-toast {
  .MuiSnackbarContent-message {
    padding: 0;
  }

  .MuiSnackbar-root {
    transform: translate(-50%, 0px);
    left: 50%;
    right: auto;
    top: 75px;
    .MuiSnackbarContent-root {
      border-radius: 12px;
      padding: 8px;
      background-color: $dark-purple !important;
      width: 90vw;
    }
  }

  .close-icon {
    right: 6px;

    @include rtl-styles {
      left: 6px;
      right: unset;
    }
  }
}
