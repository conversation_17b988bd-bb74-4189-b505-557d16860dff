@import "@styles/variables";
@import "@styles/mixins";

.brand-button {
  height: 80px;
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 0 15px;
  width: 100%;
  background: $white;
  box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.06);
  z-index: 999;

  .selected-image {
    width: 31px;
    height: 50px;
    flex-shrink: 0;
    aspect-ratio: 31/50;
    border-radius: 4px;
  }

  &__continue {
    @include font-size(16);

    font-weight: 600;
    height: 50px;
    width: 100%;
  }

  &__half {
    width: 50%;
  }

  &__skip {
    @include font-size(16);

    font-weight: 600;
    height: 50px;
    width: 100%;
    text-align: center;
    color: $barney-purple;
    margin-top: 10px;
    cursor: pointer;
  }

  &__disabled {
    opacity: 0.3;
    pointer-events: none;
  }
}

.crop-button {
  height: 80px;
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 0 15px;
  width: 100%;
  background: $white;
  box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.06);
  z-index: 99999;
}

.brand-photo-editor-tool {
  height: 460px;

  @media (max-width: ( $sm - 403)) {
    height: 400px;
  }

  /* stylelint-disable */
  [data-test="MainBarButtonClose"] {
    display: none;
  }

  [data-test="NummericInput"] {
    padding: 0 !important;
    width: 30px !important;
  }

  [data-test="BrushSize"] > div {
    margin-right: -15px !important;
  }
  [data-test="BrushHardness"] > div {
    margin-right: -15px !important;
  }

  [data-test="BrushSize"] {
    width: 190px !important;
    max-width: 190px !important;
    margin-right: 40px;
  }

  [data-test="ToolControlBarExpandableControls"] {
    width: 100% !important;
    padding: 0 !important;
  }

  [data-test="Opacity"] + div > div > div {
    margin-right: -10px !important;
  }

  [data-test="Size"] + div > div > div {
    margin-right: -10px !important;
  }

  [data-test="ToolControlBarExpandableControls"]
    > div
    > div:nth-child(2)
    > div {
    margin-right: -10px !important;
  }

  /* stylelint-enable */
}

.brand-video-editor-tool {
  height: 432px;
  overflow: hidden;

  .camera_tag {
    width: 100% !important;
    height: 100% !important;
  }
}

.brand-video-editor-cancel {
  display: block;
  text-align: center;
  background: $pale-grey-bg;
  color: $dark-purple;
  font-size: 16px;
  margin-top: 18px;
  font-weight: 600;
}

.brand-video-editor {
  .MuiDialogContent-root {
    padding: 0 !important;
  }

  .MuiDialog-paper {
    margin: 0 16px;
    padding: 0 !important;
    width: 100%;
  }
}

.MuiDialog-paper {
  margin: 0 16px !important;
  width: 100%;
  border-radius: 24px !important;

  @include rtl-styles {
    direction: ltr;
  }

  @media (max-width: ( $msm + 20)) {
    margin: 0 8px !important;
  }
}

.MuiDialogContent-root {
  padding: 0 !important;
}

.MuiPickersToolbar-penIconButton {
  display: none !important;
}

.greetings-accordion {
  background: $white !important;
  border: none;
  // padding-bottom: 24px;
}

.greetings-accordion .MuiAccordionSummary-root {
  padding: 0;
}

.greetings-accordion .MuiAccordionSummary-content {
  margin: 10px 0;
}

.greetings-accordion .MuiAccordionDetails-root {
  padding: 0;
}

// Camera tag custom changes for personalisation section
.camera_tag {
  font-family: $default-font-family !important;
}

.camera_tag .cameratag_screen.cameratag_completed .cameratag_thumb_bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position-y: center;
  border: 10px $white solid;
  display: flex;
  justify-content: center;
  &::after {
    content: "";
    background: black;
    position: absolute;
    height: 100%;
    width: 100%;
    opacity: 0.4;
  }
  img {
    position: absolute;
    top: 37%;
    z-index: 2;
  }
}

.camera_tag .cameratag_screen.cameratag_completed .cameratag_checkmark {
  text-transform: capitalize;
  font-size: 16px !important;
  font-family: $default-font-family;
  top: 60% !important;
  z-index: 9;
}

// CameraTag Accept screen

.camera_tag .cameratag_screen.cameratag_accept {
  display: flex !important;
  flex-direction: column;
  justify-content: center !important;
  align-items: center !important;
}
.camera_tag .cameratag_screen.cameratag_accept.accept_border {
  border: 10px $white solid;
}

.camera_tag .cameratag_screen.cameratag_accept.dark_overlay::before {
  content: "";
  background: #000000;
  opacity: 0.5;
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 1;
}

.camera_tag .cameratag_screen.cameratag_wait {
  border: 10px $white solid;
  z-index: 10;
}
.camera_tag .cameratag_screen.cameratag_accept.hide_element {
  display: none !important;
}

.cameratag_screen.cameratag_accept .cameratag_accept_btn,
.camera_tag .cameratag_screen.cameratag_accept .cameratag_accept_btn,
.camera_tag .cameratag_screen.cameratag_accept .cameratag_rerecord_btn,
.camera_tag .cameratag_screen.cameratag_accept .cameratag_play_btn {
  border: none !important;
  background: none !important;
  height: 15% !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 20px !important;
  color: $white !important;
  z-index: 9;
}
.cameratag_play_btn.cameratag_play {
  order: 1;
  -webkit-order: 1;
}
.cameratag_rerecord_btn.cameratag_record {
  order: 2;
  -webkit-order: 2;
}
.cameratag_accept_btn.cameratag_publish {
  order: 3;
  -webkit-order: 3;
}

.cameratag_screen.cameratag_playback,
.cameratag_screen.cameratag_recording,
.cameratag_screen.cameratag_count {
  border: 10px $white solid;
}

.cameratag_primary_link {
  width: 100% !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.camera_tag .cameratag_screen.cameratag_accept .cameratag_play_btn span {
  font-size: 20px;
}

.camera_tag .cameratag_screen.cameratag_upload .cameratag_upload_status {
  font-size: 20px !important;
}

.camera_tag .cameratag_screen .cameratag_prompt {
  text-transform: capitalize;
}
.camera_tag.rtl {
  direction: rtl;
}

.cameratag_prompt,
.cameratag_upload_status {
  display: none !important;
}
.radial-progress {
  background-color: transparent !important;
  width: 90% !important;
  top: 50% !important;
  transform: translateY(-43px) !important;
}
.radial-progress .circle .shadow {
  box-shadow: none !important;
}
.radial-progress .inset {
  display: none !important;
}
.uploading_progress {
  align-self: center;
  progress {
    width: 95%;
    height: 5px;
    border: 0px;
    border-radius: 20px;
  }
  progress::-webkit-progress-value {
    background: $barney-purple !important;
    border-radius: 20px;
  }

  progress::-moz-progress-bar {
    background: $barney-purple !important;
    border-radius: 20px;
  }

  progress::-ms-fill {
    background: $barney-purple !important;
    border-radius: 20px;
  }
  .bottomCont {
    display: flex;
    justify-content: space-between;
    margin: 13.5px 10px 0;

    p {
      font-size: 16px;
      margin: 0;
    }
  }
}

.cameratag_select_prompt,
.cameratag_mobile_prompt {
  text-transform: capitalize;
  font-size: 20px !important;
  padding: 0 20px;
  font-weight: 500 !important;
}

.camera_tag .cameratag_screen.cameratag_error .cameratag_error_message {
  color: $dark-purple !important;
  font-family: $default-font-family !important;
  font-size: 18px !important;
  font-weight: 500 !important;
}

.MuiOutlinedInput-notchedOutline {
  border-width: 0 !important;
}

.MuiMenuItem-root {
  font-family: $default-font-family !important;

  @include rtl-styles {
    font-family: $arabic-font-family !important;
  }
  font-size: 16px !important;
}

.MuiInputBase-root::before {
  border: none !important;
}

#ygag-camera {
  video {
    height: inherit !important;
  }

  .cameratag_spinner img {
    width: 40px;
    position: absolute;
    right: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
  }

  .cameratag_screen.cameratag_start {
    background: $white;
    background-color: $pale-grey-bg !important;
    padding: 24px 16px;
  }

  .cameratag_select_prompt {
    display: none !important;
  }

  .cameratag_settings_btn {
    display: none !important;
  }

  #ygag-camera__start-el-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 20px;
    background-color: $pale-grey-bg;
    border-radius: 12px;
    min-height: 164px;
    position: relative;
    z-index: 99;

    a.cameratag_primary_link {
      margin-top: 0;
      background-color: $white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 152px;
      width: 50%;
      color: $barney-purple;

      .cameratag_prompt_label {
        @include font-size-important(16);

        bottom: 25px;
      }

      .cameratag_action_icon {
        @include font-size-important(32);

        margin-top: -40px;
      }

      br {
        display: none;
      }

      i.icon-video-camera {
        @include font-size-important(22);
      }
    }

    .cameratag_primary_link.cameratag_upload {
      img {
        width: 22px;
        margin-top: -35px;
      }
    }

    .ygag-camera__or-placeholder {
      @include font-size-important(16);

      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
      border-radius: 50%;
      height: 60px;
      width: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: $pale-grey-bg;
      color: $dark-purple;
      transform: translate(-50%, -50%);
    }
  }

  #ygag-camera_start_screen
    > #ygag-camera__start-el-wrapper:first-of-type {
    display: none;
  }

  #ygag-camera__start-el-wrapper
    .cameratag_primary_link
    img:first-of-type {
    display: none;
  }

  .cameratag_error {
    background: $white;
    .cameratag_error__wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;

      &__close-button {
        font-size: 13px;
        font-weight: 600;
        text-align: center;
        color: $slight-purple;
        padding: 8px 20px;
        border-radius: 12px;
        border: 1px solid $slight-purple;
        margin-top: 20px;
      }

      h3 {
        @include font-size-important(20);

        color: $dark-purple;
        font-weight: 600;
        line-height: normal;
        width: 80%;
        margin: 0 0 15px;
      }

      span.error-message {
        @include font-size-important(12);

        color: $warm-grey;
        font-weight: 200;
        line-height: normal;
        width: 80%;
      }
    }
  }
}

.brand-user-message__drawer {
  .MuiDrawer-paper {
    border-radius: 16px 16px 0 0;
  }

  .MuiFormControl-root {
    width: 100%;
  }

  .MuiFormGroup-root {
    justify-content: space-between;
  }
}

.brand-gif-editor-tool {
  height: calc(100vh - 150px);
  overflow: hidden;
  // overflow-y: scroll;

  .giphy-grid {
    height: calc(100vh - 240px);
    overflow-y: scroll;
    @include rtl-styles {
      > div {
        display: flex;
        flex-direction: row-reverse;
      }
    }
  }

  .giphy-search-bar {
    margin: 24px 0;
    padding: 10px;
    position: relative;

    > svg {
      height: 14px;
      width: 14px;
    }

    input {
      background: $pale-grey;
      padding: 12px 12px 12px 32px;
      border-radius: 12px;
      font-size: 14px;
      font-family: var(--font-poppins);
      position: relative;

      &::placeholder {
        color: #545454;
        font-size: 14px;
      }

      @include rtl-styles {
        padding: 12px 32px 12px 12px;
      }
    }

    input + div {
      position: absolute;
      width: 34px;
      height: 34px;
    }

    input + div {
      > div {
        background: transparent;

        &:before {
          content: none;
        }
      }
      svg path {
        fill: black;
      }
    }

    input + svg + div {
      position: absolute;
      width: 34px;
      height: 34px;

      > div {
        background: transparent;

        &:before {
          content: none;
        }
      }
      svg path {
        fill: black;
      }
    }
  }

  .giphy-search-bar::before {
    content: "";
    display: block;
    width: 40px;
    height: 3.5px;
    background-color: #888888;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 6px;
  }

  .giphy-search-bar-cancel {
    margin-left: 20px;
    right: 25px;
    position: absolute;

    @include rtl-styles {
      right: auto;
      left: 0;
    }
  }

  .giphy-empty-message {
    color: #a6a6a6;
    display: flex;
    justify-content: center;
    margin: 30px 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.brand-gif-editor {
  .MuiDialog-paper {
    padding: 17px;
    max-width: 832px;
  }
}

.drawer-bottom-custom .MuiDrawer-paper {
  height: auto !important;
  border-radius: 12px;
  overflow: unset;
}

.confirm-drawer,
.personalisation-drawer {
  .MuiDrawer-paper {
    border-top-left-radius: 24px !important;
    border-top-right-radius: 24px !important;
  }
}

.image-crop-modal {
  .MuiDialogContent-root {
    padding: 20px !important;
    overflow: hidden;
    // min-width: 640px;
  }

  p:first-child {
    .edit-icon {
      display: none;
    }
  }

  .ReactCrop__crop-selection:not(
    .ReactCrop--no-animate .ReactCrop__crop-selection
  ) {
    background-image:
      linear-gradient(to right, #fff 50%, #0071ff 50%),
      linear-gradient(to right, #fff 50%, #0071ff 50%),
      linear-gradient(to bottom, #fff 50%, #0071ff 50%),
      linear-gradient(to top, #fff 50%, #0071ff 50%) !important;
  }

  .MuiSlider-thumb {
    &::before {
      background-color: #fff;
    }
  }
}

.image-crop-container {
  // display: flex;
  height: 100vh;

  .MuiDialog-paper {
    margin: 8px !important;
  }
}
