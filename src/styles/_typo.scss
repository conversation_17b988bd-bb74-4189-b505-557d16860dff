@import "variables";
@import "mixins";

h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
  margin: 0;
}

h2 {
  @include font-size(30);

  font-weight: 500;
  line-height: 48px;
  margin: 30px 0;

  @media (max-width: ( $lg + 40)) {
    @include font-size(25);

    line-height: 38px;
  }

  @media (max-width: ( $md + 40)) {
    @include font-size(20);

    line-height: 30px;
  }
}

h3 {
  @include font-size(24);

  font-weight: 500;
  line-height: 30px;
  margin: 30px 0;
}

h4 {
  @include font-size(20);

  font-weight: 500;
  line-height: 25px;
  margin: 30px 0;
}

h5 {
  @include font-size(16);

  font-weight: 500;
  line-height: 23px;
  margin: 15px 0;
}

h6 {
  @include font-size(14);

  font-weight: 500;
  line-height: 20px;
  margin: 15px 0;
}

.font-bricolage {
  font-family: var(--font-bricolage);
}

.font-mona-sans {
  font-family: var(--font-mona-sans);
}

.font-noto-kufi {
  font-family: var(--font-noto-kufi);
}
