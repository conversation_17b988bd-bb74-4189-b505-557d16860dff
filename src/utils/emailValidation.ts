const EMAIL_REGEX = /^[^@]+@[^@]+\.[^@]+$/;
const EMAIL_REGEX2 =  /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;


/**
 * @method isValidEmailAddress
 * @description To verify the provided address is meet the basic requirements
 * @param emailAddress
 * @returns
 */
const isValidEmailAddress = (emailAddress: string): boolean => {
  return EMAIL_REGEX.test(emailAddress);
};

const isValidLoginEmailAddress = (emailAddress: string): boolean => {
  return EMAIL_REGEX2.test(emailAddress);
};
export { isValid<PERSON>mailAddress, EMAIL_REGEX,isValidLoginEmailAddress };
