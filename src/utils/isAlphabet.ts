export const isAlphabetRegex = /^[a-zA-Z\u0600-\u06FF\s]*$/;

const isAlphabet = (evt: any) => {
  evt = evt ? evt : window.event;
  const charCode = evt.which ? evt.which : evt.keyCode;
  const isShift = evt.shiftKey;

  if (
    (charCode >= 65 && charCode <= 90) ||
    (charCode >= 97 && charCode <= 122) ||
    (charCode >= 1536 && charCode <= 1791) || // Arabic Characters
    (!isShift && charCode >= 48 && charCode <= 57) ||
    charCode == 32 ||
    charCode == 39
  )
    return true;
  return false;
};

export default isAlphabet;
