import * as localForage from "localforage";

const LocalStore = {
  setItem: async (key: string, value: any) => {
    return await localForage.setItem(key, value);
  },

  getItem: async (key: string) => {
    return await localForage.getItem(key);
  },

  removeItem: async (key: string) => {
    return await localForage.removeItem(key);
  },

  clear: async () => {
    return await localForage.clear();
  },
};

export default LocalStore;
