import DateTime from "./dateTime";

export const formatTime = (deliveryTime: any) => {
  // #. Get the delivery schedule time
  const time = DateTime.formatLocaleTime(deliveryTime, {
    hour: "2-digit",
    minute: "2-digit"
  });

  return time;
};

export const formatDate = (deliveryDate: any) => {
  // #. Get the delivery schedule date
  const formatter = new Intl.DateTimeFormat("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit"
  });
  // Format the deliveryDate using the formatter
  const parts = formatter.formatToParts(deliveryDate);
  const date = `${parts[4].value}-${parts[2].value}-${parts[0].value}`;
  return date;
};

/**
 * @method formatSavedDateTime
 * @description Format the saved date time strings to date time object
 */
export const formatSavedDateTime = (
  date: string = new Date().toISOString().substring(0, 10),
  time: string,
  timezone: string,
  isScheduled: boolean
) => {
  const dateVal = new Date(date);

  if (!isScheduled) {
    return dateVal;
  }

  if (!time) {
    const [hours, minutes]: any = DateTime.getTimezoneDate(timezone);
    dateVal.setHours(hours, minutes);
  } else {
    const [timeVal, modifier] = time.split(" ");
    let [hours, minutes] = <any>timeVal.split(":");
    if (modifier?.toUpperCase() === "PM") {
      hours = parseInt(hours, 10) + 12;
    }

    dateVal.setHours(hours, minutes);
  }

  return dateVal;
};

export const  isPastDateTime=(date, time, timezone) => {
  // Parse the date and time strings into Date objects
  const dateObj = new Date(date);
  const timeObj = new Date(time);

  // Check if the parsed dates are valid
  if (isNaN(dateObj.getTime()) || isNaN(timeObj.getTime())) {
      throw new Error("Invalid date or time format.");
  }

  // Extract hours and minutes from the time object (ignore seconds)
  const hours = timeObj.getHours();
  const minutes = timeObj.getMinutes();

  // Apply the extracted time to the date object (set seconds to 0)
  dateObj.setHours(hours, minutes, 0); // Seconds explicitly set to 0

  // Get the current time in the specified timezone
  const currentTimeInTimezone = new Date(
      new Date().toLocaleString("en-US", { timeZone: timezone })
  );

  // Truncate seconds and milliseconds from the current time
  currentTimeInTimezone.setSeconds(0, 0);

  // Add 10 minutes to the truncated current time
  const bufferTime = new Date(currentTimeInTimezone.getTime() + 10 * 60 * 1000);

  // Compare the selected datetime (without seconds) with the buffered time
  return dateObj < bufferTime;
}
