import { GUEST_USER_SESSION_COOKIE } from "@constants/common";
import userAuth from "@features/common/userAuth.hook";

/**
 * @method fetchAuthInfo
 */
const fetchAuthInfo = async (request: any, locale: any, storeCode: string) => {
  const refreshToken = request?.cookies?.get?.('atwork_token')?.value ?? "";

  const { getAuthInfo } = userAuth();
  if (refreshToken) {
    return getAuthInfo(refreshToken, locale, storeCode).then(({ idToken, accessToken, isUserSignedIn, displayName, userAttributes }: any) => ({
      refreshToken,
      idToken,
      accessToken,
      isUserSignedIn,
      displayName,
      userAttributes,
    }));
  } else {
    return new Promise((resolve, reject) => {
      resolve({
        refreshToken: '',
        idToken: '',
        accessToken: '',
        isUserSignedIn: false,
        displayName: '',
        userAttributes: {
          isFirstTimeUser: true
        },
      });
    });
  }
};

export default fetchAuthInfo;
