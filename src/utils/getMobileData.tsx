import { parsePhoneNumberFromString } from 'libphonenumber-js';

const extractPhoneDetails = (fullPhoneNumber: any) => {
  try {
    const phoneNumber = parsePhoneNumberFromString(fullPhoneNumber);
    if (phoneNumber) {
      return {
        phoneCode: `+${phoneNumber.countryCallingCode}`,
        countryCode: phoneNumber.country,
        phoneNumber: phoneNumber.nationalNumber,
      };
    }
  } catch (error) {
    console.warn('Error parsing phone number:', error);
  }
  return {
    phoneCode: '',
    countryCode: '',
    phoneNumber: '',
  };
};


export default extractPhoneDetails;
