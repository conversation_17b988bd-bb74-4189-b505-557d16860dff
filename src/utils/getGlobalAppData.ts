import { secureLicenceQuery } from "@features/common/commonAPI";
import commonMiddleware from "./common.middleware";

export async function getGlobalAppData(params: any) {
    const pageData = await commonMiddleware(params);
    if (!pageData.data) return pageData;

    const {
        ipAddress,
        region,
        localeCode,
        languageReource,
        refreshToken,
        idToken,
        isUserSignedIn,
        accessToken,
        displayName = null,
        commonData,
        activeRegion,
        userAttributes = null,
        atWorkSiteConfig,
        sessionCookie,
    } = pageData.data as any;

    const secureLicenceQueryRequest = secureLicenceQuery(localeCode, ipAddress);
    const [secureConfigs] = await Promise.all([secureLicenceQueryRequest]);
    return {
        // Add translation data here if needed for app directory (see next-i18next docs)
        commonData,
        isUserSignedIn,
        idToken,
        accessToken,
        refreshToken,
        displayName,
        secureConfigs: secureConfigs?.data?.secureConfigs,
        activeRegion,
        ipAddress,
        region,
        localeCode,
        userAttributes,
        atWorkSiteConfig,
        sessionCookie,
    };
}