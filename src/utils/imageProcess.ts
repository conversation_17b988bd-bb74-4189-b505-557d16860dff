/**
 * @method blobToBase64
 * @param blob
 * @returns
 */
const blobToBase64 = (blob: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
};

/**
 * @method base64ToBlob
 * @param base64
 * @returns
 */
const base64ToBlob = (base64: any) => {
  return new Promise((resolve, reject) => {
    fetch(base64).then((blob: any) => resolve(blob));
  });
};

export { blobToBase64, base64ToBlob };
