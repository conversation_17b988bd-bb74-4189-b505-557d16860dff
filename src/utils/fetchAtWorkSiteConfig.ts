import { PLATFORM_TYPE } from "@constants/common";
import { WORK_SITE_CONFIG_QUERY } from "@features/common/common.query";
import { initializeApollo } from "@graphql/apolloClient";

export const fetchAtWorkSiteConfig = async (
  locale: string,
  storeCode: string,
  ipAddress: string,
  cookieHeader: string,
): Promise<{ config: any; sessionCookie?: string }> => {
  const localeCode = locale ? locale.split("-")[0] : "en";
  let serverCookies: string[] = [];

  try {
    const apolloClient = initializeApollo(
      localeCode,
      null,
      {},
      ipAddress,
      (cookies) => {
        serverCookies = cookies;
      }
    );

    const siteConfig = await apolloClient.query({
      query: WORK_SITE_CONFIG_QUERY,
      context: {
        clientName: "at-work",
        headers: {
          "access-locale": storeCode,
          "app-platform": PLATFORM_TYPE.WEB.toLowerCase(),
          "yg-ip-address": ip<PERSON><PERSON><PERSON>,
          <PERSON><PERSON>: cookieHeader || "",
        },
        credentials: "include",
      },
    });

    // Extract just the "atwork__session" cookie
    const sessionCookie = serverCookies.find(cookie =>
      cookie.startsWith("atwork__session=")
    );

    return { config: siteConfig, sessionCookie };
  } catch (error) {
    console.error("Error fetching @work site config ", error);
    return { config: null };
  }
};
