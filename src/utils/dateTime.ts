/**
 * @name DateTime
 * @description To handle the date and time related util methods
 * @dependencies Need to check the relevence of external libraries
 */

const DateTime = {
  /**
   * @method today
   * @description To get today's date
   * @format YYYY-MM-DD
   */
  today: () => {
    return new Date().toISOString().substring(0, 10);
  },

  /**
   * @metho format
   * @description convert a date in to specific fomat
   * @param format
   * @param value
   * @returns
   */
  format: (format: string, value: string | Date) => {
    if (format == "dd Mmm yyyy") {
      return new Date(value).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    }
  },

  /**
   * @metho getTimezoneDate
   * @description Get hour, minute, and date info of timezone
   * @param timezone
   * @returns
   */
  getTimezoneDate: (timezone: string) => {
    // #. Get formatted value
    const date = new Date();

    // #. Add a 10 minute buffer
    date.setMinutes(date.getMinutes() + 10);

    const options: any = {
      timeZone: timezone,
      year: "numeric",
      month: "numeric",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    };

    const formatter = new Intl.DateTimeFormat([], options);
    const tzDate = formatter.format(date);
    let [hour, minute]: any = tzDate.split(",")[1].split(":");
    minute = minute?.split(" ")[0];

    date.setHours(hour, minute);

    return [hour, minute, date];
  },

  /**
   * @metho format
   * @description convert a date in to specific fomat
   * @param format
   * @param value
   * @returns
   */
  formatLocaleTime: (date: any, options: any = {}) => {
    return date.toLocaleTimeString("en-US", options);
  },
};

export default DateTime;
