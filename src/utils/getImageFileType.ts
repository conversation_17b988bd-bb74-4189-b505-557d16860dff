export const getImageFileType = (input: string | File): string | null => {
    if (typeof input === "string") {
        // Handle Base64 String for images
        const imageMatch = input.match(/^data:image\/(png|jpeg|jpg|gif|webp);base64,/);
        if (imageMatch) return imageMatch[1].toUpperCase();
        // Handle Base64 String for PDF
        const pdfMatch = input.match(/^data:application\/pdf;base64,/);
        if (pdfMatch) return "PDF";
        return null;
    } else if (input instanceof File) {
        // Handle File Object for images
        const imageType = input.type.match(/image\/(png|jpeg|jpg|gif|webp)/);
        if (imageType) return imageType[1].toUpperCase();
        // Handle File Object for PDF
        if (input.type === "application/pdf") return "PDF";
        return null;
    }
    return null;
};
