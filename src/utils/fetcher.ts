/*******  Custom fetch util for handling REST APIs *******/
import * as Sentry from "@sentry/nextjs";

const fetcher: any = async (
  url: RequestInfo,
  options: RequestInit | undefined
) => {
  const res = await fetch(url, options);
  if (res.ok) {
    return promiseResolveHandler(res);
  } else {
    if (
      res.status === 400 ||
      res.status === 429 ||
      res.status === 403 ||
      typeof window === "undefined"
    ) {
      return promiseRejectHandler(res);
    } else {
      const error = await promiseRejectHandler(res);
      await Sentry.captureMessage(`[Fetcher error]: ${error}`);
      await Sentry.captureException(error);
      // custom error page url
      const fallbackUrl = `/error/${res.status}/`;
      window.location.href = fallbackUrl;
    }
  }
};

const promiseResolveHandler = async (res: {
  json: () => Promise<any>;
  status: any;
  ok: any;
}) => {
  return new Promise((resolve) =>
    res.json().then((data: any) =>
      resolve({
        status: res.status,
        ok: res.ok,
        data,
      })
    )
  );
};

const promiseRejectHandler = (res: {
  json: () => Promise<any>;
  text: () => Promise<any>;
  status: any;
  ok: any;
}) => {
  return new Promise((reject) =>
    (res.status === 400 || res.status === 429 || res.status === 403
      ? res.json()
      : res.text()
    ).then((data: any) =>
      reject({
        status: res.status,
        ok: res.ok,
        data,
      })
    )
  );
};

const fetchAPI: any = async (
  url: RequestInfo,
  options: RequestInit | undefined
) => {
  try {
    // custom fetch api call
    return await fetcher(url, options);
  } catch (error) {}
};

export default fetchAPI;
