import { useEffect, useRef } from "react";

export const useOutsideClick = ({ onTrigger }: { onTrigger: () => void }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        ref.current &&
        !ref.current.contains(event.target as Node) &&
        document?.getElementById("__next")?.contains(event.target as Node)
      ) {
        if (ref.current !== event.target) {
          onTrigger();
        }
      }
    };

    document.addEventListener("mouseup", handleClickOutside);
    document.addEventListener("touchend", handleClickOutside);

    return () => {
      document.removeEventListener("mouseup", handleClickOutside);
      document.removeEventListener("touchend", handleClickOutside);
    };
  }, [onTrigger]);

  return ref;
};
