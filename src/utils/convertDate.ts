// Convert UTC date and time to current store timezone
import { DateTime } from "luxon";

const convertDate = (datePlaced, locale) => {
    const utcDateTime = datePlaced;
    const clientTimezone = DateTime?.local().zoneName || "";

    const convertedDateTime = DateTime?.fromFormat(
      utcDateTime,
      "dd/MM/yyyy HH:mm:ss",
      { zone: "utc" }
    )
      ?.setZone(clientTimezone)
      ?.toFormat("dd MMMM yyyy • hh:mm a", { locale: locale });
    return convertedDateTime;
  }

  export default convertDate;