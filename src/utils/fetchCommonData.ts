import { PA<PERSON>URLS } from "@constants/common";
import {
  CUSTOM_404_REDIRECT,
  HTTP_STATUS_CODE,
  PAGE_404_REDIRECT,
} from "@constants/statusCodes";
import commonDataQuery, { storesDataQuery } from "@features/common/commonAPI";
import { CommonDataInterface } from "@interfaces/common.inteface";
import { merge, mergeWith } from "lodash";
import * as Sentry from "@sentry/nextjs";


/**
 * @method fetchCommonData
 */
const fetchCommonData = async (
  locale: string,
  region: string,
  ipAddress: string,
  siteConfigData: any
) => {
  let commonData: any = await commonDataQuery(locale, region, ipAddress);
  if (!commonData) {
    Sentry.captureMessage(`commonDataQuery failure, ${JSON.stringify(commonData)}`);
    return CUSTOM_404_REDIRECT;
  }
  const { networkStatus } = commonData;

  // #. Handle unsupported countries based on the HTTP status code
  if (networkStatus === HTTP_STATUS_CODE.INVALID_STORE) {
    return {
      redirect: {
        destination: `/${locale}-${region}${PAGEURLS.UNSUPPORTED_COUNTRY}`,
        permanent: false,
      },
    };
  }

  // #. Get stores data
  const storesData: any = await storesDataQuery(locale, ipAddress);
  if (!storesData) {
    Sentry.captureMessage(`storesDataQuery failure, ${JSON.stringify(commonData)}`);
    return CUSTOM_404_REDIRECT;
  }
  const extendedData: any = { data: {} };

  mergeWith(extendedData.data, commonData?.data, {
    stores: storesData?.data?.stores || [],
    siteConfigs: siteConfigData?.siteConfigs,
  });

  // #. Handle unsupported countries by the site config information
  const regionInfo: any = getActiveRegionInfo(extendedData, region, locale);
  if (!regionInfo) {
    return CUSTOM_404_REDIRECT;
  }

  return { data: extendedData, regionInfo };
};

/**
 * @method getActiveRegionInfo
 * Get active region info from common data
 * @param data
 * @private
 */
const getActiveRegionInfo = (
  commonData: CommonDataInterface,
  region: string,
  locale: string
) => {
  const supportedLocales =
    commonData?.data?.siteConfigs?.edges?.[0]?.node?.languages || [];

  // #. If locale from url not found in the siteconfig means invalid url entered
  // #. Redirect to 404 page
  if (supportedLocales.indexOf(locale) === -1) {
    return false;
  }

  const activeRegion =
    region || commonData?.data?.siteConfigs?.edges?.[0]?.node?.defaultCountry;

  const stores = commonData?.data?.stores?.edges || [];
  return stores?.find((store) => {
    return (
      String(store.node?.country?.code).toUpperCase() ===
      String(activeRegion).toUpperCase()
    );
  });
};

export default fetchCommonData;
