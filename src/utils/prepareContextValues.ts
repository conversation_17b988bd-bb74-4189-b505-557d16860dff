import { INITIAL_STATE } from "mockData/index.data";
import { formatSavedDateTime } from "./formatDateTime";
import { BRAND_CARD_TYPE, BRAND_FORM_STATE, CARD_PURCHASE_TYPE, GIFT_TYPE_SELECTION } from "@constants/common";
import isAbsoluteURL from "./isAbsoluteURL";
import { GiphyFetch } from "@giphy/js-fetch-api";
import { giphyKey } from "@constants/envVariables";


const fetchGifById = async (gifId: string) => {
    try {
        if (isAbsoluteURL(gifId)) {
            return gifId;
        }
        const gf = new GiphyFetch(giphyKey);
        const { data } = await gf.gif(gifId);
        const gifUrl = data?.images?.original?.url;
        return gifUrl;
    } catch (error) {
        console.error("Error fetching GIF:", error);
        return "";
    }
};

export const transformRecipientsList = (record: any) => {
    return record?.recipientList.map((item: any) => {
        if (record?.deliveryMethod === "digital") {
            const deliveryDateTime = formatSavedDateTime(
                item?.scheduledDate || undefined,
                item?.scheduledTime || undefined,
                item?.timezone || undefined,
                item?.isScheduled
            );
            return {
                senderName: item?.senderName,
                recipientName: item?.receiverName,
                amount: item?.amount,
                recipientEmail: item?.receiverEmail,
                recipientMobile: item?.receiverPhoneNumber,
                currency: item?.currency,
                deliveryLanguage: item?.language,
                dialCode: item?.receiverCountryCode,
                date: {
                    date: deliveryDateTime,
                    time: deliveryDateTime,
                    timezone: item?.timezone,
                    type: item?.scheduledType,
                },
                referenceId: item?.referenceId,
            };
        } else {
            return {
                amount: item?.amount,
                number: item?.quantity,
                currency: item?.currency,
                referenceId: item?.referenceId,
            };
        }
    });
};

export const prepareDeliveryKeys = async (record: any) => {
    const deliveryKeys = structuredClone(INITIAL_STATE.DELIVERY_KEYS);

    deliveryKeys.card.personalisation.video.uuid =
        record?.cameratagAssetId || "";
    deliveryKeys.card.giftMessage.message = record?.message;
    deliveryKeys.card.giftMessage.backgroundColor =
        record?.msgBgColor && record?.msgBgColor;
    deliveryKeys.card.giftMessage.fontSize =
        record?.msgFontSize && record?.msgFontSize.toString();
    deliveryKeys.card.giftMessage.fontFamily =
        record?.msgFontType && record?.msgFontType;
    deliveryKeys.card.greetingCover.language =
        record?.greetingsHubLanguage && record?.greetingsHubLanguage;
    deliveryKeys.card.greetingCover.occasion.code =
        record?.greetingsHubOccasionCode;
    deliveryKeys.card.greetingCover.occasion.name =
        record?.greetingsHubOccasionName || "";
    deliveryKeys.card.greetingCover.referenceCode =
        record?.greetingsHubReference;
    deliveryKeys.card.greetingCover.staticGifPath =
        record?.greetingsHubStaticImgUrl || "";
    deliveryKeys.card.personalisation.gif.url =
        record?.personalizeGifUrl ? await fetchGifById(record?.personalizeGifUrl) : "";
    deliveryKeys.card.personalisation.gif.id = record?.personalizeGifUrl ?? "";
    deliveryKeys.card.greetingCover.coverType = record?.greetingsHubType;
    deliveryKeys.card.greetingCover.filePath = record?.greetingsHubImgUrl;

    deliveryKeys.card.personalisation.photo.file =
        record?.personalizeImageUrl || "";
    deliveryKeys.card.personalisation.video.medias.mp4 =
        record?.personalizeVideoUrl || "";
    deliveryKeys.customeGreetings.previewUrl = record?.greetingCard;
    deliveryKeys.customeGreetings.file = record?.greetingCard;

    return deliveryKeys;
};

export const preparePdfPreview = (record: any) => {
    const pdfPreview = structuredClone(INITIAL_STATE.PDF_PREVIEW);
    pdfPreview.message.text = record?.coverMessage || "";
    pdfPreview.messageStyle.color = record?.gcmColor || "";
    pdfPreview.messageStyle.fontFamily = record?.gcmFontFamily || "";
    pdfPreview.messageStyle.fontWeight = record?.gcmFontWeight || "";
    pdfPreview.messageStyle.textAlign = record?.gcmTextAlign || "";
    pdfPreview.messageStyle.textDecoration =
        record?.gcmTextDecoration || "";
    pdfPreview.logo.previewUrl = record?.senderLogo || "";
    pdfPreview.greetings.previewUrl = record?.greetingCard || "";
    pdfPreview.pdfConfigs.logo = !!record?.senderLogo;
    pdfPreview.pdfConfigs.message = !!record?.coverMessage;
    pdfPreview.standardPdf = record?.isStandardPdf;
    return pdfPreview;
};

export const buildContextData = (record: any, refId: string) => {
    return {
        giftTypeSelection: {
            name: record?.isGeneric
                ? record?.isCustomHyc
                    ? GIFT_TYPE_SELECTION.CUSTOM_CARD
                    : GIFT_TYPE_SELECTION.HAPPY_YOU_CARD
                : GIFT_TYPE_SELECTION.SINGLE_CARD,
            slug: record?.brandSlug,
            value: record?.isGeneric
                ? record?.isCustomHyc
                    ? BRAND_CARD_TYPE.HAPPY_YOU_CUSTOM
                    : BRAND_CARD_TYPE.HAPPY_YOU_NORMAL
                : BRAND_CARD_TYPE.NON_GENERIC,
        },
        brandList: {
            data: {
                brandImage: record?.isGeneric ? "" : record?.brandSkinUrl,
                brandImageData: record?.isGeneric ? "" : record?.brandSkinUrl,
                slug: record?.brandSlug,
            },
            skinData: {
                node: {
                    cardImage:
                        record?.isGeneric && !record?.isCustomHyc
                            ? record?.brandSkinUrl
                            : "",
                    occasion: {
                        code: record?.greetingsHubOccasionCode || "",
                        name: record?.greetingsHubOccasionName || "",
                    },
                    skinCode: record?.isGeneric && !record?.isCustomHyc
                        ?
                        record?.brandSkinIdentifier
                        : "",
                },
            },
            slug: record?.brandSkinCode,
            value: record?.brandName,
            name:
                record?.isGeneric && !record?.isCustomHyc
                    ? record?.brandSkinCode
                    : record?.brandName,
        },
        customGiftCard: {
            cardImage:
                record?.isGeneric && record?.isCustomHyc
                    ? record?.brandSkinUrl
                    : "",
            name:
                record?.isGeneric && record?.isCustomHyc
                    ? "Custom Gift Card"
                    : "",
            referenceId: record?.isGeneric && record?.isCustomHyc
                ? record?.brandSkinIdentifier
                : "",
        },
        card: {
            brandCode: record?.brandCode,
            brandImage: record?.brandSkinUrl,
            brandName: record?.brandName,
            brandSlug: record?.brandSlug,
            purchaseType:
                record?.deliveryMethod === "digital"
                    ? CARD_PURCHASE_TYPE.EMAIL_SMS
                    : CARD_PURCHASE_TYPE.DOWNLOAD_PDF,
            referenceId: refId,
            formState: BRAND_FORM_STATE.EDIT,
            cardValue: record?.recipientList[0]?.amount,
            currencyCode: record?.recipientList[0]?.currency,
        },
    };
};
