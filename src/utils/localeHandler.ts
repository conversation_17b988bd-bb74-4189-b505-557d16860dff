import { getSiteConfig } from "@features/common/commonAPI";
/**
 * @method localeHandler
 */
const localeHandler = async (
  ipAddress: string,
  locale: string,
  locales: string[]
) => {
  // #. Get the locale & region info first
  // #. Not found get defaults from the site config
  const [localeCode, region, networkStatus] = await getSiteConfig(
    ipAddress,
    locale,
    locales
  );

  const data = [localeCode, region, networkStatus];

  return {
    data,
  };
};

export default localeHandler;