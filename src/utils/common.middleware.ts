import ipTracker from "./ipTracker";
import localeHandler from "./localeHandler";
import fetchAuthInfo from "./fetchAuthInfo";
import fetchCommonData from "./fetchCommonData";
// import setPageHeader from "./setPageHeader";
import { PLATFORM_TYPE } from "@constants/common";
import { getSiteConfig, guestLoginEnabled } from "@features/common/commonAPI";
import { fetchAtWorkSiteConfig } from "./fetchAtWorkSiteConfig";

/**
 * @method CommonPagesMiddleware
 * @description To hanlde common page methods and scenarions
 * Pages like: index, all-bands, brands, cart ...etc
 */
const commonMiddleware = async (params: any, omitCartDataCall = false) => {
  // #. Get required info from the page params
  const {
    res = {},
    req = {},
    locale = 'en',
    locales = ['en'],
    defaultLocale = 'en',
    resolvedUrl = '/',
  } = params || {};

  // #. Get the IP address
  const ipAddress: any = ipTracker(req);
  // #. Get the locale code, region, language resource and network status
  const [localeCode, region, languageReource, networkStatus, siteConfigData] =
    await getSiteConfig(ipAddress, locale, locales);
  // #. Get common data of common pages
  const commonDataResponse: any = await fetchCommonData(localeCode, region, ipAddress, siteConfigData);

  // storecode such as STAE,STSA
  const storeCode = commonDataResponse?.regionInfo?.node?.code;

  //#. Get the user auth info
  const authInfo: any = await fetchAuthInfo(req, localeCode, storeCode);

  // @work site config data  
  const { config: atWorkSiteConfig, sessionCookie } = await fetchAtWorkSiteConfig(locale, storeCode, ipAddress, req?.headers?.cookie)
  // Destructure the authInfo object
  const {
    refreshToken,
    idToken,
    accessToken,
    isUserSignedIn,
    displayName,
    userAttributes,
  } = authInfo;

  // #. Handle the invalid responses
  if (!commonDataResponse.data) {
    return commonDataResponse;
  }

  //#. Set the page header info
  // setPageHeader(res, localeCode, region, commonDataResponse.regionInfo);

  // #. Return all common data
  return {
    data: {
      ipAddress,
      localeCode,
      region,
      languageReource,
      refreshToken,
      idToken,
      isUserSignedIn,
      accessToken,
      userAttributes,
      displayName,
      commonData: commonDataResponse?.data?.data,
      activeRegion: commonDataResponse?.regionInfo?.node,
      atWorkSiteConfig: atWorkSiteConfig?.data?.siteConfigurations,
      sessionCookie
    },
  };
};

export default commonMiddleware;
