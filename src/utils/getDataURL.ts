  export function convertBufferToDataURL(buffer: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const dataURL = reader.result;
        resolve(dataURL);
      };
      reader.onerror = (error) => {
        console.error("Error reading image:", error);
        reject(error);
      };
      reader.readAsDataURL(buffer);
    });
  }