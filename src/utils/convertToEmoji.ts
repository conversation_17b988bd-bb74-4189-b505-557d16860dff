export const convertToEmoji = (unicode: string) => {
    const codePoint = parseInt(unicode.replace('U+', ''), 16);

    // Check if the code point is a valid number and within the valid Unicode range
    if (!isNaN(codePoint) && codePoint >= 0 && codePoint <= 0x10FFFF) {
      return String.fromCodePoint(codePoint);
    } else {
      console.error(`Invalid code point: ${unicode}`);
      return '';
    }
};
