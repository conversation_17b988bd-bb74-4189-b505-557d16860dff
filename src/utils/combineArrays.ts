/**
 * @function combineData
 * @description Combines two arrays of data, merging items based on their brand codes.
 * @param {any[]} dataArray
 * @param {any[]} offerDataArray
 * @returns {any[]}
 * @throws {Error}
 */
export const combineData = (dataArray: any[], offerDataArray: any[]) => {
  // Validate input types
  if (!Array.isArray(dataArray) || !Array.isArray(offerDataArray)) {
    console.log("dataArray and offerDataArray must be arrays.");
    return [];
  }

  // Check if both arrays are empty
  if (dataArray.length === 0 && offerDataArray.length === 0) {
    return [];
  }
  try {
    const offerMap = new Map(
      offerDataArray.map((offer) => [
        offer?.node?.brand?.code || offer?.code || offer?.node?.code,
        offer?.node?.brand || offer,
      ])
    );

    const combinedData = dataArray.map((item) => {
      const code = item?.node?.brand?.code || item?.code || item?.node?.code;
      const offer = offerMap.get(code);
      if (item?.code) {
        return {
          ...item,
          ...offer,
        };
      } else if (item?.node?.code) {
        return {
          node: {
            ...item?.node,
            ...offer?.node,
          },
        };
      } else
        return {
          node: {
            brand: offer
              ? { ...item?.node?.brand, ...offer }
              : item?.node?.brand,
          },
        };
    });

    return combinedData;
  } catch (error) {
    console.error("Error combining data:", error);
  }
};
