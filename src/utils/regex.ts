// #. Check string is alpha numeric or not
export const alphaNumericReg = /[^a-zA-Z0-9 ]/;
export const isS3Regex = /^https:\/\/[a-zA-Z0-9\-\.]+\.s3\.[a-zA-Z0-9\-\.]+\.amazonaws\.com\//;
export const extractEmbeddedId = /watch\?v=([^&]+)/;
//at work regex 
export const alphabetsRegex = /^[A-Za-z ]*$/;;
export const alphabetsArabicRegex = /^[A-Za-z\u0600-\u06FF ]*$/;
export const alphaNumericSpecialCharRegex = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?\s]*$/;;
export const numberRegex = /^\d{2,15}$/;
export const alphanumericRegex = /^[a-zA-Z0-9]{2,15}$/;
export const emojiRegex = /[\uD83C-\uDBFF\uDC00-\uDFFF]+/g;
export const forbiddenTagsRegex = /<(script|a|applet|style|link|embed|listing|meta|noscript|object|plaintext|xmp|iframe|svg|img)/i;
export const emojiRegex2 = /(?:[\u203C-\u3299\u2600-\u26FF\u2700-\u27BF]|\uD83C[\uDC00-\uDFFF\uDDE6-\uDDFF]|\uD83D[\uDC00-\uDFFF]|\uD83E[\uDD00-\uDFFF]|\u200D|\uFE0F)/;