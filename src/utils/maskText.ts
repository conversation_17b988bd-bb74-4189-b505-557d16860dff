/**
 * @method maskText
 * @param text Text to be masked
 * @param start Number of character to be shown at start 
 * @param end Number of character to be shown at end 
 * @param maskIcon Mask icon
 * @returns masked text
 */
export default function maskText(
  text: string,
  start: number,
  end: number,
  maskIcon: string
): string {
  const first = text.substring(0, start);
  const last = text.substring(text.length - end);

  const mask = text.substring(start, text.length - end).replace(/\d/g, maskIcon);
  return first + mask + last;
}
