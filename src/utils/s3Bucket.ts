import { Auth, Storage } from "aws-amplify";
import { v4 as uuidv4 } from "uuid";

const S3Bucket = {
  /**
   * @method put
   * @param fileName
   * @param cotent
   * @param options
   * @returns
   */
  put: async (fileName: string = "", cotent: any, options: any = {}) => {
    // #. Get new uuid
    const uuid = uuidv4();

    // #. Rename the file with uuid
    return await Storage.put(
      `${uuid}-${fileName.replace(/ /g, "-")}`,
      cotent,
      options
    );
  },

  /**
   * @method fastPut
   * @param fileName
   * @param cotent
   * @param options
   * @returns
   */
  fastPut: async (fileName: string = "", cotent: any, options: any = {}) => {
    try {
      const base64Data = cotent.replace(/^data:image\/\w+;base64,/, "");
      const buffer = Buffer.from(base64Data, "base64");
      // #. Get new uuid
      const uuid = uuidv4();
      const key = `${uuid}-${fileName.replace(/ /g, "-")}`;
      // #. Rename the file with uuid
      await Storage.put(key, buffer, options);
      return key;
    } catch (error) {
      console.error("Error uploading file:", error);
      return ""
    }
  },

  /**
   * @method get
   * @param fileName
   * @param options
   * @returns
   */
  get: async (fileName: string, options: any = {}) => {
    return await Storage.get(fileName, options);
  },

  getImageBuffer : async (fileName:any) => {
  try {
    const image:any = await Storage.get(fileName, { download: true });
    return image.Body;
  } catch (error) {
    throw error;
  }
},

  /**
   * @method remove
   * @param fileName
   * @param options
   * @returns
   */
  remove: async (fileName: string, options: any = {}) => {
    return await Storage.remove(fileName, options);
  },
};

// #. Auth current user method
// #. Auth is mandatory to S3Bucket upload
// #. To avoid tree-shaking this declaratoin is unavoidable
const currentUserInfo = Auth.currentUserInfo;
export { currentUserInfo };

export default S3Bucket;
