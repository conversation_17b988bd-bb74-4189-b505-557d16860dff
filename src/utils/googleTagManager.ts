/**
 * @method GoogleTagManager
 * @description A wrapper to manage the ecommerce GTM
 * @dependencies Check _document.tsx and GTM_ID availability
 */

import { HAS_WINDOWS_REF } from "./hasWindowRef";

export type PushEvents =
  | "@work_start"
  | "brand_type_select"
  | "skin_select"
  | "denomination"
  | "single_brand_select"
  | "email_delivery_personalization"
  | "pdf_delivery_personalization"
  | "order_summary"
  | "@work_add_to_cart"
  | "@work_checkout"
  | "@work_purchase"
  | "@work_login"
  | "@work_sign_up"
  | "@work_email_greeting_selected"
  | "@work_email_media"
  | "@work_email_message"
  | "@work_pdf_logo"
  | "@work_pdf_greeting_cover"
  | "@work_pdf_message"
  | "@work_cart_denomination_remove"
  | "@work_cart_item_remove"
  | "@work_cart_recipient_remove"
  ;

const GoogleTagManager = () => {
  const defReturn = { push: () => {} };

  // #. Check window obj available or not
  if (!HAS_WINDOWS_REF) {
    console.warn("Google Tag Manager only work on browser!!");
    return defReturn;
  }

  // #. Check GTM scripts loaded or not
  const dataLayer = (window as any)?.dataLayer;
  if (!dataLayer) {
    console.warn("Google Tag Manager not loaded yet!!");
    return defReturn;
  }

  /**
   * @method clear
   * @description To clear the previous ecommerce object from the dataLayer
   */
  const clear = () => {
    dataLayer.push({ ecommerce: null });
  };

  /**
   * @method push
   * @description To measure the ecommerce activities
   * @params
   */
  const push = (
    eventName: PushEvents,
    measure: any,
    ecommerce: boolean = true
  ) => {
    // #. Clear the previous obj
    clear();

    let eventObj: any = { event: eventName };

    eventObj = ecommerce
      ? { ...eventObj, ecommerce: measure }
      : { ...eventObj, ...measure };

    // #. Send GTM to the event measuremnts
    dataLayer.push(eventObj);
  };

  // #. Return the public items
  return {
    push,
  };
};

export default GoogleTagManager;
