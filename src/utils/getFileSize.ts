/**
 * @method fileSizeByMB
 * @param fileSize
 * @returns
 */
export const fileSizeByMB = (fileSize: number) => {
  return fileSize / 1024 / 1024;
};

/**
 * @method getVideoDuration
 * @param file
 */
export const getVideoDuration = (file: any) => {
  const video = document.createElement("video");
  const fileURL = URL.createObjectURL(file);
  return new Promise((resolve, reject) => {
    video.src = fileURL;
    // wait for duration to change from NaN to the actual duration
    video.ondurationchange = function () {
      const duration = (this as any).duration;
      if (!isNaN(duration)) {
        resolve(duration);
      }
    };
  });
};
