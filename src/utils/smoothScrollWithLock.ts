let scrollAnimationFrame: number | null = null;

export function smoothScrollTo(to: number, duration = 600) {
  if (scrollAnimationFrame) {
    cancelAnimationFrame(scrollAnimationFrame);
    scrollAnimationFrame = null;
  }

  const start = window.scrollY;
  const change = to - start;
  const startTime = performance.now();

  function easeInOutQuad(t: number) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  function animateScroll(currentTime: number) {
    const timeElapsed = currentTime - startTime;
    const progress = Math.min(timeElapsed / duration, 1);
    window.scrollTo(0, start + change * easeInOutQuad(progress));

    if (progress < 1) {
      scrollAnimationFrame = requestAnimationFrame(animateScroll);
    } else {
      scrollAnimationFrame = null;
    }
  }

  scrollAnimationFrame = requestAnimationFrame(animateScroll);
}

export function cancelOngoingScroll() {
  if (scrollAnimationFrame) {
    cancelAnimationFrame(scrollAnimationFrame);
    scrollAnimationFrame = null;
  }
}