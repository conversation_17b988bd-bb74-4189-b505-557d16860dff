/**
 * @method getLocaleRegion
 * @description Access region and locale from (window.location.pathname)
 * @param pathname
 */
const getLocaleRegion = (pathname: any) => {

  // Split the pathname by '/'
  const segments = pathname.split('/');

  // Find the segment that matches the locale-region pattern
  for (const segment of segments) {
    // Locale-region patterns are usually two parts separated by a dash (e.g., 'en-ae')
    if (segment.match(/^[a-z]{2}-[a-z]{2}$/i)) {
      return segment;
    }
  }

  // If no match is found, return null or an appropriate default value
  return null;
};

export default getLocaleRegion;