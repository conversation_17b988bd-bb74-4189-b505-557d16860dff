import { CARD_PURCHASE_TYPE } from "@features/brand/constants/brand.constant";

export const isValidPdf = (purchaseType, pdfPreview) => {
    if (purchaseType !== CARD_PURCHASE_TYPE.DOWNLOAD_PDF) {
        return false;
    }
    if (pdfPreview?.standardPdf) {
        return true;
    } else {
        if (!pdfPreview?.greetings?.previewUrl) {
            return false;
        }
        if (pdfPreview?.pdfConfigs?.logo) {
            if (!pdfPreview?.logo?.previewUrl) {
                return false;
            }
        }
        if (pdfPreview?.pdfConfigs?.message) {
            if (!pdfPreview?.message?.text) {
                return false;
            }
        }
    }
    return true;
};