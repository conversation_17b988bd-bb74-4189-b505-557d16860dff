import { fetchMostPopularCards, getRecommendedBrand } from "@features/brand/brandAPI";

export const recommendedCardData = (async ({
  locale,
  store,
  country_Code,
  sortType,
  first,
}:any) => {
  // #. Get recommended gift cards
  const recommendedBrands: any = await getRecommendedBrand(locale, store);

  // #. Get most popular  gift cards
  const mostPopularBrands: any =
    !recommendedBrands?.data ||
    recommendedBrands?.data?.recommendedBrands?.edges?.length < 1
      ? await fetchMostPopularCards({
          locale,
          country_Code,
          sortType,
          first,
        })
      : "";

  const recommendedGiftCardsData =
    recommendedBrands?.data?.recommendedBrands?.edges?.length > 0
      ? recommendedBrands?.data?.recommendedBrands?.edges
      : mostPopularBrands?.data?.brandsByCategory?.edges;

  return recommendedGiftCardsData;
});