// import {
//   PA<PERSON>_CACHE_CONFIG,
//   YGAG_REGION_CODE_COOKIE,
//   YGAG_REGION_COOKIE,
// } from "@constants/common";

// /**
//  * @method setPageHeader
//  */
// const setPageHeader = async (
//   response: any,
//   locale: string,
//   region: string,
//   regionInfo?: any
// ) => {
//   response?.setHeader("Cache-Control", PAGE_CACHE_CONFIG);

//   // #. Set the values to cookie
//   response?.setHeader(
//     "set-cookie",
//     `${YGAG_REGION_COOKIE}=${`${locale}-${region}`}; path=/; samesite=lax;`
//   );

//   if (regionInfo) {
//     response?.setHeader(
//       "set-cookie",
//       `${YGAG_REGION_CODE_COOKIE}=${`${regionInfo?.node?.code}`}; path=/; samesite=lax;`
//     );
//   }

//   return true;
// };

// export default setPageHeader;
