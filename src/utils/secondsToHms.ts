/**
 * @method secondsToHms
 * @param number Duration in seconds
 * @returns hour, minute, second
 */

export default function secondsToHms(d: number) {
  d = Number(d);
  const h = Math.floor(d / 3600);
  const m = Math.floor((d % 3600) / 60);
  const s = Math.floor((d % 3600) % 60);

  const hour = h.toString().length === 1 ? `0${h}`: `${h}`;
  const minute = m.toString().length === 1 ? `0${m}`: `${m}`;
  const second = s.toString().length === 1 ? `0${s}`: `${s}`;

  return { hour, minute, second };
}
