export const INITIAL_STATE = {
  DELIVERY_KEYS: {
    card: {
      personalisation: {
        video: { uuid: "", file: "", fileName: "", medias: { mp4: '' } },
        gif: { id: "", url: "" },
        photo: { file: "", fileName: "" },
      },
      giftMessage: {
        message: "",
        backgroundColor: "#EB6B6B",
        fontSize: 18,
        fontFamily: "Poppins",
      },
      greetingCover: {
        language: "",
        occasion: { code: "", name: "" },
        referenceCode: "",
        staticGifPath: "",
        coverType: "",
        filePath: "",
      },
    },
    pImageKey: "",
    videoUrl: "",
    customeGreetings: {
      previewUrl: "",
      file: "",
      name: "",
    },
  },

  PDF_PREVIEW: {
    standardPdf: false,
    pdfConfigs: {
      logo: true,
      message: true,
    },
    logo: {
      file: "",
      urlType: "",
      name: "",
      previewUrl: "",
    },
    greetings: {
      file: "",
      urlType: "",
      name: "",
      previewUrl: "",
    },
    message: {
      text: "",
    },
    messageStyle: {
      color: "#000",
      fontFamily: "Roboto",
      textAlign: "center",
      fontStyle: "",
      fontWeight: "",
      textDecoration: "",
    },
    name: "Personalization",
  },
};

export const AT_WORK_INFO_CARDS = [
    {
      title: "assistedSales",
      description:
        "assistedSalesMessage",
      image: "/images/assisted-sales.svg",
      imageWidth: 84,
      imageHeight: 72,
      key:'assisted-sales'
    },
    {
      title: "bulkOrderAndDelivery",
      description:
        "bulkOrderMessage",
      image: "/images/bulk-order.svg",
      imageWidth: 84,
      imageHeight: 72,
      key:'bulk-order'
    },
    {
      title: "customDesign",
      description: "customDesignMessage",
      image: "/images/custom-design.svg",
      imageWidth: 202,
      imageHeight: 144,
      key:'custom-design'
    },
  ];
  