import { i18nRouter } from 'next-i18n-router';
import i18nConfig from '../i18nConfig';
import { NextRequest, NextResponse } from 'next/server';
import IPHelper from './utils/ipHelper';
import { basePath, domain } from '@constants/envVariables';
import localeHandler from '@utils/localeHandler';

const PUBLIC_FILE = /\.(.*)$/;

export default async function middleware(req: NextRequest) {
    const response = NextResponse.next();
    const { pathname, search } = req.nextUrl;
    // Skip internal or static paths
    if (
        pathname.startsWith('/_next') ||
        pathname.includes('/api/') ||
        pathname.startsWith('/static') ||
        pathname.startsWith('/health') ||
        pathname === '/favicon.ico' ||
        PUBLIC_FILE.test(pathname)
    ) {
        return;
    }

    // #. Extract the current locale from the path
    const cookieLocale = req.cookies.get('YGAG_REGION_COOKIE')?.value || '';
    // const rewardsRefreshToken = req.cookies.get(EGG_REWARDS_REFRESH_TOKEN)?.value || '';
    const dynamicLocale = pathname.split('/')[1];
    const locale = cookieLocale;
    const locales = i18nConfig.locales;
    const formatPattern = /^[a-z]{2}-[a-z]{2}$/;
    const isFormatValid = formatPattern.test(dynamicLocale);

    // #. Check if the extracted locale is in the i18nConfig's locales list
    const isLocaleSupported = i18nConfig.locales.includes(locale);
    const matchingLocale = i18nConfig.locales.find((loc) => loc === locale);

    // Retrieve the IP address using the IPHelper
    const { getIPFromRequest } = IPHelper();
    const ipAddress = getIPFromRequest(req);

    // #. Handle the primary locale and region info
    let data: string[] = [''];
    if (!cookieLocale && !isFormatValid) {
        const { data: localeData } = await localeHandler(
            ipAddress,
            locale,
            locales
        );
        data = localeData as string[];
    }
    const [localeCode, regionCode, networkStatus] = data;

    // Redirect only if locale doesnot match the format xx-xx
    if (!isFormatValid) {
        const locale = cookieLocale ? cookieLocale : `${localeCode}-${regionCode}`;
        return NextResponse.redirect(
            new URL(
                `${basePath}/${locale}${pathname}${search}`,
                req.url
            )
        );
    }

    // #. Set cookie when user edits the locale
    if (
        dynamicLocale !== cookieLocale &&
        i18nConfig.locales.includes(dynamicLocale)
    ) {

        // #. Set new YGAG_REGION_COOKIE cookie
        response.cookies.set('YGAG_REGION_COOKIE', dynamicLocale, { domain: domain });
        return response;
    }

    // Redirect to page not found if dynamicLocale is not in i18nConfig.locales
    if (!i18nConfig.locales.includes(dynamicLocale)) {
        return NextResponse.error();
    }

    return i18nRouter(req, i18nConfig);
}

// applies this middleware only to files in the app directory
export const config = {
    // matcher: '/((?!api|static|.*\\..*|_next|health|favicon.ico).*)',
    unstable_allowDynamic: [
        '**/node_modules/lodash/_root.js', // use a glob to allow anything in the function-bind 3rd party module
    ],
};
