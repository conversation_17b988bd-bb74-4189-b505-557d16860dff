import { useEffect } from "react";
import { useAppDispatch } from "@redux/hooks";
import { setTokenInfo, ipAddress } from "@features/common/commonSlice";

interface AppDataDispatcherProps {
  userTokens: any;
  ipPayload: any;
}

export default function AppDataDispatcher({
  userTokens,
  ipPayload,
}: AppDataDispatcherProps) {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(setTokenInfo(userTokens));
    if (ipPayload) {
      dispatch(ipAddress(ipPayload));
    }
  }, [dispatch, userTokens, ipPayload]);
  return null;
}
