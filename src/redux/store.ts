// redux store setup
import { configureStore, ThunkAction, Action, AnyAction } from "@reduxjs/toolkit";
import { combineReducers } from "redux";
import {
    nextReduxCookieMiddleware,
} from "next-redux-cookie-wrapper";
import commonReducer from "@features/common/commonSlice";
import { HYDRATE } from 'next-redux-wrapper';

export const combinedReducer = combineReducers({
    common: commonReducer,
});

const reducer = (
    state: ReturnType<typeof combinedReducer> | undefined,
    action: AnyAction
) => {
    if (action.type === HYDRATE) {
        const nextState = {
            ...state, // use previous state
            ...action.payload, // apply delta from hydration
        };
        return nextState;
    } else {
        return combinedReducer(state, action);
    }
};

export const store = configureStore({
    reducer: combinedReducer,
    devTools: process.env.NODE_ENV !== 'production',
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
        }).prepend(
            nextReduxCookieMiddleware({
                subtrees: [],
            })
        ),
});

export const makeStore = () => {
    return store;
};

export type AppStore = ReturnType<typeof makeStore>;
export type AppState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];

export type AppThunk<ReturnType = void> = ThunkAction<
    ReturnType,
    AppState,
    unknown,
    Action<string>
>;

