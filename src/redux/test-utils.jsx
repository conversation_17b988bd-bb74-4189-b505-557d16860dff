// test-utils.jsx
import React from "react";
import { render as rtlRender } from "@testing-library/react";
import { configureStore } from "@reduxjs/toolkit";
import { Provider } from "react-redux";
// Import your own reducer
import commonReducer from "@features/common/commonSlice";
import quickViewReducer from "@features/common/quickView/quickviewSlice";
import { AppRouterContext } from "@features/common/router.context";
import { allBrandsReducers } from "@features/allBrands/allBrandsSlice";

const DEFAULT_ROUTER_PROVIDER_VALUE = {
  router: {
    asPath: "/all-brands/catagory/most-popular",
    locale: "en",
    query: {
      slug: ["category", "most-popular", 1],
      region: "AE",
    },
    push: jest.fn(),
    back: jest.fn(),
    events: {
      on: jest.fn(),
      off: jest.fn(),
    },
    beforePopState: jest.fn(() => null),
  },
  redirect: jest.fn(),
  state: {
    region: "ae",
    regionUrl: "/ae",
    allBrandsPageDefaultUrl: "/ae/all-brands/category/most-popular/",
    locale: "en",
    allRegions: [],
    activeRegion: {
      node: {
        code: "AE",
        name: "UAE",
      },
    },
  },
  dispatch: jest.fn(),
  utils: {
    setLocaleCookie: jest.fn(),
    getLocaleCookie: jest.fn(),
    goToAllBrands: jest.fn(),
  },
};

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key) => key }),
}));

jest.mock("next/router", () => ({
  push: jest.fn(),
  back: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
  },
  beforePopState: jest.fn(() => null),
  useRouter: () => ({
    locale: "en",
    push: jest.fn(),
    query: {
      slug: ["category", "most-popular", 1],
    },
  }),
}));

function render(
  ui,
  {
    preloadedState = {},
    store = configureStore({
      reducer: {
        common: commonReducer,
        quickView: quickViewReducer,
        allBrands: allBrandsReducers,
      },
      preloadedState,
    }),
    ...renderOptions
  } = {},
  routerProviderValue = {}
) {
  function Wrapper({ children }) {
    if (store) {
      return (
        <AppRouterContext.Provider
          value={{ ...DEFAULT_ROUTER_PROVIDER_VALUE, ...routerProviderValue }}
        >
          <Provider store={store}>{children}</Provider>
        </AppRouterContext.Provider>
      );
    } else {
      return (
        <AppRouterContext.Provider
          value={{ ...DEFAULT_ROUTER_PROVIDER_VALUE, ...routerProviderValue }}
        >
          {children}
        </AppRouterContext.Provider>
      );
    }
  }

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
}

// re-export everything
export * from "@testing-library/react";
// override render method
export { render };
