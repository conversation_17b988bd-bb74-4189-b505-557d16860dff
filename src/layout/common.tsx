"use client";

import React from "react";
import Header from "@features/common/header/Header";
import useApp from "@features/common/common.context";

type CommonLayoutProps = {
  children: React.ReactNode;
};

const CommonLayout = ({ children }: CommonLayoutProps) => {
  const { state } = useApp();
  const activeRegion = state?.activeRegion;
  const headerData = state?.commonData?.headers || [];
  const allRegions = state?.commonData?.stores || [];
  const localeCode = state?.localeCode ||  "";
  const isUserSignedIn = state?.isUserSignedIn ||  false;
  const wallet = state?.userAttributes?.wallet;

  return (
    <main className="home">
      <Header
        headerData={headerData}
        activeRegion={activeRegion}
        allRegions={allRegions}
        localeCode={localeCode}
        isUserSignedIn={isUserSignedIn}
        wallet={wallet}
      />

      <section className="home-page">{children}</section>
    </main>
  );
};

export default CommonLayout;
