import { BANNER_TYPE } from "@constants/common";
import { ReactNode } from "react";

export interface DropDownMenuIterface {
  children: ReactNode;
  maxWidth?: number;
  dropDown: boolean;
  className?: string;
  hasMultipleLanguages?: boolean;
}

export interface SelectedRegion {
  region: string;
  flag: string;
  name: string;
}

// #. Quick view clicked event action method type
export type QuickViewClickAction = () => void;

/// to be removed
export interface CardSlider {
  sliderType: "category" | "gift";
  moreLink: string;
  title: string;
  cardsList: any[];
  slidesPerView?: number;
  spaceBetween?: number;
  mdSlidesPerView?: number;
  mdSpaceBetween?: number;
  lgSlidesPerView?: number;
  lgSpaceBetween?: number;
}
//////////////////////////////

export interface ButtonInterface {
  theme?:
  | "primary"
  | "secondary"
  | "light-shadow"
  | "light-border"
  | "semi-dark-grey"
  | "transparent"
  | "at-work-primary"
  | "at-work-primary-outlined"
  | "at-work-secondary"
  | "at-work-secondary-outlined"
  | "dark-shadow";
  className?: string;
  action?: (ev: React.MouseEvent<HTMLButtonElement>) => void;
  children?: ReactNode;
  borderTheme?: "purple" | "black";
  arrow?: "arrow-forward";
  id?: string;
  attribues?: any;
  icon?: boolean;
  wrapperClassName?: string;
  btnRef?: any;
  hasCornerIcon?: boolean;
  disabled?: boolean;
  isChatBot?: boolean;
  isFixedBottom?: boolean;
}

//Gift card interface
export interface GiftCardSlider {
  cardName: string;
  image: string;
  category: string;
  label?: string;
  special?: string;
  offerTag?: string;
  highlight?: string;
  url: string;
  itemSlider?: boolean;
  denomination?: string;
  hidden?: boolean;
}

//category card interface
export interface CategoryCardSlider {
  title: string;
  icon: string;
  url: string;
  hidden?: boolean;
}

// #. Quick view clicked event action method type
export type QuickViewPanelCloseAction = () => void;

// #. Data model for the quick view panel
export interface QuickViewPanel {
  open: boolean;
  anchor?: "left" | "right" | "top" | "bottom" | undefined;
  onClose?: QuickViewPanelCloseAction;
}

export interface QuickViewHeaderInterface {
  onClose?: QuickViewPanelCloseAction;
  brandName: string | undefined;
  brandLabel: string | undefined;
  logoImageData: string | undefined;
  brandID?: string | undefined;
}

// #. Interface for the search component

export interface Search {
  children?: ReactNode;
  cssWrapperClass?: string;
  autocomplete?: boolean;
  autocompleteOptions?: Array<SearchAutocompleteOption>;
  onAutocompleteItemSelected?: (value?: string) => void;
  clearButton?: boolean;
  onSearchChange?: (value?: string) => void;
  searchText?: string;
  isOnLoad?: boolean;
  placeHolder?: string | undefined;
  brandIncluded?: boolean;
  onListOpened?: (isOpened: boolean) => void;
  listOpened?: boolean;
}

// #. Interface for the promotional banner
export interface PromotionalBannerData {
  fullWidth: boolean;
  title: string;
  image: string;
  startDate: Date;
  endDate: Date;
}

// Download Data
export interface DowloadData {
  data: {
    image: string;
    title: string;
    subTitle: string;
    list: string[];
    appStoreImage: string;
    playStoreImage: string;
  };
}

// #. Testimonial video node
export interface TestimonialVideoNode {
  node: {
    link: string;
  };
}

// #. Testimonial video
export interface TestimonialVideo {
  edges: TestimonialVideoNode[];
}


//#. Testimonial review node
export interface TestimonialReviewNode {
  node: {
    description: string;
    customerName: string;
    happinessIndex: number;
    testimonialOrder: {
      edges: {
        node: {
          productCode: string;
          productName: string;
          dateTime: Date;
          country: {
            code: string;
            codeThreeLetter: string;
          };
        };
      }[];
    };
  };
}

// #. Testimonial review
export interface TestimonialReview {
  edges: TestimonialReviewNode[];
}

// #. Testimonials
export interface Testimonials {
  data: {
    testimonials: {
      edges: {
        node: {
          heading: string;
          testimonialVideo: TestimonialVideo;
          testimonialReview: TestimonialReview;
        };
      }[];
    };
  };
}
export interface SearchData {
  title: string;
  category: string;
  image: string;
}

// Minor Header Data
export interface MinorHeaderDataNodes {
  node: {
    backgroundImage: string;
    headerType: {
      code: string;
    };
    tagLine: string;
  };
}

export interface MinorHeaderDataHeaders {
  headers: {
    edges: MinorHeaderDataNodes[];
  };
}
export interface MinorHeaderData {
  data: MinorHeaderDataHeaders;
}

export interface MajorHeaderData {
  data: {
    headers: {
      edges: headerNodeInterface[];
    };
  };
}

// Site config data
export interface HelpDeskItem {
  phone: string;
  mobile: string;
  timing: string;
  country: {
    code: string;
    name: string;
    flagImage: string;
  };
}
export interface HelpDeskNode {
  node: HelpDeskItem;
}
export interface CaptchaConfig {
  hasCaptchaEnabled: boolean;
  actionName: string;
}
export interface SiteConfigDataNode {
  node: {
    defaultCountry: string;
    defaultLanguage: string;
    defaultStoreCode: string;
    email: string;
    chatKey: string;
    chatEnabled: boolean;
    clevertapAccountId: string;
    recaptchaSiteKey: string;
    captchaConfig: CaptchaConfig[];
    homepageSiteMeta: {
      id: string;
      title?: string;
      urlPattern: string;
      description: string;
      keywords: string;
    };
    helpDesk: {
      edges: HelpDeskNode[];
    };
    languages: Array<string>;
  };
}
export interface SiteConfigData {
  data: {
    siteConfigs: {
      edges: SiteConfigDataNode[];
    };
  };
}

export interface MetaDataInterface {
  id?: string;
  title?: string;
  urlPattern?: string;
  description?: string;
  keywords?: string;
}

// #. Data node of the promotion banner popup
export interface PromotionDataNode {
  backgroundColor: string;
  url: string;
  ctaText: string;
  title: string;
  subTitle: string;
  isPopup: boolean;
  description: string;
  image: string;
  __typename: string;
}

// #. Data format of the promotion banner popup
export interface PromotionData {
  data: {
    promotion: PromotionDataNode;
  };
}
// stores data

export interface StoresLanguages {
  node: {
    name: string;
  };
}
export interface StoresItem {
  node: {
    code: string;
    name: string;
    timezone: string;
    country: {
      code: string;
      flagImage: string;
      codeThreeLetter: string;
      name: string;
      languages: {
        edges: StoresLanguages[];
      };
    };
  };
}
// stores data
export interface StoresData {
  data: {
    stores: {
      edges: StoresItem[];
    };
  };
}

// Main Banner
export interface BannerItems {
  name: string;
  image: string;
  url: string;
  isVideo?: any;
  bannerDescription: string;
  borderColor: string;
  imageWebp?: string;
  bannerCategory: {
    code: BANNER_TYPE;
    slidingTimeInterval?: null | number;
    isSliding: boolean;
    floatingType?: "HORIZONTAL" | "VERTICAL" | null;
  };
  isPopup: boolean;
}
export interface BannerInterface {
  data: {
    banners: BannerItems[];
  };
}

// Side Banner
export interface SideBannerItems {
  image: string;
  url: string;
}
export interface SideBanner {
  data: {
    banners: SideBannerItems[];
  };
}

// Rewards Interface
export interface RewardsItemInterface {
  image: string;
  bannerCategory: {
    code: BANNER_TYPE;
  };
  url: string;
}

export interface RewardsInterface {
  data: {
    banners: RewardsItemInterface[];
  };
}

export interface PressRoomItemInterface {
  name: string;
  pressUrl: string;
  pressLogo: string;
  code: string;
  orderNumber: number;
}
export interface PressRoomInterface {
  data: {
    pressRooms: PressRoomItemInterface[];
  };
}

// Footer app banner
export interface AppBannerInterface {
  itemLabel?: string | null;
  itemImage: string;
  itemName: string;
  itemUrl: string;
  itemImageWebp?: string;
  id?: string;
}
// Footer Contact Interface
export interface ContactInterface {
  email: string;
  helpDesk: {
    edges: HelpDeskNode[];
  };
}
// Footer Stores Interface
export interface FooterStoresInterface {
  storeLink: string;
  store: {
    country: {
      name: string;
    };
  };
}
// Footer payment partners Interface
export interface PaymentPartnersNodeInterface {
  node: {
    name: string;
    logo: string;
    code: string;
  };
}
export interface PaymentPartnersInterface {
  edges: PaymentPartnersNodeInterface[];
}
// Footer Social media Interface

// Footer SubMenu Interface

export interface SubMenuNodeInterface {
  node: {
    itemLabel: string;
    itemName: string;
    itemUrl: string | null;
    itemImage?: string;
    itemIcon?: string;
    itemCode?: string | null;
  };
}
export interface SubMenuInterface {
  itemLabel: string;
  itemName: string;
  itemUrl: string | null;
  itemCode?: string | null;
  itemAlignment?: string | null;
  children: {
    edges: SubMenuNodeInterface[];
  };
}

// Footer Item

export interface FooterItemInterface {
  appBanner: AppBannerInterface[];
  contact: ContactInterface;
  copyrightNotice: string;
  footerStores: FooterStoresInterface[];
  logo: string;
  logoWebp?: string;
  paymentPartners: PaymentPartnersInterface;
  subMenu: SubMenuInterface[];
  tagLine: string;
}

// Aside menu

export interface AsideMenu {
  heading: string;
  menu: {
    name: string;
    url: string;
    value: string;
    image?: string | undefined;
  }[];
}

// Footer data
export interface FooterInterface {
  data: {
    footer: FooterItemInterface;
  };
}

// Blogs data

export interface BlogsItemInterface {
  node: {
    title: string;
    image: string;
    url: string;
  };
}
export interface BlogsInterface {
  data: {
    blogs: {
      edges: BlogsItemInterface[];
    };
  };
}

// Download app

export interface DownloadTextNodeInterface {
  node: {
    id: string;
    text: string;
  };
}
export interface DownloadAppDataInterface {
  downloadApp: [
    {
      appBanner: AppBannerInterface[];
      bannerImage: string;
      text: {
        edges: DownloadTextNodeInterface[];
      };
      title: string;
    }
  ];
}
export interface DownloadAppInterface {
  data: DownloadAppDataInterface;
}

export interface DownloadAppPopupInterface {
  downloadApp: [
    {
      appBanner: AppBannerInterface[];
      appDownloadQrcode: string;
      bannerImage: string;
      bannerImageAr: string;
      text: {
        edges: DownloadTextNodeInterface[];
      };
      title: string;
    }
  ];
}

// Signature interface

export interface SignatureSiteConfigInterface {
  node: {
    signatureSubtitle: string;
    signatureTitle: string;
  };
}
export interface SignatureInterface {
  data: {
    siteConfigs: {
      edges: SignatureSiteConfigInterface[];
    };
  };
}

export interface HappyCardBrand {
  brand: {
    code: string;
    name: string;
    brandImageData: string;
    primaryCategory: {
      id: string;
      name: string;
    };
  };
}

export interface HappyCardRedeemableBrands {
  totalCount: number;
  edges: {
    node: HappyCardBrand;
  }[];
}
export interface HappyCard {
  title: string;
  subTitle: string;
  description: string;
  cardImage: string;
  url: string;
  brand: {
    brandImageData: string;
  };
  redeemableBrands: HappyCardRedeemableBrands;
}

export interface HappyCardData {
  data: {
    happyCard: HappyCard;
  };
}

// #. News letter response data model
export interface NewsletterSubscriptionData {
  emailSubscription: {
    subscription: {
      emailAddress: string;
    };
  };
}

// #. News letter data
export interface NewsletterConfigData {
  data: {
    newsletterConfiguration: {
      title: string;
      subTitle: string;
      buttonName: string;
    };
  };
}

// #. News letter response data model
export interface GetDownloadLink {
  downloadAppRequest: {
    downloadAppRequest: {
      deliveryStatus: string;
    };
  };
}

// header interface

export interface headerNodeInterface {
  node: {
    backgroundImage: string;
    eGiftCards_SeoName?: string;
    gamingCards_SeoName?: string;
    headerType: {
      code: string;
    };
    tagLine: string;
    logo?: string;
    logoWebp?: string;
  };
}
export interface headerInterface {
  edges: headerNodeInterface[];
}

// Site config data
export interface SiteConfigNode {
  node: {
    defaultCountry: string;
    defaultLanguage: string;
    defaultStoreCode: string;
    signatureSubtitle: string;
    signatureTitle: string;
    email: string;
    clevertapAccountId: string;
    chatKey: string;
    chatEnabled: boolean;
    recaptchaSiteKey: string;
    homepageSiteMeta: {
      id: string;
      title: string;
      urlPattern: string;
      description: string;
      keywords: string;
    };
    captchaConfig: CaptchaConfig[];
    helpDesk: {
      edges: HelpDeskNode[];
    };
    languages: Array<string>;
  };
}

export interface SecureConfigs {
  cameraTagAppId: string;
  imglyLicense: string;
}

// slider config data

export interface BrandImageGallery {
  node: {
    image: string;
  };
}

export interface BrandStoreLocations {
  edges: {
    node: {
      contactNumber: string;
      address: string;
      city: {
        name: string;
      };
    };
  }[];
}

export interface BrandRedeemables {
  edges: {
    node: {
      brand: {
        name: string;
        logoImageData: string;
        redemptionType?: string;
        redemptionBadges?: {
          label: string;
          type: string;
        }[];
      };
    };
  }[];
}

export interface Brand {
  brandImageData: string;
  logoImageData: string;
  buyForYourself: boolean;
  renderPreviewPage: boolean;
  currencyDecimalNotation?: number;
  company: {
    code: string;
    name: string;
  };
  crossSellBrands: {
    edges: {
      node: {
        brand: {
          brandImageData: string;
          code: string;
          name: string;
          primaryCategory: {
            code: string;
            name: string;
          };
        };
      };
    }[];
  };
  denominationRange: string;
  description: string;
  expiry: string;
  id: string;
  imageGallery: {
    edges: BrandImageGallery[];
  };
  label: string;
  name: string;
  nameEn?: string;
  code?: string;
  slug?: string;
  siteMeta: {
    id: string;
    title: string;
    urlPattern: string;
    description: string;
    keywords: string;
    noIndex: boolean;
  };
  primaryCategory: {
    name: string;
    nameEn?: string;
  };
  primaryCategoryForEvent?: {
    code: string;
    name: string;
    nameEn: string;
  };
  defaultGenericOccasion?: {
    id: string;
    brand: {
      name: string;
      code: string;
    };
    edges: {
      node: {
        occasion: {
          name: string;
          code: string;
        };
      };
    }[];
  };
  redemptionDetails: string;
  redemptionType: string;
  redemptionTypeForEvent?: string;
  redemptionLabel?: string;
  redemptionBadges?: {
    label: string;
    type: string;
  }[];
  store: {
    country: {
      flagImage: string;
    };
  };
  website: string;
  variableDenomination?: boolean;
  storeLocations?: BrandStoreLocations;
  currency?: {
    name: string;
    code: string;
  };
  maxDenominationAmount?: number;
  minDenominationAmount?: number;
  redeemableBrands?: BrandRedeemables;
  isOffer?: boolean;
  hasOffer?: boolean;
  status?: string;
  message?: string;
  isPrintathomeEnabled?: boolean;
  requireMobileVerification?: boolean;
}

export interface SliderBrandInterface {
  node: {
    brandData: {
      brand: Brand;
      orderNumber: string;
    };
  };
}

export interface SliderCategoriesInterface {
  node: {
    code: string;
    iconImage: string;
    name: string;
    nameEn?: string;
    title: string;
    titleEn?: string;
    seoName: string;
    tagType: string;
    heading?: string;
    image?: string;
    imageWebp?: string;
    url?: string;
  };
}
export interface SliderInterface {
  ctaText: string;
  ctaTextAr: string;
  enableCta: boolean;

  heading: string;
  sliderBrand: {
    edges: SliderBrandInterface[];
  };
  sliderCategories: {
    edges: SliderCategoriesInterface[];
  };
  sliderType: string;
  category: {
    seoName: string;
  };
  isScrollable: boolean;
  redemptionBadges: { type: string; label: string }[];
  slug: string;
}

// Home main data interface
export interface HomeDataInterface {
  widgetOrder: {
    widget: "BLOG" | "REWARDS_BANNER" | "PROMOTIONAL_BANNER" | "DOWNLOAD_APP";
  }[];
  sliders: SliderInterface[];
  banners: BannerItems[];
  blogs: { edges: BlogsItemInterface[] };
  downloadApp: [
    {
      appBanner: AppBannerInterface[];
      bannerImage: string;
      text: {
        edges: DownloadTextNodeInterface[];
      };
      title: string;
    }
  ];
  happyCard: HappyCard;
  pressRooms: PressRoomItemInterface[];
  promotion: PromotionDataNode;
  testimonials: {
    edges: {
      node: {
        heading: string;
        testimonialVideo: TestimonialVideo;
        testimonialReview: TestimonialReview;
      };
    }[];
  };
  searchTags: SearchTags[];
}

export interface AboutDataInterface {
  headers: headerInterface;
  footer: FooterItemInterface;
  siteConfigs: {
    edges: SiteConfigNode[];
  };
  stores: {
    edges: StoresItem[];
  };
}

export interface UpcomingOccasions {
  occasionName: string;
  message: string;
  seoName: string;
  isToday: boolean;
}

export interface BrandFilterData {
  code?: string;
  name: string;
  nameEn?: string;
  seoName: string;
  iconImage: string;
}

export interface BrandFilters {
  data: {
    categories: BrandFilterData[];
    occasions: BrandFilterData[];
  };
}

export interface AllBrandsInterface {
  upcomingOccasions: UpcomingOccasions[];
  brandFilters: BrandFilters;
  productCatalogs: BrandsByCategory | BrandsByOccasion;
  banners: BannerItems[];
  howToUses: HowToUseData;
}

// quickview
export interface CrossSellBrandsItem {
  node: {
    brand: {
      code: string;
      name: string;
      id: string;
      slug: string;
      primaryCategory: {
        code: string;
        name: string;
      };
      brandImageData: string;
    };
  };
}
export interface QuickviewInterface {
  brand: {
    redeemAt: boolean;
    primaryCategory: {
      code: string;
      name: string;
      nameEn?: string;
    };
    brandImageData: string;
    imageGallery: {
      edges: {
        node: {
          image: string;
          caption: string;
        };
      }[];
    };
    code: string;
    name: string;
    nameEn?: string;
    slug?: string;
    logoImageData: string;
    company: {
      code: string;
      name: string;
    };
    denominationRange: string;
    storeLocations: {
      edges: [];
    };
    description: string;
    redemptionType: string;
    redemptionDetails: string[];
    store: {
      country: {
        name: string;
        flagImage: string;
      };
    };
    expiry: string;
    label: string;
  };
  crossSellBrandConfig: [
    {
      crossSellBrands: {
        totalCount: number;
        edges: CrossSellBrandsItem[];
      };
    }
  ];
}

export interface QuickviewSlider {
  brandSlug: string | undefined;
  brandLabel: string | undefined;
  denominationRange: string | undefined;
  brandImageData: string | undefined;
  imageGallery:
  | { edges: { node: { image: string; caption: string } }[] }
  | undefined;
}

export interface QuickViewProductInfoInterface {
  redeemAt: boolean | undefined;
  brandName: string | undefined;
  country:
  | {
    name: string;
    flagImage: string;
  }
  | undefined;
  expiry: string | undefined;
  type: string | undefined;
}

export interface HowToUseData {
  edges: {
    node: {
      title: string;
      howToUseBanner: {
        edges: {
          node: {
            id: string;
            bannerImage: string;
            bannerImageWebp?: string;
          };
        }[];
      };
    };
  }[];
}

export interface HowToUseInterface {
  howToUses: HowToUseData;
}

export interface ProductFeedbackInterface {
  productFeedbackBox: {
    productFeedbackBox: {
      emailAddress: string;
      brandName: string;
    };
  };
}

export interface BrandsByCategory {
  data: {
    brandsByCategory: {
      totalCount: number;
      edges: {
        node: {
          brand: SliderBrandInterface;
        };
      }[];
      categoryMessage: string;
    };
    recommendedGiftCards: RecommendedGiftCards[];
  };
}

export interface BrandsByOccasion {
  data: {
    brandsByOccasion: {
      totalCount: number;
      edges: {
        node: {
          brand: SliderBrandInterface;
        };
      }[];
      occasionMessage: string;
    };
    recommendedGiftCards: RecommendedGiftCards[];
  };
}

export interface RecommendedGiftCards {
  brand: Brand;
}

export interface CommonDataInterface {
  data: {
    footer: FooterItemInterface;
    headers: headerInterface;
    siteConfigs: {
      edges: SiteConfigNode[];
    };
    stores: {
      edges: StoresItem[];
    };
  };
}

export interface WidgetOrderInterface {
  data: {
    widgetOrder: {
      widget: "BLOG" | "REWARDS_BANNER" | "PROMOTIONAL_BANNER" | "DOWNLOAD_APP";
    }[];
  };
}
export interface SearchAutocompleteOption {
  name: string;
  slug: string;
  highlight: string;
}

export interface SearchTags {
  brand: {
    name: string;
    slug: string;
  };
}

export interface NotifierInterface {
  title: string;
  description: string | unknown;
  icon?: string;
  autoHideDuration?: number;
  onClose?: () => void;
}

export interface ThemeSettingsInterface {
  snowFlakeCount: number;
  speedMin: number;
  speedMax: number;
  windMax: number;
  windMin: number;
  radiusMax: number;
  radiusMin: number;
  useImage: boolean;
  image: string;
  rotationSpeedMin: number;
  rotationSpeedMax: number;
  color: string;
}

export interface ThemeConfigInterface {
  themeConfig: ThemeSettingsInterface[];
}

export interface BrandPromotion {
  bannerDescription: string;
  bannerImage: string;
  bannerImageWebp?: string;
  bannerRedirectUrl: string;
  bannerTitle: string;
  bannerType: string;
  bannerUrlText: string;
  purchaseMode: string;
  id: string;
  store: any;
  brand: string | null;
  brandOffer: BrandOffer | null;
  brandName: string;
  brandCode: string;
}

export interface BrandOffer {
  offerText: string;
  brand: {
    name: string;
    logoImageData: string;
  };
}

export interface CommunicationConfigsInterface {
  data: {
    communicationConfigs: CommunicationConfigs[];
  };
}

export interface CommunicationConfigs {
  resendDeliveryEnabled: boolean;
}
export interface GiftCardEditorInterface {
  occasion: string;
  color: string;
  handleOccasionChange: (e: any) => void;
  logoName: any;
  logoSize: string;
  bgImageName: any;
  bgImageSize: string;
  handleDelete: (type: any) => void;
  handleFileChange: (file: File, type: "logo" | "background") => void;
  logoErrMessage: string;
  bgErrMessage: string;
  setColor: (color: string) => void;
  isCustomCard: boolean;
  logo: any;
  background: any;
  cardConfig: any;
  siteConfig: any;
  setLogoErrMessage: any;
  setBgErrMessage: any;
  occasionErr: string;
  onCancel: (targetId: string) => void | any;
  currentComponentId?: any;
  setCardPage?: any;
  isCustomHYCEdit?: boolean;
}

export interface WorkComponentProps {
  onContinue: any;
  onCancel: (targetId: string) => void | any;
  componentId: string;
  localeWithRegion?: string;
}

import {
  GREETING_COVER_TYPE,
  PERSONAL_TITLE,
  CARD_PURCHASE_TYPE,
  CARD_DELIVERY_TYPE,
  BRAND_FORM_STATE,
  BRAND_STEPPER,
  BRAND_STEPPER_STATE,
  WORK_STEPPER_STATE,
  WORK_STEPPER,
} from "@constants/common";

// #. Greeting cover context
export interface BrandPurchaseGreetingCover {
  occasion: any;
  coverType: GREETING_COVER_TYPE;
  language: string;
  filePath: string;
  referenceCode: string;
  staticGifPath: string;
}

export interface BrandPurchaseSendToPersonInfo {
  title: PERSONAL_TITLE;
  name: string;
  email: string;
  phoneCode: string;
  phoneNumber: string;
  isValidPhoneNumber: boolean;
  isValidEmail: boolean;
}

// #. Main model of the purchase context
export interface BrandPurchaseContext {
  referenceId: string;
  brandCode: string;
  brandName: string;
  brandImage: string;
  brandSlug: string;
  cardValue: number;
  isInvalidCardValue: boolean;
  currencyCode: string;
  purchaseType: CARD_PURCHASE_TYPE;
  printablePDFSelected?: boolean;
  quantity?: number;
  greetingCover?: BrandPurchaseGreetingCover;
  personalisation?: {
    video: {
      uuid: string;
      medias: {
        filimstrip: string;
        mp4: string;
        thumb: string;
      };
    };
    photo: {
      blob: any;
      file: any;
      fileName: string;
      prevFile?: any;
      mimeType?: string;
    };
    gif: {
      url: string;
      id: string;
    };
  };
  personalisationImgToDelete: string;
  personalisationVideoToDelete: string;
  giftMessage?: {
    message: string;
    backgroundColor: string;
    fontFamily: string;
    fontSize: number;
    isDirty: boolean;
  };
  sendToPerson?: BrandPurchaseSendToPersonInfo;
  recipientDetails: RecipientDetailsInterface[];
  senderName?: string;
  delivery?: {
    type: CARD_DELIVERY_TYPE;
    dateType: string;
    date?: Date;
    time?: Date;
    timezone?: string;
  };
  availableStock: number;
  outOfStock: { yes: boolean; amount: number };
  isDirty: boolean;
  formState: BRAND_FORM_STATE;
  storiesCount: number;
  nonModulusBrandValues: Array<number>;
  categoryName: string;
  brandSkinUrl: string;
  redemptionType: string;
  greetingsHubOccasionName: string;
  greetingsHubOccasionCode: string;
  brandInfo: {
    brandImage: string;
    brandSlug: string;
  }
}

export interface BrandSessionSave {
  data: BrandPurchaseContext | undefined;
  clickMode: number;
}

export interface BrandStepper {
  activeStep: BRAND_STEPPER;
  openStep: BRAND_STEPPER;
  [key: number]: {
    stepState: BRAND_STEPPER_STATE;
  };
}

export interface BrandWorkStepper {
  activeStep: WORK_STEPPER;
  openStep: WORK_STEPPER;
  [key: number]: {
    stepState: WORK_STEPPER_STATE;
  };
}

export interface RecipientDetailsInterface {
  index: any;
  recipientName: string;
  amount: number | string;
  currency: string;
  sendAs: "Email" | "SMS";
  whatsappDelivery: boolean;
  recipientEmail: string;
  countryCode: string;
  dialCode: string;
  recipientMobile: string;
  date: any;
  senderName: string;
  referenceId?: string
  deliveryLanguage: string
}

export interface RecipientPdf {
  id: number;
  number: number;
  amount: string;
  referenceId?: string
}

export interface CustomCard {
  logo: File,
  name: string,
  background: File,
  bgColor: string,
  occasion: string,
  logoPreview: string,
  bgPreview: string,
  cardImage: string,
  referenceId: string,
}

//#. Denominations data interface
export interface BrandDenominations {
  brandDenominations: {
    edges: {
      node: {
        amount: number;
        isDefault: boolean;
      };
    }[];
  };
}

// #. Page props interface
export interface BrandValueInterface {
  slug: string | undefined;
  brandCode: string | undefined;
  brandName: string | undefined;
  currencyCode: string | undefined;
  variableDenomination?: boolean | undefined;
  maxDenominationAmount?: number | undefined;
  minDenominationAmount?: number | undefined;
  currencyDecimalNotation?: number | undefined;
  isUserAuthorized?: boolean;
  renderPreviewPage?: boolean | undefined;
  fromOfferPage?: boolean;
  isInvalidAmount: boolean;
  setIsInvalidAmount: any;
  amountValue: any;
  setAmountValue: any;

}