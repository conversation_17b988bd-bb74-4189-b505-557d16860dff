import React from 'react';

export const UnderLineIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 13.095a.71.71 0 0 1-.71.71H5.71a.71.71 0 1 1 0-1.42h7.58a.71.71 0 0 1 .71.71zm-4.5-1.658a4.03 4.03 0 0 0 4.026-4.026V3.148a.71.71 0 1 0-1.42 0v4.263a2.605 2.605 0 0 1-5.211 0V3.148a.71.71 0 1 0-1.421 0v4.263A4.03 4.03 0 0 0 9.5 11.438z"
      fill="#667085"
    />
  </svg>
);

export const ItalicIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 3.79c0 .209-.075.41-.208.558a.677.677 0 0 1-.502.23h-1.857l-2.368 7.896h1.382c.189 0 .37.083.503.23a.837.837 0 0 1 .208.56c0 .209-.075.41-.208.557a.676.676 0 0 1-.503.232H5.711a.676.676 0 0 1-.503-.232.836.836 0 0 1-.208-.558c0-.21.075-.41.208-.558a.677.677 0 0 1 .503-.231h1.856l2.368-7.895H8.553a.677.677 0 0 1-.503-.231.836.836 0 0 1-.208-.559c0-.209.075-.41.208-.558A.677.677 0 0 1 8.553 3h4.736c.189 0 .37.083.503.231A.836.836 0 0 1 14 3.79z"
      fill="#667085"
    />
  </svg>
);

export const BoldIcon = () => (
  <svg
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.319 7.904A3 3 0 0 0 10 3H5.75A.75.75 0 0 0 5 3.75v9.502a.75.75 0 0 0 .75.75h5a3.25 3.25 0 0 0 1.569-6.098zM6.5 4.5H10a1.5 1.5 0 0 1 0 3H6.5v-3zm4.25 8.001H6.5v-3.5h4.25a1.75 1.75 0 1 1 0 3.5z"
      fill="#667085"
    />
  </svg>
);

export const RightAlignLogo = () => (
  <svg
    id="rightAlign"
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.329 3.831h6.333m-6.333 8.333h6.333M3.995 7.998h9.667"
      stroke="#667085"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const CenterAlignLogo = () => (
  <svg
    id="centerAlign"
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.995 3.831h5.667m-5.667 8.333h5.667M3.995 7.998h9.667"
      stroke="#667085"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const LeftAlignLogo = () => (
  <svg
    id="leftAlign"
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.995 3.831h6.334m-6.334 8.333h6.334M3.995 7.998h9.667"
      stroke="#667085"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const ImageCropSelect = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path fill="#fff" d="M0 0h20v20H0z" />
    <circle cx="12" cy="10" r="9" fill="#0071FF" />
    <path
      d="M10.817 12.983a.624.624 0 0 1-.442-.183l-2.358-2.358a.629.629 0 0 1 0-.884.629.629 0 0 1 .883 0l1.917 1.917L15.1 7.192a.629.629 0 0 1 .883 0 .629.629 0 0 1 0 .883L11.258 12.8a.624.624 0 0 1-.441.183z"
      fill="#fff"
    />
  </svg>
);

export const UploadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.5 10.19H17.61C15.24 10.19 13.31 8.26 13.31 5.89V3C13.31 2.45 12.86 2 12.31 2H8.07C4.99 2 2.5 4 2.5 7.57V16.43C2.5 20 4.99 22 8.07 22H15.93C19.01 22 21.5 20 21.5 16.43V11.19C21.5 10.64 21.05 10.19 20.5 10.19ZM11.53 13.53C11.38 13.68 11.19 13.75 11 13.75C10.81 13.75 10.62 13.68 10.47 13.53L9.75 12.81V17C9.75 17.41 9.41 17.75 9 17.75C8.59 17.75 8.25 17.41 8.25 17V12.81L7.53 13.53C7.24 13.82 6.76 13.82 6.47 13.53C6.18 13.24 6.18 12.76 6.47 12.47L8.47 10.47C8.54 10.41 8.61 10.36 8.69 10.32C8.71 10.31 8.74 10.3 8.76 10.29C8.82 10.27 8.88 10.26 8.95 10.25C8.98 10.25 9 10.25 9.03 10.25C9.11 10.25 9.19 10.27 9.27 10.3C9.28 10.3 9.28 10.3 9.29 10.3C9.37 10.33 9.45 10.39 9.51 10.45C9.52 10.46 9.53 10.46 9.53 10.47L11.53 12.47C11.82 12.76 11.82 13.24 11.53 13.53Z"
      fill="#0071FF"
    />
    <path
      d="M17.4297 8.80999C18.3797 8.81999 19.6997 8.81999 20.8297 8.81999C21.3997 8.81999 21.6997 8.14999 21.2997 7.74999C19.8597 6.29999 17.2797 3.68999 15.7997 2.20999C15.3897 1.79999 14.6797 2.07999 14.6797 2.64999V6.13999C14.6797 7.59999 15.9197 8.80999 17.4297 8.80999Z"
      fill="#0071FF"
    />
  </svg>
);

export const UploadIconWhite = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.5 10.19H17.61C15.24 10.19 13.31 8.26 13.31 5.89V3C13.31 2.45 12.86 2 12.31 2H8.07C4.99 2 2.5 4 2.5 7.57V16.43C2.5 20 4.99 22 8.07 22H15.93C19.01 22 21.5 20 21.5 16.43V11.19C21.5 10.64 21.05 10.19 20.5 10.19ZM11.53 13.53C11.38 13.68 11.19 13.75 11 13.75C10.81 13.75 10.62 13.68 10.47 13.53L9.75 12.81V17C9.75 17.41 9.41 17.75 9 17.75C8.59 17.75 8.25 17.41 8.25 17V12.81L7.53 13.53C7.24 13.82 6.76 13.82 6.47 13.53C6.18 13.24 6.18 12.76 6.47 12.47L8.47 10.47C8.54 10.41 8.61 10.36 8.69 10.32C8.71 10.31 8.74 10.3 8.76 10.29C8.82 10.27 8.88 10.26 8.95 10.25C8.98 10.25 9 10.25 9.03 10.25C9.11 10.25 9.19 10.27 9.27 10.3C9.28 10.3 9.28 10.3 9.29 10.3C9.37 10.33 9.45 10.39 9.51 10.45C9.52 10.46 9.53 10.46 9.53 10.47L11.53 12.47C11.82 12.76 11.82 13.24 11.53 13.53Z"
      fill="white"
    />
    <path
      d="M17.4297 8.80999C18.3797 8.81999 19.6997 8.81999 20.8297 8.81999C21.3997 8.81999 21.6997 8.14999 21.2997 7.74999C19.8597 6.29999 17.2797 3.68999 15.7997 2.20999C15.3897 1.79999 14.6797 2.07999 14.6797 2.64999V6.13999C14.6797 7.59999 15.9197 8.80999 17.4297 8.80999Z"
      fill="white"
    />
  </svg>
);

export const UploadIconBlack = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
  <path d="M21 10.19H18.11C15.74 10.19 13.81 8.26 13.81 5.89V3C13.81 2.45 13.36 2 12.81 2H8.57C5.49 2 3 4 3 7.57V16.43C3 20 5.49 22 8.57 22H16.43C19.51 22 22 20 22 16.43V11.19C22 10.64 21.55 10.19 21 10.19ZM12.03 13.53C11.88 13.68 11.69 13.75 11.5 13.75C11.31 13.75 11.12 13.68 10.97 13.53L10.25 12.81V17C10.25 17.41 9.91 17.75 9.5 17.75C9.09 17.75 8.75 17.41 8.75 17V12.81L8.03 13.53C7.74 13.82 7.26 13.82 6.97 13.53C6.68 13.24 6.68 12.76 6.97 12.47L8.97 10.47C9.04 10.41 9.11 10.36 9.19 10.32C9.21 10.31 9.24 10.3 9.26 10.29C9.32 10.27 9.38 10.26 9.45 10.25C9.48 10.25 9.5 10.25 9.53 10.25C9.61 10.25 9.69 10.27 9.77 10.3C9.78 10.3 9.78 10.3 9.79 10.3C9.87 10.33 9.95 10.39 10.01 10.45C10.02 10.46 10.03 10.46 10.03 10.47L12.03 12.47C12.32 12.76 12.32 13.24 12.03 13.53Z" fill="#0E0F0C"/>
  <path d="M17.9297 8.81048C18.8797 8.82048 20.1997 8.82048 21.3297 8.82048C21.8997 8.82048 22.1997 8.15048 21.7997 7.75048C20.3597 6.30048 17.7797 3.69048 16.2997 2.21048C15.8897 1.80048 15.1797 2.08048 15.1797 2.65048V6.14048C15.1797 7.60048 16.4197 8.81048 17.9297 8.81048Z" fill="#0E0F0C"/>
</svg>
);

export const CloseIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="33"
    viewBox="0 0 32 33"
    fill="none"
  >
    <path
      d="M16.0001 3.1665C8.65341 3.1665 2.66675 9.15317 2.66675 16.4998C2.66675 23.8465 8.65341 29.8332 16.0001 29.8332C23.3467 29.8332 29.3334 23.8465 29.3334 16.4998C29.3334 9.15317 23.3467 3.1665 16.0001 3.1665ZM20.4801 19.5665C20.8667 19.9532 20.8667 20.5932 20.4801 20.9798C20.2801 21.1798 20.0267 21.2732 19.7734 21.2732C19.5201 21.2732 19.2667 21.1798 19.0667 20.9798L16.0001 17.9132L12.9334 20.9798C12.7334 21.1798 12.4801 21.2732 12.2267 21.2732C11.9734 21.2732 11.7201 21.1798 11.5201 20.9798C11.1334 20.5932 11.1334 19.9532 11.5201 19.5665L14.5867 16.4998L11.5201 13.4332C11.1334 13.0465 11.1334 12.4065 11.5201 12.0198C11.9067 11.6332 12.5467 11.6332 12.9334 12.0198L16.0001 15.0865L19.0667 12.0198C19.4534 11.6332 20.0934 11.6332 20.4801 12.0198C20.8667 12.4065 20.8667 13.0465 20.4801 13.4332L17.4134 16.4998L20.4801 19.5665Z"
      fill="#292D32"
    />
  </svg>
);

export const ErrorIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M7.99992 1.3335C4.32659 1.3335 1.33325 4.32683 1.33325 8.00016C1.33325 11.6735 4.32659 14.6668 7.99992 14.6668C11.6733 14.6668 14.6666 11.6735 14.6666 8.00016C14.6666 4.32683 11.6733 1.3335 7.99992 1.3335ZM7.49992 5.3335C7.49992 5.06016 7.72659 4.8335 7.99992 4.8335C8.27325 4.8335 8.49992 5.06016 8.49992 5.3335V8.66683C8.49992 8.94016 8.27325 9.16683 7.99992 9.16683C7.72659 9.16683 7.49992 8.94016 7.49992 8.66683V5.3335ZM8.61325 10.9202C8.57992 11.0068 8.53325 11.0735 8.47325 11.1402C8.40659 11.2002 8.33325 11.2468 8.25325 11.2802C8.17325 11.3135 8.08659 11.3335 7.99992 11.3335C7.91325 11.3335 7.82659 11.3135 7.74659 11.2802C7.66659 11.2468 7.59325 11.2002 7.52659 11.1402C7.46659 11.0735 7.41992 11.0068 7.38659 10.9202C7.35325 10.8402 7.33325 10.7535 7.33325 10.6668C7.33325 10.5802 7.35325 10.4935 7.38659 10.4135C7.41992 10.3335 7.46659 10.2602 7.52659 10.1935C7.59325 10.1335 7.66659 10.0868 7.74659 10.0535C7.90659 9.98683 8.09325 9.98683 8.25325 10.0535C8.33325 10.0868 8.40659 10.1335 8.47325 10.1935C8.53325 10.2602 8.57992 10.3335 8.61325 10.4135C8.64659 10.4935 8.66659 10.5802 8.66659 10.6668C8.66659 10.7535 8.64659 10.8402 8.61325 10.9202Z" fill="#E74848"/>
  </svg>
);

export const GalleryIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
  <path d="M21.22 1H18.28C17.01 1 16.25 1.76 16.25 3.03V5.97C16.25 7.24 17.01 8 18.28 8H21.22C22.49 8 23.25 7.24 23.25 5.97V3.03C23.25 1.76 22.49 1 21.22 1ZM21.44 4.31C21.32 4.43 21.16 4.49 21 4.49C20.84 4.49 20.68 4.43 20.56 4.31L20.38 4.13V6.37C20.38 6.72 20.1 7 19.75 7C19.4 7 19.12 6.72 19.12 6.37V4.13L18.94 4.31C18.7 4.55 18.3 4.55 18.06 4.31C17.82 4.07 17.82 3.67 18.06 3.43L19.31 2.18C19.36 2.13 19.43 2.09 19.5 2.06C19.52 2.05 19.54 2.05 19.56 2.04C19.61 2.02 19.66 2.01 19.72 2.01C19.74 2.01 19.76 2.01 19.78 2.01C19.85 2.01 19.91 2.02 19.98 2.05C19.99 2.05 19.99 2.05 20 2.05C20.07 2.08 20.13 2.12 20.18 2.17C20.19 2.18 20.19 2.18 20.2 2.18L21.45 3.43C21.69 3.67 21.69 4.07 21.44 4.31Z" fill="#292D32"/>
  <path d="M9.24914 10.3801C10.5636 10.3801 11.6291 9.31456 11.6291 8.00012C11.6291 6.68568 10.5636 5.62012 9.24914 5.62012C7.9347 5.62012 6.86914 6.68568 6.86914 8.00012C6.86914 9.31456 7.9347 10.3801 9.24914 10.3801Z" fill="#292D32"/>
  <path d="M21.22 8H20.75V12.61L20.62 12.5C19.84 11.83 18.58 11.83 17.8 12.5L13.64 16.07C12.86 16.74 11.6 16.74 10.82 16.07L10.48 15.79C9.77 15.17 8.64 15.11 7.84 15.65L4.1 18.16C3.88 17.6 3.75 16.95 3.75 16.19V7.81C3.75 4.99 5.24 3.5 8.06 3.5H16.25V3.03C16.25 2.63 16.32 2.29 16.48 2H8.06C4.42 2 2.25 4.17 2.25 7.81V16.19C2.25 17.28 2.44 18.23 2.81 19.03C3.67 20.93 5.51 22 8.06 22H16.44C20.08 22 22.25 19.83 22.25 16.19V7.77C21.96 7.93 21.62 8 21.22 8Z" fill="#292D32"/>
</svg>
);

export const AtWorkProWaring = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path d="M21.7585 15.92L15.3585 4.4C14.4985 2.85 13.3085 2 11.9985 2C10.6885 2 9.49851 2.85 8.63851 4.4L2.23851 15.92C1.42851 17.39 1.33851 18.8 1.98851 19.91C2.63851 21.02 3.91851 21.63 5.59851 21.63H18.3985C20.0785 21.63 21.3585 21.02 22.0085 19.91C22.6585 18.8 22.5685 17.38 21.7585 15.92ZM11.2485 9C11.2485 8.59 11.5885 8.25 11.9985 8.25C12.4085 8.25 12.7485 8.59 12.7485 9V14C12.7485 14.41 12.4085 14.75 11.9985 14.75C11.5885 14.75 11.2485 14.41 11.2485 14V9ZM12.7085 17.71C12.6585 17.75 12.6085 17.79 12.5585 17.83C12.4985 17.87 12.4385 17.9 12.3785 17.92C12.3185 17.95 12.2585 17.97 12.1885 17.98C12.1285 17.99 12.0585 18 11.9985 18C11.9385 18 11.8685 17.99 11.7985 17.98C11.7385 17.97 11.6785 17.95 11.6185 17.92C11.5585 17.9 11.4985 17.87 11.4385 17.83C11.3885 17.79 11.3385 17.75 11.2885 17.71C11.1085 17.52 10.9985 17.26 10.9985 17C10.9985 16.74 11.1085 16.48 11.2885 16.29C11.3385 16.25 11.3885 16.21 11.4385 16.17C11.4985 16.13 11.5585 16.1 11.6185 16.08C11.6785 16.05 11.7385 16.03 11.7985 16.02C11.9285 15.99 12.0685 15.99 12.1885 16.02C12.2585 16.03 12.3185 16.05 12.3785 16.08C12.4385 16.1 12.4985 16.13 12.5585 16.17C12.6085 16.21 12.6585 16.25 12.7085 16.29C12.8885 16.48 12.9985 16.74 12.9985 17C12.9985 17.26 12.8885 17.52 12.7085 17.71Z" fill="#E74848"/>
</svg>
);

export const CheckCircle = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M7.9987 1.33301C4.32536 1.33301 1.33203 4.32634 1.33203 7.99967C1.33203 11.673 4.32536 14.6663 7.9987 14.6663C11.672 14.6663 14.6654 11.673 14.6654 7.99967C14.6654 4.32634 11.672 1.33301 7.9987 1.33301ZM11.1854 6.46634L7.40536 10.2463C7.31203 10.3397 7.18536 10.393 7.05203 10.393C6.9187 10.393 6.79203 10.3397 6.6987 10.2463L4.81203 8.35967C4.6187 8.16634 4.6187 7.84634 4.81203 7.65301C5.00536 7.45967 5.32536 7.45967 5.5187 7.65301L7.05203 9.18634L10.4787 5.75968C10.672 5.56634 10.992 5.56634 11.1854 5.75968C11.3787 5.95301 11.3787 6.26634 11.1854 6.46634Z" fill="#0071FF"/>
  </svg>
);
